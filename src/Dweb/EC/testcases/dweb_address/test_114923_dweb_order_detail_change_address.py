"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_114923_dweb_order_detail_change_address.py
@Description    :  订单详情页修改地址功能测试
@CreateTime     :  2025/7/30 13:44
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/7/30 13:44
"""
import allure
import pytest
from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_category import dweb_category_ele
from src.Dweb.EC.dweb_pages.dweb_page_address.dweb_page_address import DWebAddressPage
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_cart import DWebCartPage
from src.Dweb.EC.dweb_pages.dweb_page_category.dweb_page_category import DWebCategorypage
from src.Dweb.EC.dweb_pages.page_checkout.page_checkout import CheckoutPage
from src.api.zipcode import switch_zipcode
from src.common.commfunc import empty_cart
from src.config.weee.log_help import log
from src.config.base_config import TEST_URL

class TestDwebOrderDetailChangeAddress:
    pytestmark = [pytest.mark.todo,pytest.mark.zhuli]

    @allure.title("PC端订单详情页修改地址功能测试")
    def test_114923_dweb_order_detail_change_address(self,page: dict, pc_autotest_header, login_trace):
        """
        订单详情页修改地址功能测试
        """
        p: Page = page.get("page")
        c = page.get("context")
        switch_zipcode(pc_autotest_header, '94538')
        p.wait_for_timeout(2000)
        # 2.清除购物车
        empty_cart(pc_autotest_header)

        # 2. 从分类页加购生鲜商品
        with (allure.step("从分类页加购生鲜商品")):
            # 构造的分类页面
            category_page = DWebCategorypage(p, pc_autotest_header, browser_context=c, page_url="/category/sale")
            # 去分类页加购Local类型的商品进购物车
            local_filter_id = dweb_category_ele.ele_local_delivery_test_id

            # 调用封装方法加购Local Delivery商品
            added_count = category_page.add_products_from_home_by_filter(
                filter_name="Local Delivery",
                filter_id=local_filter_id,
                count=3,  # 加购2个商品
            )
            p.wait_for_timeout(5000)  # 增加等待时间
            log.info(f"成功加购{added_count}个商品")
            assert added_count > 0, "未能成功加购商品"

        # 4. 点击结算按钮进入结算页
        with allure.step("点击结算按钮进入结算页"):
            # 构造的购物车页面
            DWebCartPage(p, pc_autotest_header, browser_context=c, page_url="/cart")
            p.wait_for_timeout(2000)
            # 点击结算按钮
            checkout_button = p.get_by_test_id("wid-cart-summary-checkout")
            assert checkout_button.is_visible(), "结算按钮不可见"
            checkout_button.click()
            log.info("成功点击结算按钮")
            p.wait_for_timeout(3000)
            upsell_button = p.get_by_test_id("wid-upsell-continue-to-checkout-btn")
            if upsell_button.is_visible(timeout=3000):
                log.info("检测到upsell弹窗，点击继续结算按钮")
                upsell_button.click()
                p.wait_for_timeout(3000)
            else:
                log.info("未检测到upsell弹窗，直接进行下")
        p.wait_for_timeout(2000)

        # 直接调用积分支付方法，不创建实例避免页面刷新
        fresh_order_id = CheckoutPage.submit_order_with_points_on_checkout_page(None, p)

        p.wait_for_timeout(2000)
        address_page = DWebAddressPage(p, pc_autotest_header, c, page_url=f'/account/address/order/{fresh_order_id}')

        # 验证生鲜订单显示修改地址入口
        assert address_page.verify_fresh_order_change_address_entry(fresh_order_id)

        # 验证跨区域修改地址提示错误
        assert address_page.verify_cross_region_address_change_error(fresh_order_id)

        log.info("订单详情页修改地址功能验证完成")
    

    

