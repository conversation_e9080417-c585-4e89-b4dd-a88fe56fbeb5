{"name": "【PC-PDP】活动弹窗详细内容UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Unsupported token \"@data-testid\" while parsing selector \"div[contains(@data-testid,'wid-product-card-item-')]\"", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pdp_promotion_ui_ux.TestDWebPDPPromotionDrawerUIUX object at 0x00000205F08C0D50>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc....28>>, 'page': <Page url='https://www.sayweee.com/en/product/Synear-Shrimp-Pork-and-Scallop-Dumplings-Frozen-1/23847'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...Db_0pdUVMLxg2rMrH3DXQLK43mdXF0sHosL1EPaEYVL-kHETU3lbkhegb-lKyslI1iJQpUHeheQnsL-GTvqmggvDtegd-XvZCdRn7v3UFINXDK0w', ...}\nlogin_trace = None\n\n    @allure.title(\"【PC-PDP】活动弹窗详细内容UI/UX验证\")\n    def test_dweb_pdp_promotion_drawer_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        PC-PDP活动弹窗详细内容UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 检查是否存在活动模块\n        3. 点击查看更多打开活动弹窗\n        4. 验证活动名称和描述\n        5. 验证商品展示（价格、图片、标题）\n        6. 检查价格过滤器是否存在\n        7. 如果存在过滤器，测试过滤功能\n        8. 如果不存在过滤器，直接测试加购功能\n        9. 验证活动进度信息\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 直接进入指定pdp页面\n        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,\n                               page_url=\"/product/Synear-Shrimp-Pork-and-Scallop-Dumplings-Frozen-1/23847\")\n    \n        p.wait_for_timeout(5000)\n    \n        # 2. 检查活动模块是否存在\n        promotions_module = p.locator(\"div[data-testid='wid-pdp-product-promotions']\")\n    \n        if not promotions_module.is_visible():\n            print(\"商品页面不存在活动模块，跳过活动弹窗验证\")\n            pytest.skip(\"商品页面不存在活动模块\")\n            return\n    \n        print(\"发现商品活动模块，开始验证活动弹窗内容\")\n    \n        # 3. 点击查看更多按钮打开活动弹窗\n        promotion_button = p.locator(\"div[data-testid='btn-pdp-product-promotion']\")\n        assert promotion_button.is_visible(), \"查看更多按钮不可见\"\n    \n        promotion_button.click()\n        p.wait_for_timeout(2000)\n    \n        # 验证活动弹窗出现\n        promotion_drawer = p.locator(\"div[data-testid='wid-promotion-drawer-wrapper']\")\n        assert promotion_drawer.is_visible(), \"活动弹窗未出现\"\n    \n        print(\"活动弹窗已打开\")\n    \n        # 4. 验证活动名称和描述\n        self._verify_promotion_content(p)\n    \n        # 5. 验证商品展示\n>       self._verify_product_display(p)\n\ntest_110551_dweb_pdp_promotion_ui_ux.py:64: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\ntest_110551_dweb_pdp_promotion_ui_ux.py:121: in _verify_product_display\n    product_cards = p.locator(\"div[contains(@data-testid,'wid-product-card-item-')]\").all()\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17056: in all\n    return mapping.from_impl_list(self._sync(self._impl_obj.all()))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:384: in all\n    for index in range(await self.count()):\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:391: in count\n    return await self._frame._query_count(self._selector)\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:122: in _query_count\n    return await self._channel.send(\"queryCount\", {\"selector\": selector})\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x00000205F0C8A690>\nmethod = 'queryCount'\nparams = {'selector': \"div[contains(@data-testid,'wid-product-card-item-')]\"}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: Unsupported token \"@data-testid\" while parsing selector \"div[contains(@data-testid,'wid-product-card-item-')]\"\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        PC-PDP活动弹窗详细内容UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 检查是否存在活动模块\n        3. 点击查看更多打开活动弹窗\n        4. 验证活动名称和描述\n        5. 验证商品展示（价格、图片、标题）\n        6. 检查价格过滤器是否存在\n        7. 如果存在过滤器，测试过滤功能\n        8. 如果不存在过滤器，直接测试加购功能\n        9. 验证活动进度信息\n        ", "start": 1754650225721, "stop": 1754650242286, "uuid": "42c6bc65-3830-4a71-b922-98598eef0d4f", "historyId": "ba9fefcd295d2faf82ffffa491f944b0", "testCaseId": "ba9fefcd295d2faf82ffffa491f944b0", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pdp_promotion_ui_ux.TestDWebPDPPromotionDrawerUIUX#test_dweb_pdp_promotion_drawer_ui_ux", "labels": [{"name": "story", "value": "【PC-PDP】活动弹窗详细内容UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_110551_dweb_pdp_promotion_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPPromotionDrawerUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "38468-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pdp_promotion_ui_ux"}]}