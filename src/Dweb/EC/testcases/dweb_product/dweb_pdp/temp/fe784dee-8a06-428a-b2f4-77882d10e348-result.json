{"name": "[111284][dweb]promotion-pc端pdp活动页流程验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Error: strict mode violation: get_by_test_id(\"wid-pdp-product-promotion-icon\") resolved to 2 elements:\n    1) <img class=\"\" alt=\"weee\" data-testid=\"wid-pdp-product…/> aka get_by_test_id(\"wid-pdp-product-promotion-icon\").first\n    2) <img class=\"\" alt=\"weee\" data-testid=\"wid-pdp-product…/> aka get_by_test_id(\"wid-pdp-product-promotion-icon\").nth(1)", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_111284_dweb_pdp_promotion_pop.TestDWebPDPProductPromotionsPopUIUX object at 0x0000018DEC023750>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc...28>>, 'page': <Page url='https://www.sayweee.com/en/product/Ka<PERSON>-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-Bubble-Hair-Color--Natural-Black/16772'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...318quwMImcRelCxpwcsh1st6wevxDROOuQVfqRYTezvAS6Re_glP8PTLDuMNDcxeUKmOsD_djlTcN8jgTJysnmgT15kBNeFHrv0ukifSiOopg5dk', ...}\nlogin_trace = None\n\n    @allure.title(\"[111284][dweb]promotion-pc端pdp活动页流程验证\")\n    def test_111284_dweb_pdp_promotions_pop_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        PC-PDP商品活动模块UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 检查是否存在活动模块\n        4. 如果存在活动，校验活动元素\n        5. 验证活动图标、标题、描述\n        6. 测试查看更多按钮功能\n        7. 验证活动弹窗显示和关闭\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 直接进入指定pdp页面\n        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,\n                               page_url=\"/product/Kao-Liese-Prettia-Bubble-Hair-Color--Natural-Black/16772\")\n    \n        p.wait_for_timeout(5000)\n    \n        # 2. 检查活动模块是否存在\n        # 等待页面完全加载\n    \n        zipcode = p.get_by_test_id('wid-modal-zip-code').text_content()\n        if zipcode != '99991':\n            switch_zipcode(headers=pc_autotest_header,zipcode=\"99991\")\n            p.reload()\n        p.wait_for_load_state(\"networkidle\", timeout=30000)\n    \n        promotions_module = p.get_by_test_id(\"wid-pdp-product-promotions\")\n    \n        if promotions_module.count() == 0 or not promotions_module.is_visible():\n            print(\"商品页面不存在活动模块，跳过活动验证\")\n            print(f\"当前页面URL: {p.url}\")\n            pytest.skip(\"商品页面不存在活动模块\")\n            return\n    \n        print(\"发现商品活动模块，开始验证活动内容\")\n    \n        # 3. 校验活动模块存在\n        assert promotions_module.is_visible(), \"活动模块不可见\"\n    \n        # 4. 校验单个活动项\n        promotion_item = p.get_by_test_id(\"wid-pdp-product-promotion\").first\n        assert promotion_item.count() > 0, \"活动项元素不存在\"\n        assert promotion_item.is_visible(), \"活动项不可见\"\n    \n        # 验证活动项的样式\n        item_classes = promotion_item.get_attribute(\"class\")\n        assert \"flex items-center justify-between py-2.5\" in item_classes, f\"活动项样式不正确: {item_classes}\"\n    \n        # 5. 校验活动图标\n        promotion_icon = p.get_by_test_id(\"wid-pdp-product-promotion-icon\")\n        assert promotion_icon.count() > 0, \"活动图标元素不存在\"\n>       assert promotion_icon.is_visible(), \"活动图标不可见\"\n\ntest_111284_dweb_pdp_promotion_pop.py:70: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17546: in is_visible\n    self._sync(self._impl_obj.is_visible(timeout=timeout))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:499: in is_visible\n    return await self._frame.is_visible(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:348: in is_visible\n    return await self._channel.send(\"isVisible\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x0000018DEE77FB10>\nmethod = 'isVisible'\nparams = {'selector': 'internal:testid=[data-testid=\"wid-pdp-product-promotion-icon\"s]', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: Error: strict mode violation: get_by_test_id(\"wid-pdp-product-promotion-icon\") resolved to 2 elements:\nE           1) <img class=\"\" alt=\"weee\" data-testid=\"wid-pdp-product…/> aka get_by_test_id(\"wid-pdp-product-promotion-icon\").first\nE           2) <img class=\"\" alt=\"weee\" data-testid=\"wid-pdp-product…/> aka get_by_test_id(\"wid-pdp-product-promotion-icon\").nth(1)\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        PC-PDP商品活动模块UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 检查是否存在活动模块\n        4. 如果存在活动，校验活动元素\n        5. 验证活动图标、标题、描述\n        6. 测试查看更多按钮功能\n        7. 验证活动弹窗显示和关闭\n        ", "start": 1757743021195, "stop": 1757743040653, "uuid": "b55580b8-9a16-4a7e-acb4-6ca9a23117fc", "historyId": "89604bea0eb06608cba0742324a6255a", "testCaseId": "89604bea0eb06608cba0742324a6255a", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_111284_dweb_pdp_promotion_pop.TestDWebPDPProductPromotionsPopUIUX#test_111284_dweb_pdp_promotions_pop_ui_ux", "labels": [{"name": "story", "value": "[111284][dweb]promotion-pc端pdp活动页流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_111284_dweb_pdp_promotion_pop"}, {"name": "subSuite", "value": "TestDWebPDPProductPromotionsPopUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "25112-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_111284_dweb_pdp_promotion_pop"}]}