{"name": "[110551][dweb]promotion-活动tag详情页价格显示及筛选流程验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_promotion_filter_ui_ux.TestDWebPDPPromotionFilterUIUX object at 0x00000169DD7D9450>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc...28>>, 'page': <Page url='https://www.sayweee.com/en/product/Ka<PERSON>-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-Bubble-Hair-Color--Natural-Black/16772'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...pNEhP7iQ8LZ696hxfVsrbt3ijXwUfaFjM_9oLcMgaxxuzfOuKNVkNxk9A5CfmedNWu6dRMkR7TJtHv73bj1GOcAbDBobu99Zse89bAj4jTXYDM_U', ...}\nlogin_trace = None\n\n    @allure.title(\"[110551][dweb]promotion-活动tag详情页价格显示及筛选流程验证\")\n    def test_110551_dweb_pdp_promotion_filter_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        PC-PDP活动弹窗价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 检查是否存在活动模块\n        3. 点击活动按钮打开活动弹窗\n        4. 验证弹窗中的商品信息\n        5. 检查是否存在价格过滤器\n        6. 测试价格过滤器功能\n        7. 验证过滤结果的准确性\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 直接进入指定pdp页面\n        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,\n                               page_url=\"/product/Kao-Liese-Prettia-Bubble-Hair-Color--Natural-Black/16772\")\n    \n        p.wait_for_timeout(5000)\n        switch_zipcode(headers=pc_autotest_header, zipcode=\"99991\")\n        # 2. 检查活动模块是否存在\n        promotions_module = p.get_by_test_id(\"wid-pdp-product-promotions\")\n    \n        if promotions_module.count() == 0 or not promotions_module.is_visible():\n            print(\"商品页面不存在活动模块，跳过活动弹窗验证\")\n            pytest.skip(\"商品页面不存在活动模块\")\n            return\n    \n        print(\"发现商品活动模块，开始验证活动弹窗内容\")\n        promotion_items = p.get_by_test_id(\"wid-pdp-product-promotion\").all()\n        assert len(promotion_items) > 0, \"活动项不存在\"\n    \n        for i, promotion_item in enumerate(promotion_items):\n            print(f\"验证第 {i + 1} 个活动...\")\n    \n            # 3. 点击活动按钮打开活动弹窗\n            promotion_button = promotion_item.get_by_test_id(\"btn-pdp-product-promotion\")\n            assert promotion_button.count() > 0, \"活动按钮不存在\"\n            assert promotion_button.is_visible(), \"活动按钮不可见\"\n    \n>           promotion_button.click()\n\ntest_110551_dweb_promotion_filter_ui_ux.py:55: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:15784: in click\n    self._sync(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:159: in click\n    return await self._frame.click(self._selector, strict=True, **params)\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:484: in click\n    await self._channel.send(\"click\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x00000169DDC0C210>\nmethod = 'click'\nparams = {'selector': 'internal:testid=[data-testid=\"wid-pdp-product-promotion\"s] >> nth=1 >> internal:testid=[data-testid=\"btn-pdp-product-promotion\"s]', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        PC-PDP活动弹窗价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 检查是否存在活动模块\n        3. 点击活动按钮打开活动弹窗\n        4. 验证弹窗中的商品信息\n        5. 检查是否存在价格过滤器\n        6. 测试价格过滤器功能\n        7. 验证过滤结果的准确性\n        ", "start": 1757747161984, "stop": 1757747234950, "uuid": "755c7c60-d7ca-4b9b-9269-ea68c10ebadf", "historyId": "5e10af71f5d1a908adc084dd76d8b20b", "testCaseId": "5e10af71f5d1a908adc084dd76d8b20b", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_promotion_filter_ui_ux.TestDWebPDPPromotionFilterUIUX#test_110551_dweb_pdp_promotion_filter_ui_ux", "labels": [{"name": "story", "value": "[110551][dweb]promotion-活动tag详情页价格显示及筛选流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_110551_dweb_promotion_filter_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPPromotionFilterUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "5412-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_promotion_filter_ui_ux"}]}