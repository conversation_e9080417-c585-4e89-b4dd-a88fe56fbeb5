{"name": "【PC-PDP】活动弹窗价格过滤器UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Error: strict mode violation: get_by_test_id(\"wid-product-card-promotion-drawer-item\").first.get_by_test_id(\"btn-atc-plus\") resolved to 2 elements:\n    1) <span tabindex=\"0\" role=\"button\" data-testid=\"btn-atc-…>Add to cart</span> aka locator(\"div:nth-child(2) > div > .w-full > .enki-body-base-strong\").first\n    2) <div tabindex=\"0\" role=\"button\" aria-label=\"add-to-ca…>…</div> aka locator(\"div:nth-child(2) > div > .w-full > .relative > .w-9\").first", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pr0motion_filter_ui_ux.TestDWebPDPPromotionFilterUIUX object at 0x000001DB847F1DD0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc...on=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/HM-Rock-Sugar-Crispy-Water-Chestnut/107200'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...sGSE5eQYhl-c_5C1gRna9gb0Bokl9Nv_9dVcyMZclQtsQODa1-TKpzsRtg9n6tafGSvC8cNssqo9mDtj9R531CtgyitzQV45fT3Ba5xvsbGyPhzw', ...}\nlogin_trace = None\n\n    @allure.title(\"【PC-PDP】活动弹窗价格过滤器UI/UX验证\")\n    def test_dweb_pdp_promotion_filter_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        PC-PDP活动弹窗价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 检查是否存在活动模块\n        3. 点击活动按钮打开活动弹窗\n        4. 验证弹窗中的商品信息\n        5. 检查是否存在价格过滤器\n        6. 测试价格过滤器功能\n        7. 验证过滤结果的准确性\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 直接进入指定pdp页面\n        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,\n                               page_url=\"/product/HM-Rock-Sugar-Crispy-Water-Chestnut/107200\")\n    \n        p.wait_for_timeout(5000)\n        switch_zipcode(pc_autotest_header, '94538')\n        # 2. 检查活动模块是否存在\n        promotions_module = p.get_by_test_id(\"wid-pdp-product-promotions\")\n    \n        if promotions_module.count() == 0 or not promotions_module.is_visible():\n            print(\"商品页面不存在活动模块，跳过活动弹窗验证\")\n            pytest.skip(\"商品页面不存在活动模块\")\n            return\n    \n        print(\"发现商品活动模块，开始验证活动弹窗内容\")\n    \n        # 3. 点击活动按钮打开活动弹窗\n        promotion_button = p.get_by_test_id(\"btn-pdp-product-promotion\")\n        assert promotion_button.count() > 0, \"活动按钮不存在\"\n        assert promotion_button.is_visible(), \"活动按钮不可见\"\n    \n        promotion_button.click()\n        p.wait_for_timeout(2000)\n    \n        # 验证活动弹窗出现\n        promotion_drawer = p.get_by_test_id(\"wid-promotion-drawer-wrapper\")\n        assert promotion_drawer.count() > 0, \"活动弹窗元素不存在\"\n        assert promotion_drawer.is_visible(), \"活动弹窗未出现\"\n    \n        print(\"活动弹窗已打开\")\n    \n        # 4. 验证弹窗中的商品信息\n>       self._verify_promotion_products(p)\n\ntest_110551_dweb_pr0motion_filter_ui_ux.py:61: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\ntest_110551_dweb_pr0motion_filter_ui_ux.py:129: in _verify_promotion_products\n    elif atc_btn.is_visible():\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17546: in is_visible\n    self._sync(self._impl_obj.is_visible(timeout=timeout))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:499: in is_visible\n    return await self._frame.is_visible(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:348: in is_visible\n    return await self._channel.send(\"isVisible\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001DB86F628D0>\nmethod = 'isVisible'\nparams = {'selector': 'internal:testid=[data-testid=\"wid-product-card-promotion-drawer-item\"s] >> nth=0 >> internal:testid=[data-testid=\"btn-atc-plus\"s]', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: Error: strict mode violation: get_by_test_id(\"wid-product-card-promotion-drawer-item\").first.get_by_test_id(\"btn-atc-plus\") resolved to 2 elements:\nE           1) <span tabindex=\"0\" role=\"button\" data-testid=\"btn-atc-…>Add to cart</span> aka locator(\"div:nth-child(2) > div > .w-full > .enki-body-base-strong\").first\nE           2) <div tabindex=\"0\" role=\"button\" aria-label=\"add-to-ca…>…</div> aka locator(\"div:nth-child(2) > div > .w-full > .relative > .w-9\").first\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        PC-PDP活动弹窗价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 检查是否存在活动模块\n        3. 点击活动按钮打开活动弹窗\n        4. 验证弹窗中的商品信息\n        5. 检查是否存在价格过滤器\n        6. 测试价格过滤器功能\n        7. 验证过滤结果的准确性\n        ", "start": 1755844232599, "stop": 1755844251318, "uuid": "c8ff0ef1-635e-431a-9ed3-e06f5fe0b9ee", "historyId": "9351c87328daac0d49bf3f2d0540b727", "testCaseId": "9351c87328daac0d49bf3f2d0540b727", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pr0motion_filter_ui_ux.TestDWebPDPPromotionFilterUIUX#test_dweb_pdp_promotion_filter_ui_ux", "labels": [{"name": "story", "value": "【PC-PDP】活动弹窗价格过滤器UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_110551_dweb_pr0motion_filter_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPPromotionFilterUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "25552-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pr0motion_filter_ui_ux"}]}