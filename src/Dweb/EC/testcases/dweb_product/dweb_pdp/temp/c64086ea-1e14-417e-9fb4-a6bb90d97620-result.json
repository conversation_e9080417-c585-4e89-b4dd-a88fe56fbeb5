{"name": "[111284][dweb]promotion-pc端pdp活动页流程验证", "status": "skipped", "statusDetails": {"message": "Skipped: 商品页面不存在活动模块", "trace": "('D:\\\\software\\\\qa-ui-dmweb\\\\src\\\\Dweb\\\\EC\\\\testcases\\\\dweb_product\\\\dweb_pdp\\\\test_111284_dweb_pdp_promotion_pop.py', 50, 'Skipped: 商品页面不存在活动模块')"}, "description": "\n        PC-PDP商品活动模块UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 检查是否存在活动模块\n        4. 如果存在活动，校验活动元素\n        5. 验证活动图标、标题、描述\n        6. 测试查看更多按钮功能\n        7. 验证活动弹窗显示和关闭\n        ", "start": 1757740754658, "stop": 1757740769722, "uuid": "8222f8be-fa4a-4678-bd65-1c636b77d19f", "historyId": "89604bea0eb06608cba0742324a6255a", "testCaseId": "89604bea0eb06608cba0742324a6255a", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_111284_dweb_pdp_promotion_pop.TestDWebPDPProductPromotionsPopUIUX#test_111284_dweb_pdp_promotions_pop_ui_ux", "labels": [{"name": "story", "value": "[111284][dweb]promotion-pc端pdp活动页流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_111284_dweb_pdp_promotion_pop"}, {"name": "subSuite", "value": "TestDWebPDPProductPromotionsPopUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "23596-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_111284_dweb_pdp_promotion_pop"}]}