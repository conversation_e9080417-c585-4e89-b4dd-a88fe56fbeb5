{"name": "【PC-PDP】活动弹窗价格过滤器UI/UX验证", "status": "passed", "description": "\n        PC-PDP活动弹窗价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 检查是否存在活动模块\n        3. 点击活动按钮打开活动弹窗\n        4. 验证弹窗中的商品信息\n        5. 检查是否存在价格过滤器\n        6. 测试价格过滤器功能\n        7. 验证过滤结果的准确性\n        ", "start": 1755843147454, "stop": 1755843173196, "uuid": "a49661b2-7819-4061-89f3-43654523ceb7", "historyId": "9351c87328daac0d49bf3f2d0540b727", "testCaseId": "9351c87328daac0d49bf3f2d0540b727", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pr0motion_filter_ui_ux.TestDWebPDPPromotionFilterUIUX#test_dweb_pdp_promotion_filter_ui_ux", "labels": [{"name": "story", "value": "【PC-PDP】活动弹窗价格过滤器UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_110551_dweb_pr0motion_filter_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPPromotionFilterUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "11836-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pr0motion_filter_ui_ux"}]}