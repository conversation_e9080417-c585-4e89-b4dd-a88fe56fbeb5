{"uuid": "c0c45c6f-7325-4919-8e78-3c18b6b951d3", "children": ["848e59ed-5cb0-4097-9b74-39818eafb817"], "befores": [{"name": "login_trace", "status": "passed", "start": 1751598524184, "stop": 1751598524214}], "afters": [{"name": "login_trace::0", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"D:\\software\\qa-ui-dmweb\\venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 221, in __call__\n    return self._fixture_function(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\software\\qa-ui-dmweb\\venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 911, in _teardown_yield_fixture\n    next(it)\n  File \"D:\\software\\qa-ui-dmweb\\src\\Dweb\\EC\\conftest.py\", line 326, in login_trace\n    context_case.tracing.stop(path=f\"./login_trace/{os.getenv('BUILD_NUMBER', 0)}/{trace_file_name}.zip\")\n  File \"D:\\software\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 15526, in stop\n    return mapping.from_maybe_impl(self._sync(self._impl_obj.stop(path=path)))\n                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\software\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 113, in _sync\n    self._dispatcher_fiber.switch()\n  File \"D:\\software\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\sync_api\\_context_manager.py\", line 56, in greenlet_main\n    self._loop.run_until_complete(self._connection.run_as_sync())\n  File \"D:\\software\\py\\Lib\\asyncio\\base_events.py\", line 640, in run_until_complete\n    self.run_forever()\n  File \"D:\\software\\py\\Lib\\asyncio\\windows_events.py\", line 321, in run_forever\n    super().run_forever()\n  File \"D:\\software\\py\\Lib\\asyncio\\base_events.py\", line 607, in run_forever\n    self._run_once()\n  File \"D:\\software\\py\\Lib\\asyncio\\base_events.py\", line 1884, in _run_once\n    event_list = self._selector.select(timeout)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\software\\py\\Lib\\asyncio\\windows_events.py\", line 444, in select\n    self._poll(timeout)\n  File \"D:\\software\\py\\Lib\\asyncio\\windows_events.py\", line 817, in _poll\n    status = _overlapped.GetQueuedCompletionStatus(self._iocp, ms)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1751598543582, "stop": 1751598543710}], "start": 1751598524184, "stop": 1751598543710}