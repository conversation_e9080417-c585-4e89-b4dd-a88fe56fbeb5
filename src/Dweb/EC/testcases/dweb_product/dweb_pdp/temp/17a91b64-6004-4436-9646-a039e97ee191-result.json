{"name": "【100616】 验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案", "status": "passed", "description": "\n        【100616】验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案\n        测试步骤：\n        1. 访问Global FBW商品PDP页面\n        2. 校验页面基本元素\n        3. 校验Weee配送信息模块主容器\n        4. 验证配送图标元素\n        5. 验证配送标题和Weee logo\n        6. 验证配送副标题信息\n        7. 验证模块样式和布局\n        ", "start": 1751613826336, "stop": 1751613843327, "uuid": "f2481625-f943-4406-b082-3601bbeb8ce3", "historyId": "394f698f29d2db254618aa306599bd10", "testCaseId": "394f698f29d2db254618aa306599bd10", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_100616_dweb_pdp_global_fbw.TestDWebPDPReviewListUIUX#test_global_fbw_pdp_info_ui", "labels": [{"name": "story", "value": "【100616】 验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案 "}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_100616_dweb_pdp_global_fbw"}, {"name": "subSuite", "value": "TestDWebPDPReviewListUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "39416-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_100616_dweb_pdp_global_fbw"}]}