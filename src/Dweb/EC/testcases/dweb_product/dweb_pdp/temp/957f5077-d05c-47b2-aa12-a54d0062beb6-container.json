{"uuid": "df5a6949-1997-4609-b618-7bbe71d78243", "children": ["448fdb1b-55c3-435b-bd83-9d98d242a0bd"], "befores": [{"name": "page", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1091\\chrome-win\\chrome.exe\n╔════════════════════════════════════════════════════════════╗\n║ Looks like Playwright was just installed or updated.       ║\n║ Please run the following command to download new browsers: ║\n║                                                            ║\n║     playwright install                                     ║\n║                                                            ║\n║ <3 Playwright Team                                         ║\n╚════════════════════════════════════════════════════════════╝\n", "trace": "  File \"D:\\software\\qa-ui-dmweb\\venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 77, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\software\\qa-ui-dmweb\\venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 1123, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\software\\qa-ui-dmweb\\venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 895, in call_fixture_func\n    fixture_result = next(generator)\n                     ^^^^^^^^^^^^^^^\n  File \"D:\\software\\qa-ui-dmweb\\src\\Dweb\\EC\\conftest.py\", line 161, in page\n    browser = playwright.chromium.launch(headless=bool(os.getenv(\"headless\", False)), args=['--start-maximized', '--incognito'])\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\software\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 14806, in launch\n    self._sync(\n  File \"D:\\software\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"D:\\software\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_browser_type.py\", line 95, in launch\n    Browser, from_channel(await self._channel.send(\"launch\", params))\n                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\software\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 62, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\software\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 492, in wrap_api_call\n    return await cb()\n           ^^^^^^^^^^\n  File \"D:\\software\\qa-ui-dmweb\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 100, in inner_send\n    result = next(iter(done)).result()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1750147089750, "stop": 1750147089920}], "start": 1750147089750, "stop": 1750147090198}