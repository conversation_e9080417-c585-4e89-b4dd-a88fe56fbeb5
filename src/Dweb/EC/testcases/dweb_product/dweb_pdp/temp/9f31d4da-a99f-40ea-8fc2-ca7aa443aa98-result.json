{"name": "【PC-All Stores】商品加购弹窗UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Error: strict mode violation: get_by_test_id(\"btn-atc-plus\") resolved to 21 elements:\n    1) <div tabindex=\"0\" role=\"button\" data-testid=\"btn-atc-…>加入购物车</div> aka get_by_test_id(\"wid-add-cart-container\").get_by_test_id(\"btn-atc-plus\")\n    2) <span tabindex=\"0\" role=\"button\" data-testid=\"btn-atc-…>加入购物车</span> aka get_by_label(\"weee 茶颜悦色 四季有猫系列 小贵胄杯 240 毫升\").get_by_role(\"button\", name=\"加入购物车\")\n    3) <div tabindex=\"0\" role=\"button\" aria-label=\"add-to-ca…>…</div> aka get_by_label(\"weee 茶颜悦色 四季有猫系列 小贵胄杯 240 毫升\").get_by_label(\"add-to-cart\")\n    4) <span tabindex=\"0\" role=\"button\" data-testid=\"btn-atc-…>加入购物车</span> aka get_by_label(\"weee 茶颜悦色 茶小颜出游记玻璃杯套装 860 毫升\").get_by_role(\"button\", name=\"加入购物车\")\n    5) <div tabindex=\"0\" role=\"button\" aria-label=\"add-to-ca…>…</div> aka get_by_label(\"weee 茶颜悦色 茶小颜出游记玻璃杯套装 860 毫升\").get_by_label(\"add-to-cart\")\n    6) <span tabindex=\"0\" role=\"button\" data-testid=\"btn-atc-…>加入购物车</span> aka get_by_label(\"weee 茶颜悦色 嘚瑟到摇晃陶瓷杯 280 毫升\").get_by_role(\"button\", name=\"加入购物车\")\n    7) <div tabindex=\"0\" role=\"button\" aria-label=\"add-to-ca…>…</div> aka get_by_label(\"weee 茶颜悦色 嘚瑟到摇晃陶瓷杯 280 毫升\").get_by_label(\"add-to-cart\")\n    8) <span tabindex=\"0\" role=\"button\" data-testid=\"btn-atc-…>加入购物车</span> aka get_by_label(\"weee 茶颜悦色 鸳央联名款公道杯 700 毫升\").get_by_role(\"button\", name=\"加入购物车\")\n    9) <div tabindex=\"0\" role=\"button\" aria-label=\"add-to-ca…>…</div> aka get_by_label(\"weee 茶颜悦色 鸳央联名款公道杯 700 毫升\").get_by_label(\"add-to-cart\")\n    10) <span tabindex=\"0\" role=\"button\" data-testid=\"btn-atc-…>加入购物车</span> aka get_by_label(\"weee 茶颜悦色 烤椰脆脆 5包 5.3 盎司\").get_by_role(\"button\", name=\"加入购物车\")\n    ...", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_101862_dweb_pdp_add_global_sku_pop_ui_ux.TestDWebAllStoresAddCartPopupUIUX object at 0x0000017BEB309750>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc....28>>, 'page': <Page url='https://www.sayweee.com/zh/product/Chayanyuese-Limited-time-Snack-Gift-Box-35Packs/2889810'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...XFM0eBPDqXj1CJBnLA3NJQTxVz2BIHqJe7gCTQnZiX3p95qAmoI3QQ65RnXArqof74BgAVbI27eTsXHXIf9IOBYzRz4C8IL8fUvbdNCyAS6Z2GFo', ...}\nlogin_trace = None\n\n    @allure.title(\"【PC-All Stores】商品加购弹窗UI/UX验证\")\n    def test_dweb_all_stores_add_cart_popup_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        PC All Stores商品加购弹窗UI/UX验证\n        测试步骤：\n        1. 访问All Stores页面\n        2. 验证商家和商品展示\n        3. 点击商品进入PDP页面\n        4. 验证PDP页面商品信息\n        5. 测试加购功能和弹窗\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 进入All Stores页面\n        stores_url = \"https://www.sayweee.com/zh/mkpl/global?mode=sub_page&hide_activity_pop=1\"\n        p.goto(stores_url)\n        p.wait_for_timeout(5000)\n    \n        print(\"All Stores页面加载完成\")\n    \n        # 2. 验证商家容器 - 使用get_by_test_id\n        seller_containers = p.get_by_test_id(\"wid-seller-container\").all()\n        assert len(seller_containers) > 0, \"没有找到商家容器\"\n    \n        print(f\"找到 {len(seller_containers)} 个商家\")\n    \n        # 3. 验证第一个商家的商品\n        first_seller = seller_containers[0]\n    \n        # 验证商家名称\n        seller_title = first_seller.get_by_test_id(\"wid-seller-title\")\n        assert seller_title.is_visible(), \"商家标题不可见\"\n        seller_name = seller_title.text_content()\n        print(f\"商家名称: {seller_name}\")\n    \n        # 4. 获取商品卡片并点击进入PDP\n        product_images = first_seller.get_by_test_id(\"wid-product-card-image\").all()\n        assert len(product_images) > 0, \"没有找到商品卡片\"\n    \n        # 点击第一个商品\n        first_product = product_images[0]\n        first_product.click()\n        p.wait_for_timeout(5000)\n    \n        print(\"已点击商品，进入PDP页面\")\n    \n        # 5. 验证PDP页面加载 - 使用get_by_test_id\n        product_name = p.get_by_test_id(\"wid-pdp-product-name\")\n        assert product_name.is_visible(), \"PDP页面商品名称不可见\"\n        product_name_text = product_name.text_content()\n        print(f\"商品名称: {product_name_text}\")\n    \n        # 验证商品价格\n        current_price = p.get_by_test_id(\"wid-pdp-current-price\")\n        assert current_price.is_visible(), \"商品当前价格不可见\"\n        price_text = current_price.text_content()\n        print(f\"商品价格: {price_text}\")\n    \n        # 验证品牌信息\n        brand = p.get_by_test_id(\"wid-pdp-brand\")\n        if brand.is_visible():\n            brand_text = brand.text_content()\n            print(f\"商品品牌: {brand_text}\")\n    \n        # 6. 验证加购按钮\n        add_cart_container = p.get_by_test_id(\"wid-add-cart-container\")\n        assert add_cart_container.is_visible(), \"加购容器不可见\"\n    \n        add_cart_btn = p.get_by_test_id(\"btn-atc-plus\")\n>       assert add_cart_btn.is_visible(), \"加购按钮不可见\"\n\ntest_101862_dweb_pdp_add_global_sku_pop_ui_ux.py:82: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17546: in is_visible\n    self._sync(self._impl_obj.is_visible(timeout=timeout))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:499: in is_visible\n    return await self._frame.is_visible(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:348: in is_visible\n    return await self._channel.send(\"isVisible\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x0000017BEB9390D0>\nmethod = 'isVisible'\nparams = {'selector': 'internal:testid=[data-testid=\"btn-atc-plus\"s]', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: Error: strict mode violation: get_by_test_id(\"btn-atc-plus\") resolved to 21 elements:\nE           1) <div tabindex=\"0\" role=\"button\" data-testid=\"btn-atc-…>加入购物车</div> aka get_by_test_id(\"wid-add-cart-container\").get_by_test_id(\"btn-atc-plus\")\nE           2) <span tabindex=\"0\" role=\"button\" data-testid=\"btn-atc-…>加入购物车</span> aka get_by_label(\"weee 茶颜悦色 四季有猫系列 小贵胄杯 240 毫升\").get_by_role(\"button\", name=\"加入购物车\")\nE           3) <div tabindex=\"0\" role=\"button\" aria-label=\"add-to-ca…>…</div> aka get_by_label(\"weee 茶颜悦色 四季有猫系列 小贵胄杯 240 毫升\").get_by_label(\"add-to-cart\")\nE           4) <span tabindex=\"0\" role=\"button\" data-testid=\"btn-atc-…>加入购物车</span> aka get_by_label(\"weee 茶颜悦色 茶小颜出游记玻璃杯套装 860 毫升\").get_by_role(\"button\", name=\"加入购物车\")\nE           5) <div tabindex=\"0\" role=\"button\" aria-label=\"add-to-ca…>…</div> aka get_by_label(\"weee 茶颜悦色 茶小颜出游记玻璃杯套装 860 毫升\").get_by_label(\"add-to-cart\")\nE           6) <span tabindex=\"0\" role=\"button\" data-testid=\"btn-atc-…>加入购物车</span> aka get_by_label(\"weee 茶颜悦色 嘚瑟到摇晃陶瓷杯 280 毫升\").get_by_role(\"button\", name=\"加入购物车\")\nE           7) <div tabindex=\"0\" role=\"button\" aria-label=\"add-to-ca…>…</div> aka get_by_label(\"weee 茶颜悦色 嘚瑟到摇晃陶瓷杯 280 毫升\").get_by_label(\"add-to-cart\")\nE           8) <span tabindex=\"0\" role=\"button\" data-testid=\"btn-atc-…>加入购物车</span> aka get_by_label(\"weee 茶颜悦色 鸳央联名款公道杯 700 毫升\").get_by_role(\"button\", name=\"加入购物车\")\nE           9) <div tabindex=\"0\" role=\"button\" aria-label=\"add-to-ca…>…</div> aka get_by_label(\"weee 茶颜悦色 鸳央联名款公道杯 700 毫升\").get_by_label(\"add-to-cart\")\nE           10) <span tabindex=\"0\" role=\"button\" data-testid=\"btn-atc-…>加入购物车</span> aka get_by_label(\"weee 茶颜悦色 烤椰脆脆 5包 5.3 盎司\").get_by_role(\"button\", name=\"加入购物车\")\nE           ...\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        PC All Stores商品加购弹窗UI/UX验证\n        测试步骤：\n        1. 访问All Stores页面\n        2. 验证商家和商品展示\n        3. 点击商品进入PDP页面\n        4. 验证PDP页面商品信息\n        5. 测试加购功能和弹窗\n        ", "start": 1755153351230, "stop": 1755153370916, "uuid": "68f94744-2ea4-4599-9113-b3cb10db1bb6", "historyId": "b19cef2dfde8bdb7b46ef6cf579ac0b5", "testCaseId": "b19cef2dfde8bdb7b46ef6cf579ac0b5", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_101862_dweb_pdp_add_global_sku_pop_ui_ux.TestDWebAllStoresAddCartPopupUIUX#test_dweb_all_stores_add_cart_popup_ui_ux", "labels": [{"name": "story", "value": "【PC-All Stores】商品加购弹窗UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcstores"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_101862_dweb_pdp_add_global_sku_pop_ui_ux"}, {"name": "subSuite", "value": "TestDWebAllStoresAddCartPopupUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "38056-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_101862_dweb_pdp_add_global_sku_pop_ui_ux"}]}