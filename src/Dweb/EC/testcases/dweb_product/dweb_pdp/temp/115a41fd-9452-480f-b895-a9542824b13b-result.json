{"name": "【PC-PDP】活动弹窗价格过滤器UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: 第1个商品加购按钮不可见\nassert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/HM-Rock-Sugar-Crispy-Water-Chestnut/107200'> selector='internal:testid=[data-testid=\"wid-product-card-promotion-drawer-item\"s] >> nth=0 >> internal:testid=[data-testid=\"btn-atc-plus\"s] >> nth=0'>>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/HM-Rock-Sugar-Crispy-Water-Chestnut/107200'> selector='internal:testid=[data-testid=\"wid-product-card-promotion-drawer-item\"s] >> nth=0 >> internal:testid=[data-testid=\"btn-atc-plus\"s] >> nth=0'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/HM-Rock-Sugar-Crispy-Water-Chestnut/107200'> selector='internal:testid=[data-testid=\"wid-product-card-promotion-drawer-item\"s] >> nth=0 >> internal:testid=[data-testid=\"btn-atc-plus\"s] >> nth=0'>.is_visible", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pr0motion_filter_ui_ux.TestDWebPDPPromotionFilterUIUX object at 0x00000231A9A73750>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc...on=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/HM-Rock-Sugar-Crispy-Water-Chestnut/107200'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...UYmO5eyyg8gUjHJUAH7WrxhVDDaaVPJutuI92RkbxV3G8KLAHywdY2HxDi5y4m6ggx77HUI-vxpjmJY5Rgn_x6tUjO8iti5-rjl5l6gZKSi0-hGI', ...}\nlogin_trace = None\n\n    @allure.title(\"【PC-PDP】活动弹窗价格过滤器UI/UX验证\")\n    def test_dweb_pdp_promotion_filter_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        PC-PDP活动弹窗价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 检查是否存在活动模块\n        3. 点击活动按钮打开活动弹窗\n        4. 验证弹窗中的商品信息\n        5. 检查是否存在价格过滤器\n        6. 测试价格过滤器功能\n        7. 验证过滤结果的准确性\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 直接进入指定pdp页面\n        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,\n                               page_url=\"/product/HM-Rock-Sugar-Crispy-Water-Chestnut/107200\")\n    \n        p.wait_for_timeout(5000)\n        switch_zipcode(pc_autotest_header, '94538')\n        # 2. 检查活动模块是否存在\n        promotions_module = p.get_by_test_id(\"wid-pdp-product-promotions\")\n    \n        if promotions_module.count() == 0 or not promotions_module.is_visible():\n            print(\"商品页面不存在活动模块，跳过活动弹窗验证\")\n            pytest.skip(\"商品页面不存在活动模块\")\n            return\n    \n        print(\"发现商品活动模块，开始验证活动弹窗内容\")\n    \n        # 3. 点击活动按钮打开活动弹窗\n        promotion_button = p.get_by_test_id(\"btn-pdp-product-promotion\")\n        assert promotion_button.count() > 0, \"活动按钮不存在\"\n        assert promotion_button.is_visible(), \"活动按钮不可见\"\n    \n        promotion_button.click()\n        p.wait_for_timeout(2000)\n    \n        # 验证活动弹窗出现\n        promotion_drawer = p.get_by_test_id(\"wid-promotion-drawer-wrapper\")\n        assert promotion_drawer.count() > 0, \"活动弹窗元素不存在\"\n        assert promotion_drawer.is_visible(), \"活动弹窗未出现\"\n    \n        print(\"活动弹窗已打开\")\n    \n        # 4. 验证弹窗中的商品信息\n>       self._verify_promotion_products(p)\n\ntest_110551_dweb_pr0motion_filter_ui_ux.py:61: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pr0motion_filter_ui_ux.TestDWebPDPPromotionFilterUIUX object at 0x00000231A9A73750>\np = <Page url='https://www.sayweee.com/en/product/HM-Rock-Sugar-Crispy-Water-Chestnut/107200'>\n\n    def _verify_promotion_products(self, p: Page):\n        \"\"\"验证活动弹窗中的商品信息\"\"\"\n        print(\"开始验证活动弹窗中的商品信息...\")\n    \n        # 验证商品列表容器\n        product_list = p.get_by_test_id(\"wid-promotion-drawer-product-list\")\n        assert product_list.count() > 0, \"商品列表容器不存在\"\n        assert product_list.is_visible(), \"商品列表容器不可见\"\n    \n        # 获取所有商品卡片\n        product_cards = p.get_by_test_id(\"wid-product-card-promotion-drawer-item\").all()\n        assert len(product_cards) > 0, \"没有找到商品卡片\"\n    \n        print(f\"找到 {len(product_cards)} 个商品\")\n    \n        # 验证前3个商品的详细信息\n        for index, card in enumerate(product_cards[:3]):\n            print(f\"验证第 {index + 1} 个商品...\")\n    \n            # 验证商品图片\n            product_image = card.get_by_test_id(\"wid-product-card-image\")\n            assert product_image.count() > 0, f\"第{index + 1}个商品图片容器不存在\"\n            assert product_image.is_visible(), f\"第{index + 1}个商品图片容器不可见\"\n    \n            # 验证图片元素\n            img_element = product_image.locator(\"img\")\n            assert img_element.count() > 0, f\"第{index + 1}个商品图片不存在\"\n            assert img_element.is_visible(), f\"第{index + 1}个商品图片不可见\"\n    \n            image_alt = img_element.get_attribute(\"alt\")\n            image_src = img_element.get_attribute(\"src\")\n            assert image_alt, f\"第{index + 1}个商品图片alt属性为空\"\n            assert image_src and \"weeecdn\" in image_src, f\"第{index + 1}个商品图片src不正确: {image_src}\"\n    \n            # 验证商品标题\n            product_title = card.get_by_test_id(\"wid-product-card-title\")\n            assert product_title.count() > 0, f\"第{index + 1}个商品标题不存在\"\n            assert product_title.is_visible(), f\"第{index + 1}个商品标题不可见\"\n            title_text = product_title.text_content()\n            assert title_text, f\"第{index + 1}个商品标题为空\"\n    \n            # 验证商品价格\n            product_price = card.get_by_test_id(\"wid-product-card-price-value\")\n            assert product_price.count() > 0, f\"第{index + 1}个商品价格不存在\"\n            assert product_price.is_visible(), f\"第{index + 1}个商品价格不可见\"\n            price_text = product_price.text_content()\n            assert price_text and price_text.startswith(\"$\"), f\"第{index + 1}个商品价格格式不正确: {price_text}\"\n    \n            # 验证加购按钮或售罄状态\n            atc_btn = card.get_by_test_id(\"btn-atc-plus\")\n            sold_out_indicator = card.get_by_test_id(\"wid-sold-out\")\n    \n            if sold_out_indicator.count() > 0 and sold_out_indicator.is_visible():\n                # 商品售罄\n                print(f\"第{index + 1}个商品已售罄\")\n                sold_out_text = sold_out_indicator.text_content()\n                print(f\"售罄提示: {sold_out_text}\")\n    \n                # 验证售罄状态下不应该有加购按钮\n                assert atc_btn.count() == 0 or not atc_btn.is_visible(), f\"第{index + 1}个商品已售罄但仍显示加购按钮\"\n    \n            elif atc_btn.count() > 0:\n                # 商品有库存，验证加购按钮\n                first_atc_btn = atc_btn.first\n>               assert first_atc_btn.is_visible(), f\"第{index + 1}个商品加购按钮不可见\"\nE               AssertionError: 第1个商品加购按钮不可见\nE               assert False\nE                +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/HM-Rock-Sugar-Crispy-Water-Chestnut/107200'> selector='internal:testid=[data-testid=\"wid-product-card-promotion-drawer-item\"s] >> nth=0 >> internal:testid=[data-testid=\"btn-atc-plus\"s] >> nth=0'>>()\nE                +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/HM-Rock-Sugar-Crispy-Water-Chestnut/107200'> selector='internal:testid=[data-testid=\"wid-product-card-promotion-drawer-item\"s] >> nth=0 >> internal:testid=[data-testid=\"btn-atc-plus\"s] >> nth=0'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/HM-Rock-Sugar-Crispy-Water-Chestnut/107200'> selector='internal:testid=[data-testid=\"wid-product-card-promotion-drawer-item\"s] >> nth=0 >> internal:testid=[data-testid=\"btn-atc-plus\"s] >> nth=0'>.is_visible\n\ntest_110551_dweb_pr0motion_filter_ui_ux.py:132: AssertionError"}, "description": "\n        PC-PDP活动弹窗价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 检查是否存在活动模块\n        3. 点击活动按钮打开活动弹窗\n        4. 验证弹窗中的商品信息\n        5. 检查是否存在价格过滤器\n        6. 测试价格过滤器功能\n        7. 验证过滤结果的准确性\n        ", "start": 1755843837193, "stop": 1755843854541, "uuid": "b12a5444-091d-4645-9ab8-987d30c5a01a", "historyId": "9351c87328daac0d49bf3f2d0540b727", "testCaseId": "9351c87328daac0d49bf3f2d0540b727", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pr0motion_filter_ui_ux.TestDWebPDPPromotionFilterUIUX#test_dweb_pdp_promotion_filter_ui_ux", "labels": [{"name": "story", "value": "【PC-PDP】活动弹窗价格过滤器UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_110551_dweb_pr0motion_filter_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPPromotionFilterUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "21216-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pr0motion_filter_ui_ux"}]}