{"name": "[110551][dweb]promotion-活动tag详情页价格显示及筛选流程验证", "status": "failed", "statusDetails": {"message": "AssertionError: 活动弹窗元素不存在\nassert 0 > 0\n +  where 0 = <bound method Locator.count of <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Ka<PERSON>-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-Bubble-Hair-Color--Natural-Black/16772'> selector='internal:testid=[data-testid=\"wid-pdp-product-promotion\"s] >> nth=0 >> internal:testid=[data-testid=\"wid-promotion-drawer-wrapper\"s]'>>()\n +    where <bound method Locator.count of <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Ka<PERSON>-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-Bubble-Hair-Color--Natural-Black/16772'> selector='internal:testid=[data-testid=\"wid-pdp-product-promotion\"s] >> nth=0 >> internal:testid=[data-testid=\"wid-promotion-drawer-wrapper\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/<PERSON><PERSON>-<PERSON><PERSON>-<PERSON>tti<PERSON>-Bubble-Hair-Color--Natural-Black/16772'> selector='internal:testid=[data-testid=\"wid-pdp-product-promotion\"s] >> nth=0 >> internal:testid=[data-testid=\"wid-promotion-drawer-wrapper\"s]'>.count", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_promotion_filter_ui_ux.TestDWebPDPPromotionFilterUIUX object at 0x0000023814F58850>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc...28>>, 'page': <Page url='https://www.sayweee.com/en/product/Kao-<PERSON>e-Prettia-Bubble-Hair-Color--Natural-Black/16772'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...ouLIsG72LzNC2OO-nDC7NGcfe7xz9pCD74Y8lv6ARImKKmhZIW04FTbU8HyBF3hPmlFPnpXTjhmNEnsJ58maD7TbrhcPIarlpjrnwTsjhj700cGE', ...}\nlogin_trace = None\n\n    @allure.title(\"[110551][dweb]promotion-活动tag详情页价格显示及筛选流程验证\")\n    def test_110551_dweb_pdp_promotion_filter_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        PC-PDP活动弹窗价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 检查是否存在活动模块\n        3. 点击活动按钮打开活动弹窗\n        4. 验证弹窗中的商品信息\n        5. 检查是否存在价格过滤器\n        6. 测试价格过滤器功能\n        7. 验证过滤结果的准确性\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 直接进入指定pdp页面\n        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,\n                               page_url=\"/product/Kao-Liese-Prettia-Bubble-Hair-Color--Natural-Black/16772\")\n    \n        p.wait_for_timeout(5000)\n        switch_zipcode(headers=pc_autotest_header, zipcode=\"99991\")\n        # 2. 检查活动模块是否存在\n        promotions_module = p.get_by_test_id(\"wid-pdp-product-promotions\")\n    \n        if promotions_module.count() == 0 or not promotions_module.is_visible():\n            print(\"商品页面不存在活动模块，跳过活动弹窗验证\")\n            pytest.skip(\"商品页面不存在活动模块\")\n            return\n    \n        print(\"发现商品活动模块，开始验证活动弹窗内容\")\n        promotion_items = p.get_by_test_id(\"wid-pdp-product-promotion\").all()\n        assert len(promotion_items) > 0, \"活动项不存在\"\n    \n        for i, promotion_item in enumerate(promotion_items):\n            print(f\"验证第 {i + 1} 个活动...\")\n    \n            # 3. 点击活动按钮打开活动弹窗\n            promotion_button = promotion_item.get_by_test_id(\"btn-pdp-product-promotion\")\n            assert promotion_button.count() > 0, \"活动按钮不存在\"\n            assert promotion_button.is_visible(), \"活动按钮不可见\"\n    \n            promotion_button.click()\n            p.wait_for_timeout(2000)\n    \n            # 验证活动弹窗出现\n            promotion_drawer = promotion_item.get_by_test_id(\"wid-promotion-drawer-wrapper\")\n>           assert promotion_drawer.count() > 0, \"活动弹窗元素不存在\"\nE           AssertionError: 活动弹窗元素不存在\nE           assert 0 > 0\nE            +  where 0 = <bound method Locator.count of <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Kao-Liese-Prettia-Bubble-Hair-Color--Natural-Black/16772'> selector='internal:testid=[data-testid=\"wid-pdp-product-promotion\"s] >> nth=0 >> internal:testid=[data-testid=\"wid-promotion-drawer-wrapper\"s]'>>()\nE            +    where <bound method Locator.count of <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Kao-Liese-Prettia-Bubble-Hair-Color--Natural-Black/16772'> selector='internal:testid=[data-testid=\"wid-pdp-product-promotion\"s] >> nth=0 >> internal:testid=[data-testid=\"wid-promotion-drawer-wrapper\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Kao-Liese-Prettia-Bubble-Hair-Color--Natural-Black/16772'> selector='internal:testid=[data-testid=\"wid-pdp-product-promotion\"s] >> nth=0 >> internal:testid=[data-testid=\"wid-promotion-drawer-wrapper\"s]'>.count\n\ntest_110551_dweb_promotion_filter_ui_ux.py:60: AssertionError"}, "description": "\n        PC-PDP活动弹窗价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 检查是否存在活动模块\n        3. 点击活动按钮打开活动弹窗\n        4. 验证弹窗中的商品信息\n        5. 检查是否存在价格过滤器\n        6. 测试价格过滤器功能\n        7. 验证过滤结果的准确性\n        ", "start": 1757747103037, "stop": 1757747117236, "uuid": "53dd2b8c-2b23-4861-983e-3b5e837738ec", "historyId": "5e10af71f5d1a908adc084dd76d8b20b", "testCaseId": "5e10af71f5d1a908adc084dd76d8b20b", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_promotion_filter_ui_ux.TestDWebPDPPromotionFilterUIUX#test_110551_dweb_pdp_promotion_filter_ui_ux", "labels": [{"name": "story", "value": "[110551][dweb]promotion-活动tag详情页价格显示及筛选流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_110551_dweb_promotion_filter_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPPromotionFilterUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "22304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_promotion_filter_ui_ux"}]}