{"name": "【100616】 验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'is_visible'", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_100616_dweb_pdp_global_fbw.TestDWebPDPReviewListUIUX object at 0x0000027D300EA210>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc...e': <Page url='https://www.sayweee.com/en/zh/product/2X-Iron-Spatula-Cooking-Spoon-Spatula-Long-Beech-Handle/2120534'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...__JtO__SrlP7lkoQTFj-Z2ypW_MbTtp0JNAY7waD0FK__ECndvYzEErhxIUXxIR_kVo7KoMUCOMmzrmf1jFZxj1OPhTGyB1Odw7cmFQkJS6T4NYM', ...}\nlogin_trace = None\n\n    @allure.title(\"【100616】 验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案\")\n    def test_global_fbw_pdp_info_ui(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【100616】验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案\n        测试步骤：\n        1. 访问Global FBW商品PDP页面\n        2. 校验页面基本元素\n        3. 校验Weee配送信息模块主容器\n        4. 验证配送图标元素\n        5. 验证配送标题和Weee logo\n        6. 验证配送副标题信息\n        7. 验证模块样式和布局\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 直接进入Global FBW商品pdp页面\n        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,\n                               page_url=\"/zh/product/2X-Iron-Spatula-Cooking-Spoon-Spatula-Long-Beech-Handle/2120534\")\n    \n        p.wait_for_timeout(5000)\n    \n        # 2. 校验Weee配送信息模块主容器存在\n        promotion_module = pdp_page.FE.ele(dweb_pdp_ele.ele_pdp_global_fbw_info)\n>       assert promotion_module.is_visible(), \"Global FBW配送信息模块不可见\"\nE       AttributeError: 'NoneType' object has no attribute 'is_visible'\n\ntest_100616_dweb_pdp_global_fbw.py:38: AttributeError"}, "description": "\n        【100616】验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案\n        测试步骤：\n        1. 访问Global FBW商品PDP页面\n        2. 校验页面基本元素\n        3. 校验Weee配送信息模块主容器\n        4. 验证配送图标元素\n        5. 验证配送标题和Weee logo\n        6. 验证配送副标题信息\n        7. 验证模块样式和布局\n        ", "start": 1751598524216, "stop": 1751598543549, "uuid": "848e59ed-5cb0-4097-9b74-39818eafb817", "historyId": "394f698f29d2db254618aa306599bd10", "testCaseId": "394f698f29d2db254618aa306599bd10", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_100616_dweb_pdp_global_fbw.TestDWebPDPReviewListUIUX#test_global_fbw_pdp_info_ui", "labels": [{"name": "story", "value": "【100616】 验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案 "}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_100616_dweb_pdp_global_fbw"}, {"name": "subSuite", "value": "TestDWebPDPReviewListUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "1716-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_100616_dweb_pdp_global_fbw"}]}