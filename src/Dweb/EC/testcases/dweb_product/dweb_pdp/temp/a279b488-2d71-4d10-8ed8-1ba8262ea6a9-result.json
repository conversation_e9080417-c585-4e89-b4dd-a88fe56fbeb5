{"name": "[110551][dweb]promotion-活动tag详情页价格显示及筛选流程验证", "status": "passed", "description": "\n        PC-PDP活动弹窗价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 检查是否存在活动模块\n        3. 点击活动按钮打开活动弹窗\n        4. 验证弹窗中的商品信息\n        5. 检查是否存在价格过滤器\n        6. 测试价格过滤器功能\n        7. 验证过滤结果的准确性\n        ", "start": 1757747720762, "stop": 1757747869352, "uuid": "c57d3d71-31ad-4c15-a8d5-9a6b5fa7decd", "historyId": "5e10af71f5d1a908adc084dd76d8b20b", "testCaseId": "5e10af71f5d1a908adc084dd76d8b20b", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_promotion_filter_ui_ux.TestDWebPDPPromotionFilterUIUX#test_110551_dweb_pdp_promotion_filter_ui_ux", "labels": [{"name": "story", "value": "[110551][dweb]promotion-活动tag详情页价格显示及筛选流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_110551_dweb_promotion_filter_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPPromotionFilterUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "15468-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_promotion_filter_ui_ux"}]}