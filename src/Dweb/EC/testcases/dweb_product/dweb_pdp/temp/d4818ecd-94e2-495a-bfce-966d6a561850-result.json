{"name": "[101480]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Unsupported token \"@data-testid\" while parsing selector \"div[@data-testid='wid-product-card-title']\"", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_101480_dweb_pdp_same_vendor_ui_ux.TestDWebPDPSameVendorUIUX object at 0x000001E8FC6E53D0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc...ion=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/SK-II-Skinpower-Airy-Milky-Lotion/2038708'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...xedZ53w-65tJ9K9b1UlhARRerU4B4MU-ZIjF66VotPcO7Y0GdOdezVHcxkdHAnN-wuierfUUUaqrSaBeyIbFOwLrZLtgrZJwROziNwph9egps8ok', ...}\nlogin_trace = None\n\n    @allure.title(\"[101480]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\")\n    def test_101480_dweb_pdp_same_vendor_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        [101480]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 滚动到店铺推荐模块\n        4. 校验店铺推荐模块元素\n        5. 验证商品信息（名称、价格）\n        6. 测试收藏按钮功能\n        7. 测试加购按钮功能\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 直接进入指定pdp页面\n        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,\n                               page_url=\"/product/SK-II-Skinpower-Airy-Milky-Lotion/2038708\")\n    \n        p.wait_for_timeout(5000)\n    \n        # 2. 滚动到店铺推荐模块\n        scroll_one_page_until(p, dweb_pdp_ele.ele_pdp_same_vendor_card)\n    \n        # 3. 校验店铺推荐模块存在\n        assert pdp_page.FE.ele(dweb_pdp_ele.ele_pdp_same_vendor_card).is_visible(), \"店铺推荐模块不可见\"\n    \n        # 4. 校验店铺推荐标题\n        assert pdp_page.FE.ele(dweb_pdp_ele.ele_pdp_same_vendor_title).is_visible(), \"店铺推荐标题不可见\"\n    \n        # 5. 获取店铺推荐商品列表\n        same_vendor_products = pdp_page.FE.eles(dweb_pdp_ele.ele_pdp_same_vendor_product_card)\n        assert len(same_vendor_products) > 0, \"店铺推荐商品列表为空\"\n    \n        # 6. 验证每个商品的基本信息\n        for index, product in enumerate(same_vendor_products[:3]):  # 只验证前3个商品\n            # 6.1 验证商品名称\n>           product_name_ele = product.query_selector(\"div[@data-testid='wid-product-card-title']\")\n\ntest_101480_dweb_pdp_same_vendor_ui_ux.py:52: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:2897: in query_selector\n    self._sync(self._impl_obj.query_selector(selector=selector))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_element_handle.py:317: in query_selector\n    await self._channel.send(\"querySelector\", dict(selector=selector))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001E8FF437AD0>\nmethod = 'querySelector'\nparams = {'selector': \"div[@data-testid='wid-product-card-title']\"}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: Unsupported token \"@data-testid\" while parsing selector \"div[@data-testid='wid-product-card-title']\"\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        [101480]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 滚动到店铺推荐模块\n        4. 校验店铺推荐模块元素\n        5. 验证商品信息（名称、价格）\n        6. 测试收藏按钮功能\n        7. 测试加购按钮功能\n        ", "start": 1751007089181, "stop": 1751007103368, "uuid": "fc6284de-b409-4662-8146-c63c301bda08", "historyId": "ba2c7a7bf4001cfafbcc89e6f461da13", "testCaseId": "ba2c7a7bf4001cfafbcc89e6f461da13", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_101480_dweb_pdp_same_vendor_ui_ux.TestDWebPDPSameVendorUIUX#test_101480_dweb_pdp_same_vendor_ui_ux", "labels": [{"name": "story", "value": "[101480]-Global+商品-验证商品PDP-店铺推荐模块"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_101480_dweb_pdp_same_vendor_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPSameVendorUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "33992-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_101480_dweb_pdp_same_vendor_ui_ux"}]}