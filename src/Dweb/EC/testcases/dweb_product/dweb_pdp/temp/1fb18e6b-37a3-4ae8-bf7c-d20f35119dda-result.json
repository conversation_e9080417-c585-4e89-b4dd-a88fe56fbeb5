{"name": "[101480]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1091\\chrome-win\\chrome.exe\n╔════════════════════════════════════════════════════════════╗\n║ Looks like Playwright was just installed or updated.       ║\n║ Please run the following command to download new browsers: ║\n║                                                            ║\n║     playwright install                                     ║\n║                                                            ║\n║ <3 Playwright Team                                         ║\n╚════════════════════════════════════════════════════════════╝", "trace": "playwright = <playwright._impl._playwright.Playwright object at 0x000001F7A27F8910>\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...MSfN_1FvGxiOlhsKWyhMyHlhpTE9TdRFJIKOfC2JOFw34isZWGSkTWajWbqkJSTAAhsCvUEtLC6wf4wLBxxy5JmTJ9MmhVXXGjR5ZV9ptdd0BqOs', ...}\nporder = {'message': None, 'message_id': '10000', 'object': {'addr_address': None, 'addr_city': 'Bothell', 'addr_country': '2', 'addr_state': '84', ...}, 'result': True, ...}\n\n    @pytest.fixture(scope='session')\n    def page(playwright, pc_autotest_header, porder) -> Page:\n        # browser = playwright.chromium.launch(executable_path=r'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', headless=False, args=['--start-maximized'])\n>       browser = playwright.chromium.launch(headless=bool(os.getenv(\"headless\", False)), args=['--start-maximized', '--incognito'])\n\n..\\..\\..\\conftest.py:161: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:14806: in launch\n    self._sync(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_browser_type.py:95: in launch\n    Browser, from_channel(await self._channel.send(\"launch\", params))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001F7A27B65D0>\nmethod = 'launch'\nparams = {'args': ['--start-maximized', '--incognito'], 'headless': False}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1091\\chrome-win\\chrome.exe\nE       ╔════════════════════════════════════════════════════════════╗\nE       ║ Looks like Playwright was just installed or updated.       ║\nE       ║ Please run the following command to download new browsers: ║\nE       ║                                                            ║\nE       ║     playwright install                                     ║\nE       ║                                                            ║\nE       ║ <3 Playwright Team                                         ║\nE       ╚════════════════════════════════════════════════════════════╝\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        [101480]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 滚动到店铺推荐模块\n        4. 校验店铺推荐模块元素\n        5. 验证商品信息（名称、价格）\n        6. 测试收藏按钮功能\n        7. 测试加购按钮功能\n        ", "start": 1751003681367, "stop": 1751003681367, "uuid": "87aff11a-89aa-4d74-a2fe-ed0405c119e7", "historyId": "ba2c7a7bf4001cfafbcc89e6f461da13", "testCaseId": "ba2c7a7bf4001cfafbcc89e6f461da13", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_101480_dweb_pdp_same_vendor_ui_ux.TestDWebPDPSameVendorUIUX#test_101480_dweb_pdp_same_vendor_ui_ux", "labels": [{"name": "story", "value": "[101480]-Global+商品-验证商品PDP-店铺推荐模块"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_101480_dweb_pdp_same_vendor_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPSameVendorUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "29284-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_101480_dweb_pdp_same_vendor_ui_ux"}]}