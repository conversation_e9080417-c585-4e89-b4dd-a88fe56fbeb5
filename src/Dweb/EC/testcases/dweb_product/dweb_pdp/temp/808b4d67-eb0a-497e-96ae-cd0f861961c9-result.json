{"name": "【PC-PDP】活动弹窗详细内容UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: 第1个商品加购按钮文本不可见\nassert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Synear-Vegetable-Dumpling--Frozen-1/66118'> selector=\"div[data-testid^='wid-product-card-item-'] >> nth=0 >> span[data-testid='btn-atc-plus']\">>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Synear-Vegetable-Dumpling--Frozen-1/66118'> selector=\"div[data-testid^='wid-product-card-item-'] >> nth=0 >> span[data-testid='btn-atc-plus']\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Synear-Vegetable-Dumpling--Frozen-1/66118'> selector=\"div[data-testid^='wid-product-card-item-'] >> nth=0 >> span[data-testid='btn-atc-plus']\">.is_visible", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pdp_promotion_ui_ux.TestDWebPDPPromotionDrawerUIUX object at 0x000001DDE6FB79D0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc...ion=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/Synear-Vegetable-Dumpling--Frozen-1/66118'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...kA6S6v9RWEukQIc17DF63kgYhat9FwAPJUFq2o6kIYelb89ozPaWPYY5kkPkBzWM_OJq-C-dHE3v4GrCPW7P6tWhjN8zqQSWaF5gcJGodfbZsjow', ...}\nlogin_trace = None\n\n    @allure.title(\"【PC-PDP】活动弹窗详细内容UI/UX验证\")\n    def test_dweb_pdp_promotion_drawer_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        PC-PDP活动弹窗详细内容UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 检查是否存在活动模块\n        3. 点击查看更多打开活动弹窗\n        4. 验证活动名称和描述\n        5. 验证商品展示（价格、图片、标题）\n        6. 检查价格过滤器是否存在\n        7. 如果存在过滤器，测试过滤功能\n        8. 如果不存在过滤器，直接测试加购功能\n        9. 验证活动进度信息\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 直接进入指定pdp页面\n        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,\n                               page_url=\"/product/Synear-Vegetable-Dumpling--Frozen-1/66118\")\n    \n        p.wait_for_timeout(5000)\n    \n        # 2. 检查活动模块是否存在\n        promotions_module = p.locator(\"div[data-testid='wid-pdp-product-promotions']\")\n    \n        if not promotions_module.is_visible():\n            print(\"商品页面不存在活动模块，跳过活动弹窗验证\")\n            pytest.skip(\"商品页面不存在活动模块\")\n            return\n    \n        print(\"发现商品活动模块，开始验证活动弹窗内容\")\n    \n        # 3. 点击查看更多按钮打开活动弹窗\n        promotion_button = p.locator(\"div[data-testid='btn-pdp-product-promotion']\")\n        assert promotion_button.is_visible(), \"查看更多按钮不可见\"\n    \n        promotion_button.click()\n        p.wait_for_timeout(2000)\n    \n        # 验证活动弹窗出现\n        promotion_drawer = p.locator(\"div[data-testid='wid-promotion-drawer-wrapper']\")\n        assert promotion_drawer.is_visible(), \"活动弹窗未出现\"\n    \n        print(\"活动弹窗已打开\")\n    \n        # 4. 验证活动名称和描述\n        self._verify_promotion_content(p)\n    \n        # 5. 验证商品展示\n>       self._verify_product_display(p)\n\ntest_110551_dweb_pdp_promotion_ui_ux.py:64: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pdp_promotion_ui_ux.TestDWebPDPPromotionDrawerUIUX object at 0x000001DDE6FB79D0>\np = <Page url='https://www.sayweee.com/en/product/Synear-Vegetable-Dumpling--Frozen-1/66118'>\n\n    def _verify_product_display(self, p: Page):\n        \"\"\"验证商品展示（修正版 - 使用动态ID匹配）\"\"\"\n        print(\"开始验证商品展示...\")\n    \n        # 验证商品列表容器\n        product_list = p.locator(\"div[data-testid='wid-promotion-drawer-product-list']\")\n        assert product_list.is_visible(), \"商品列表容器不可见\"\n    \n        # 获取所有商品卡片（使用CSS选择器匹配以wid-product-card-item-开头的data-testid）\n        product_cards = p.locator(\"div[data-testid^='wid-product-card-item-']\").all()\n        assert len(product_cards) > 0, \"没有找到商品卡片\"\n    \n        print(f\"找到 {len(product_cards)} 个商品\")\n    \n        # 验证前3个商品的详细信息\n        for index, card in enumerate(product_cards[:3]):\n            print(f\"验证第 {index + 1} 个商品...\")\n    \n            # 获取商品ID（从data-testid中提取）\n            card_testid = card.get_attribute(\"data-testid\")\n            product_id = card_testid.replace(\"wid-product-card-item-\", \"\") if card_testid else \"unknown\"\n            print(f\"商品ID: {product_id}\")\n    \n            # 验证商品图片容器\n            image_container = card.locator(\"div[data-testid='wid-product-card-image']\")\n            assert image_container.is_visible(), f\"第{index + 1}个商品图片容器不可见\"\n    \n            # 验证商品图片\n            product_image = image_container.locator(\"img\")\n            assert product_image.is_visible(), f\"第{index + 1}个商品图片不可见\"\n    \n            image_alt = product_image.get_attribute(\"alt\")\n            image_src = product_image.get_attribute(\"src\")\n            assert image_alt, f\"第{index + 1}个商品图片alt属性为空\"\n            assert image_src and \"weeecdn.net\" in image_src, f\"第{index + 1}个商品图片src不正确: {image_src}\"\n    \n            # 验证图片容器样式（105px x 105px）\n            container_style = image_container.get_attribute(\"style\")\n            assert \"width: 105px\" in container_style, f\"第{index + 1}个商品图片容器宽度不正确\"\n            assert \"height: 105px\" in container_style, f\"第{index + 1}个商品图片容器高度不正确\"\n    \n            # 验证商品标题\n            product_title = card.locator(\"p[data-testid='wid-product-card-title']\")\n            assert product_title.is_visible(), f\"第{index + 1}个商品标题不可见\"\n            title_text = product_title.text_content()\n            assert title_text, f\"第{index + 1}个商品标题为空\"\n    \n            # 验证标题样式\n            title_classes = product_title.get_attribute(\"class\")\n            assert \"enki-body-base-medium\" in title_classes, f\"第{index + 1}个商品标题样式不正确\"\n            assert \"line-clamp-2\" in title_classes, f\"第{index + 1}个商品标题行数限制不正确\"\n    \n            # 验证商品当前价格\n            product_price = card.locator(\"span[data-testid='wid-product-card-price-value']\")\n            assert product_price.is_visible(), f\"第{index + 1}个商品价格不可见\"\n            price_text = product_price.text_content()\n            assert price_text and price_text.startswith(\"$\"), f\"第{index + 1}个商品价格格式不正确: {price_text}\"\n    \n            # 验证价格样式\n            price_classes = product_price.get_attribute(\"class\")\n            assert \"text-[#C92927]\" in price_classes, f\"第{index + 1}个商品价格颜色不正确\"\n            assert \"font-semibold\" in price_classes, f\"第{index + 1}个商品价格字体不正确\"\n    \n            # 验证商品原价（如果存在）\n            base_price = card.locator(\"span[data-testid='wid-product-card-base-price']\")\n            if base_price.is_visible():\n                base_price_text = base_price.text_content()\n                base_price_classes = base_price.get_attribute(\"class\")\n                assert \"line-through\" in base_price_classes, f\"第{index + 1}个商品原价样式不正确\"\n                print(f\"第{index + 1}个商品原价: {base_price_text}\")\n    \n            # 验证加购按钮（检查两个部分）\n            # 1. 文本部分\n            atc_text = card.locator(\"span[data-testid='btn-atc-plus']\")\n>           assert atc_text.is_visible(), f\"第{index + 1}个商品加购按钮文本不可见\"\nE           AssertionError: 第1个商品加购按钮文本不可见\nE           assert False\nE            +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Synear-Vegetable-Dumpling--Frozen-1/66118'> selector=\"div[data-testid^='wid-product-card-item-'] >> nth=0 >> span[data-testid='btn-atc-plus']\">>()\nE            +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Synear-Vegetable-Dumpling--Frozen-1/66118'> selector=\"div[data-testid^='wid-product-card-item-'] >> nth=0 >> span[data-testid='btn-atc-plus']\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Synear-Vegetable-Dumpling--Frozen-1/66118'> selector=\"div[data-testid^='wid-product-card-item-'] >> nth=0 >> span[data-testid='btn-atc-plus']\">.is_visible\n\ntest_110551_dweb_pdp_promotion_ui_ux.py:184: AssertionError"}, "description": "\n        PC-PDP活动弹窗详细内容UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 检查是否存在活动模块\n        3. 点击查看更多打开活动弹窗\n        4. 验证活动名称和描述\n        5. 验证商品展示（价格、图片、标题）\n        6. 检查价格过滤器是否存在\n        7. 如果存在过滤器，测试过滤功能\n        8. 如果不存在过滤器，直接测试加购功能\n        9. 验证活动进度信息\n        ", "start": 1754650352110, "stop": 1754650371075, "uuid": "c05bf04f-cba1-46e9-abcc-db510cc71c32", "historyId": "ba9fefcd295d2faf82ffffa491f944b0", "testCaseId": "ba9fefcd295d2faf82ffffa491f944b0", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pdp_promotion_ui_ux.TestDWebPDPPromotionDrawerUIUX#test_dweb_pdp_promotion_drawer_ui_ux", "labels": [{"name": "story", "value": "【PC-PDP】活动弹窗详细内容UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_110551_dweb_pdp_promotion_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPPromotionDrawerUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "5996-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pdp_promotion_ui_ux"}]}