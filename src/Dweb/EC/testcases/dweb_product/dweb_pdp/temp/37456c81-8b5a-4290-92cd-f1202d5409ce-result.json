{"name": "【PC-All Stores】商品加购弹窗UI/UX验证", "status": "passed", "description": "\n        PC All Stores商品加购弹窗UI/UX验证\n        测试步骤：\n        1. 访问All Stores页面\n        2. 验证商家和商品展示\n        3. 点击商品进入PDP页面\n        4. 验证PDP页面商品信息\n        5. 测试加购功能和弹窗\n        ", "start": 1755154605476, "stop": 1755154636680, "uuid": "8328599d-e80c-4be1-925b-3aa5e7bb8822", "historyId": "332cbf97d64edeae808c85bd3f6e5414", "testCaseId": "332cbf97d64edeae808c85bd3f6e5414", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_101862_dweb_pdp_add_global_sku_pop_ui_ux.TestDWebPdpAddGlobalPopupUIUX#test_101862_dweb_pdp_add_global_sku_pop_ui_ux", "labels": [{"name": "story", "value": "【PC-All Stores】商品加购弹窗UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcstores"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_101862_dweb_pdp_add_global_sku_pop_ui_ux"}, {"name": "subSuite", "value": "TestDWebPdpAddGlobalPopupUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "38520-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_101862_dweb_pdp_add_global_sku_pop_ui_ux"}]}