{"name": "【PC-PDP】活动弹窗详细内容UI/UX验证", "status": "skipped", "statusDetails": {"message": "Skipped: 商品页面不存在活动模块", "trace": "('D:\\\\software\\\\qa-ui-dmweb\\\\src\\\\Dweb\\\\EC\\\\testcases\\\\dweb_product\\\\dweb_pdp\\\\test_110551_dweb_pdp_promotion_ui_ux.py', 42, 'Skipped: 商品页面不存在活动模块')"}, "description": "\n        PC-PDP活动弹窗详细内容UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 检查是否存在活动模块\n        3. 点击查看更多打开活动弹窗\n        4. 验证活动名称和描述\n        5. 验证商品展示（价格、图片、标题）\n        6. 检查价格过滤器是否存在\n        7. 如果存在过滤器，测试过滤功能\n        8. 如果不存在过滤器，直接测试加购功能\n        9. 验证活动进度信息\n        ", "start": 1754650316933, "stop": 1754650327395, "uuid": "b7756688-1000-4dd0-919e-9ae9c02ca626", "historyId": "ba9fefcd295d2faf82ffffa491f944b0", "testCaseId": "ba9fefcd295d2faf82ffffa491f944b0", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pdp_promotion_ui_ux.TestDWebPDPPromotionDrawerUIUX#test_dweb_pdp_promotion_drawer_ui_ux", "labels": [{"name": "story", "value": "【PC-PDP】活动弹窗详细内容UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_110551_dweb_pdp_promotion_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPPromotionDrawerUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "12052-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110551_dweb_pdp_promotion_ui_ux"}]}