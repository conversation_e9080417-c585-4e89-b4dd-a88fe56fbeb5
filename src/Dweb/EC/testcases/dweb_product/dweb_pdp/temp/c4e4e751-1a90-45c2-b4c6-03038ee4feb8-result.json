{"name": "[111284][dweb]promotion-pc端pdp活动页流程验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'Page' object has no attribute 'page'", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_111284_dweb_pdp_promotion_pop.TestDWebPDPProductPromotionsPopUIUX object at 0x000002C137C56B10>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc...on=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/HM-Rock-Sugar-Crispy-Water-Chestnut/107200'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...KDOfP85kUFGW3TXmUeThhnNqjoqO48CgN958jwiKY92vkikjzw_k6uUq0AwWKThaFYhPgME1gUdQuUW11zyXuq5jl8ObSi0jW45FKDvonKYcQ6kU', ...}\nlogin_trace = None\n\n    @allure.title(\"[111284][dweb]promotion-pc端pdp活动页流程验证\")\n    def test_111284_dweb_pdp_promotions_pop_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        PC-PDP商品活动模块UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 检查是否存在活动模块\n        4. 如果存在活动，校验活动元素\n        5. 验证活动图标、标题、描述\n        6. 测试查看更多按钮功能\n        7. 验证活动弹窗显示和关闭\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 直接进入指定pdp页面\n        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,\n                               page_url=\"/product/HM-Rock-Sugar-Crispy-Water-Chestnut/107200\")\n    \n        p.wait_for_timeout(5000)\n    \n        # 2. 检查活动模块是否存在\n        # 等待页面完全加载\n        p.wait_for_load_state(\"networkidle\", timeout=30000)\n    \n>       zipcode = p.page.get_by_test_id('wid-modal-zip-code').text_content()\nE       AttributeError: 'Page' object has no attribute 'page'\n\ntest_111284_dweb_pdp_promotion_pop.py:40: AttributeError"}, "description": "\n        PC-PDP商品活动模块UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 检查是否存在活动模块\n        4. 如果存在活动，校验活动元素\n        5. 验证活动图标、标题、描述\n        6. 测试查看更多按钮功能\n        7. 验证活动弹窗显示和关闭\n        ", "start": 1757735217065, "stop": 1757735228640, "uuid": "d11fd064-46c8-4440-86d5-5099b3bfde18", "historyId": "89604bea0eb06608cba0742324a6255a", "testCaseId": "89604bea0eb06608cba0742324a6255a", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_111284_dweb_pdp_promotion_pop.TestDWebPDPProductPromotionsPopUIUX#test_111284_dweb_pdp_promotions_pop_ui_ux", "labels": [{"name": "story", "value": "[111284][dweb]promotion-pc端pdp活动页流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_111284_dweb_pdp_promotion_pop"}, {"name": "subSuite", "value": "TestDWebPDPProductPromotionsPopUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "30568-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_111284_dweb_pdp_promotion_pop"}]}