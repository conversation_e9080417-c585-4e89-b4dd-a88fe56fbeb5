{"name": "[101480]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: 收藏按钮不存在\nassert None", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_101480_dweb_pdp_same_vendor_ui_ux.TestDWebPDPSameVendorUIUX object at 0x000002466A4A07D0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc...ion=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/SK-II-Skinpower-Airy-Milky-Lotion/2038708'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...dCl4h8eBvQzVGx7OH1f4fE7iCvIHoKlNrSBB-9Mqdzf9AAhoPiXgPDcxtYmnUdZ8L9TwlGbQyiHkUSZDdQdkuQHLJ8ws_3ShK4PtwXxIGDXLvBGM', ...}\nlogin_trace = None\n\n    @allure.title(\"[101480]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\")\n    def test_101480_dweb_pdp_same_vendor_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        [101480]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 滚动到店铺推荐模块\n        4. 校验店铺推荐模块元素\n        5. 验证商品信息（名称、价格）\n        6. 测试收藏按钮功能\n        7. 测试加购按钮功能\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 直接进入指定pdp页面\n        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,\n                               page_url=\"/product/SK-II-Skinpower-Airy-Milky-Lotion/2038708\")\n    \n        p.wait_for_timeout(5000)\n    \n        # 2. 滚动到店铺推荐模块\n        scroll_one_page_until(p, dweb_pdp_ele.ele_pdp_same_vendor_card)\n    \n        # 3. 校验店铺推荐模块存在\n        assert pdp_page.FE.ele(dweb_pdp_ele.ele_pdp_same_vendor_card).is_visible(), \"店铺推荐模块不可见\"\n    \n        # 4. 校验店铺推荐标题\n        assert pdp_page.FE.ele(dweb_pdp_ele.ele_pdp_same_vendor_title).is_visible(), \"店铺推荐标题不可见\"\n    \n        # 5. 获取店铺推荐商品列表\n        same_vendor_products = pdp_page.FE.eles(dweb_pdp_ele.ele_pdp_same_vendor_product_card)\n        assert len(same_vendor_products) > 0, \"店铺推荐商品列表为空\"\n    \n        # 6. 验证每个商品的基本信息\n        for index, product in enumerate(same_vendor_products[:3]):  # 只验证前3个商品\n            # 6.1 验证商品名称\n            product_name_ele = product.query_selector(\"div[data-testid='wid-product-card-title']\")\n            assert product_name_ele, f\"第{index + 1}个商品名称元素不存在\"\n            product_name = product_name_ele.text_content()\n            assert product_name, f\"第{index + 1}个商品名称为空\"\n    \n            # 6.2 验证商品价格\n            product_price_ele = product.query_selector(\"div[data-testid='wid-product-card-price']\")\n            assert product_price_ele, f\"第{index + 1}个商品价格元素不存在\"\n            product_price = product_price_ele.text_content()\n            assert product_price and product_price.startswith(\n                \"$\"), f\"第{index + 1}个商品价格格式不正确: {product_price}\"\n    \n            # 6.3 验证商品图片\n            product_img = product.query_selector(\"img\")\n            assert product_img, f\"第{index + 1}个商品图片不存在\"\n    \n            print(f\"商品{index + 1} - 名称: {product_name}, 价格: {product_price}\")\n    \n        # 7. 测试收藏按钮功能\n        first_product = same_vendor_products[0]\n        # hover到商品上显示收藏按钮\n        first_product.hover()\n        p.wait_for_timeout(1000)\n    \n        favorite_btn = first_product.query_selector(\"button[data-testid='btn-favorite']\")\n>       assert favorite_btn, \"收藏按钮不存在\"\nE       AssertionError: 收藏按钮不存在\nE       assert None\n\ntest_101480_dweb_pdp_same_vendor_ui_ux.py:77: AssertionError"}, "description": "\n        [101480]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 滚动到店铺推荐模块\n        4. 校验店铺推荐模块元素\n        5. 验证商品信息（名称、价格）\n        6. 测试收藏按钮功能\n        7. 测试加购按钮功能\n        ", "start": 1751007649830, "stop": 1751007666295, "uuid": "e9b5d1a3-1bf5-4ffd-84aa-9cc7aabae2c2", "historyId": "ba2c7a7bf4001cfafbcc89e6f461da13", "testCaseId": "ba2c7a7bf4001cfafbcc89e6f461da13", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_101480_dweb_pdp_same_vendor_ui_ux.TestDWebPDPSameVendorUIUX#test_101480_dweb_pdp_same_vendor_ui_ux", "labels": [{"name": "story", "value": "[101480]-Global+商品-验证商品PDP-店铺推荐模块"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_101480_dweb_pdp_same_vendor_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPSameVendorUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "11752-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_101480_dweb_pdp_same_vendor_ui_ux"}]}