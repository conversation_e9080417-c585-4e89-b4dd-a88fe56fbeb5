import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_product import dweb_pdp_ele
from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_page_pdp.dweb_page_pdp import DWebPDPPage


@allure.story("【PC-PDP】活动弹窗详细内容UI/UX验证")
class TestDWebPDPPromotionDrawerUIUX:
    pytestmark = [pytest.mark.pcpdp, pytest.mark.dweb_regression, pytest.mark.transaction]

    @allure.title("【PC-PDP】活动弹窗详细内容UI/UX验证")
    def test_dweb_pdp_promotion_drawer_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        PC-PDP活动弹窗详细内容UI/UX验证
        测试步骤：
        1. 访问指定商品PDP页面
        2. 检查是否存在活动模块
        3. 点击查看更多打开活动弹窗
        4. 验证活动名称和描述
        5. 验证商品展示（价格、图片、标题）
        6. 检查价格过滤器是否存在
        7. 如果存在过滤器，测试过滤功能
        8. 如果不存在过滤器，直接测试加购功能
        9. 验证活动进度信息
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 直接进入指定pdp页面
        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,
                               page_url="/product/Synear-Vegetable-Dumpling--Frozen-1/66118")

        p.wait_for_timeout(5000)

        # 2. 检查活动模块是否存在
        promotions_module = p.locator("div[data-testid='wid-pdp-product-promotions']")

        if not promotions_module.is_visible():
            print("商品页面不存在活动模块，跳过活动弹窗验证")
            pytest.skip("商品页面不存在活动模块")
            return

        print("发现商品活动模块，开始验证活动弹窗内容")

        # 3. 点击查看更多按钮打开活动弹窗
        promotion_button = p.locator("div[data-testid='btn-pdp-product-promotion']")
        assert promotion_button.is_visible(), "查看更多按钮不可见"

        promotion_button.click()
        p.wait_for_timeout(2000)

        # 验证活动弹窗出现
        promotion_drawer = p.locator("div[data-testid='wid-promotion-drawer-wrapper']")
        assert promotion_drawer.is_visible(), "活动弹窗未出现"

        print("活动弹窗已打开")

        # 4. 验证活动名称和描述
        self._verify_promotion_content(p)

        # 5. 验证商品展示
        self._verify_product_display(p)

        # 6. 检查价格过滤器并测试相应功能
        self._check_and_test_price_filters(p)

        # 7. 验证活动进度信息（如果存在）
        self._verify_promotion_progress(p)

        print("活动弹窗详细内容验证完成")

    def _verify_promotion_content(self, p: Page):
        """验证活动名称和描述"""
        print("开始验证活动内容...")

        # 验证活动标题
        promotion_title = p.locator("h3[data-testid='txt-promotion-drawer-title']")
        assert promotion_title.is_visible(), "活动标题不可见"
        title_text = promotion_title.text_content()
        assert title_text, "活动标题为空"

        # 验证标题样式
        title_classes = promotion_title.get_attribute("class")
        assert "enki-body-xl-medium" in title_classes, f"活动标题样式不正确: {title_classes}"

        print(f"活动标题验证完成: {title_text}")

        # 验证活动描述
        promotion_desc = p.locator("p[data-testid='txt-promotion-drawer-description']")
        assert promotion_desc.is_visible(), "活动描述不可见"
        desc_text = promotion_desc.text_content()
        assert desc_text, "活动描述为空"

        # 验证描述样式
        desc_classes = promotion_desc.get_attribute("class")
        assert "enki-body-sm-medium" in desc_classes, f"活动描述样式不正确: {desc_classes}"

        print(f"活动描述验证完成: {desc_text[:50]}...")

        # 验证活动规则（如果存在）
        promotion_rules = p.locator("p[data-testid='txt-promotion-drawer-rules']")
        if promotion_rules.is_visible():
            rules_text = promotion_rules.text_content()
            print(f"活动规则验证完成: {rules_text[:50]}...")
        else:
            print("活动规则不存在，跳过验证")

    def _verify_product_display(self, p: Page):
        """验证商品展示（修正版 - 使用动态ID匹配）"""
        print("开始验证商品展示...")

        # 验证商品列表容器
        product_list = p.locator("div[data-testid='wid-promotion-drawer-product-list']")
        assert product_list.is_visible(), "商品列表容器不可见"

        # 获取所有商品卡片（使用CSS选择器匹配以wid-product-card-item-开头的data-testid）
        product_cards = p.locator("div[data-testid^='wid-product-card-item-']").all()
        assert len(product_cards) > 0, "没有找到商品卡片"

        print(f"找到 {len(product_cards)} 个商品")

        # 验证前3个商品的详细信息
        for index, card in enumerate(product_cards[:3]):
            print(f"验证第 {index + 1} 个商品...")

            # 获取商品ID（从data-testid中提取）
            card_testid = card.get_attribute("data-testid")
            product_id = card_testid.replace("wid-product-card-item-", "") if card_testid else "unknown"
            print(f"商品ID: {product_id}")

            # 验证商品图片容器
            image_container = card.locator("div[data-testid='wid-product-card-image']")
            assert image_container.is_visible(), f"第{index + 1}个商品图片容器不可见"

            # 验证商品图片
            product_image = image_container.locator("img")
            assert product_image.is_visible(), f"第{index + 1}个商品图片不可见"

            image_alt = product_image.get_attribute("alt")
            image_src = product_image.get_attribute("src")
            assert image_alt, f"第{index + 1}个商品图片alt属性为空"
            assert image_src and "weeecdn.net" in image_src, f"第{index + 1}个商品图片src不正确: {image_src}"

            # 验证图片容器样式（105px x 105px）
            container_style = image_container.get_attribute("style")
            assert "width: 105px" in container_style, f"第{index + 1}个商品图片容器宽度不正确"
            assert "height: 105px" in container_style, f"第{index + 1}个商品图片容器高度不正确"

            # 验证商品标题
            product_title = card.locator("p[data-testid='wid-product-card-title']")
            assert product_title.is_visible(), f"第{index + 1}个商品标题不可见"
            title_text = product_title.text_content()
            assert title_text, f"第{index + 1}个商品标题为空"

            # 验证标题样式
            title_classes = product_title.get_attribute("class")
            assert "enki-body-base-medium" in title_classes, f"第{index + 1}个商品标题样式不正确"
            assert "line-clamp-2" in title_classes, f"第{index + 1}个商品标题行数限制不正确"

            # 验证商品当前价格
            product_price = card.locator("span[data-testid='wid-product-card-price-value']")
            assert product_price.is_visible(), f"第{index + 1}个商品价格不可见"
            price_text = product_price.text_content()
            assert price_text and price_text.startswith("$"), f"第{index + 1}个商品价格格式不正确: {price_text}"

            # 验证价格样式
            price_classes = product_price.get_attribute("class")
            assert "text-[#C92927]" in price_classes, f"第{index + 1}个商品价格颜色不正确"
            assert "font-semibold" in price_classes, f"第{index + 1}个商品价格字体不正确"

            # 验证商品原价（如果存在）
            base_price = card.locator("span[data-testid='wid-product-card-base-price']")
            if base_price.is_visible():
                base_price_text = base_price.text_content()
                base_price_classes = base_price.get_attribute("class")
                assert "line-through" in base_price_classes, f"第{index + 1}个商品原价样式不正确"
                print(f"第{index + 1}个商品原价: {base_price_text}")

            # 验证加购按钮（检查两个部分）
            # 1. 文本部分
            atc_text = card.locator("span[data-testid='btn-atc-plus']")
            assert atc_text.is_visible(), f"第{index + 1}个商品加购按钮文本不可见"
            atc_text_content = atc_text.text_content()
            assert "Add to cart" in atc_text_content, f"第{index + 1}个商品加购按钮文本不正确: {atc_text_content}"

            # 2. 图标部分
            atc_icon = card.locator("div[data-testid='btn-atc-plus'][aria-label='add-to-cart']")
            assert atc_icon.is_visible(), f"第{index + 1}个商品加购按钮图标不可见"

            # 验证加购按钮包含SVG图标
            atc_svg = atc_icon.locator("svg")
            assert atc_svg.is_visible(), f"第{index + 1}个商品加购按钮SVG图标不可见"

            print(f"第{index + 1}个商品验证完成 - ID: {product_id}, 标题: {title_text[:30]}..., 价格: {price_text}")

    def _check_and_test_price_filters(self, p: Page):
        """检查价格过滤器并测试相应功能"""
        print("检查价格过滤器...")

        # 检查价格过滤器是否存在
        price_filters_container = p.locator("div[data-testid='wid-promotion-drawer-price-filters']")

        if not price_filters_container.is_visible():
            print("价格过滤器不存在，直接测试加购功能")
            self._test_add_to_cart_without_filter(p)
            return

        print("发现价格过滤器，开始测试过滤功能")

        # 获取所有过滤器选项
        filter_options = p.locator("div[contains(@data-testid,'wid-promotion-drawer-price-filters-')]").all()
        assert len(filter_options) > 0, "没有找到价格过滤器选项"

        print(f"找到 {len(filter_options)} 个价格过滤器选项")

        # 记录过滤前的商品数量
        products_before = p.locator("div[data-testid^='wid-product-card-item-']").all()
        products_count_before = len(products_before)
        print(f"过滤前商品数量: {products_count_before}")

        # 测试点击第二个过滤器选项（如果存在）
        if len(filter_options) > 1:
            second_filter = filter_options[1]
            filter_text = second_filter.text_content()
            print(f"点击价格过滤器: {filter_text}")

            # 记录过滤器点击前的样式
            filter_classes_before = second_filter.get_attribute("class")

            # 点击过滤器
            second_filter.click()
            p.wait_for_timeout(2000)

            # 验证过滤器状态变化
            filter_classes_after = second_filter.get_attribute("class")
            print(f"过滤器样式变化: {filter_classes_before != filter_classes_after}")

            # 检查商品数量是否发生变化
            products_after = p.locator("div[data-testid^='wid-product-card-item-']").all()
            products_count_after = len(products_after)
            print(f"过滤后商品数量: {products_count_after}")

            # 测试加购功能
            if products_count_after > 0:
                self._test_add_to_cart_with_filter(p, products_after[0])
            else:
                print("过滤后没有商品，跳过加购测试")
        else:
            print("只有一个过滤器选项，跳过过滤测试")
            self._test_add_to_cart_without_filter(p)

    def _test_add_to_cart_without_filter(self, p: Page):
        """不使用过滤器时的加购测试（修正版）"""
        print("开始测试加购功能（无过滤器）...")

        # 获取第一个商品（使用CSS选择器）
        first_product = p.locator("div[data-testid^='wid-product-card-item-']").first
        if not first_product.is_visible():
            print("没有找到可加购的商品")
            return

        # 获取商品信息
        product_title = first_product.locator("p[data-testid='wid-product-card-title']").text_content()
        product_price = first_product.locator("span[data-testid='wid-product-card-price-value']").text_content()

        # 点击加购按钮（点击图标部分更可靠）
        atc_button = first_product.locator("div[data-testid='btn-atc-plus'][aria-label='add-to-cart']")
        atc_button.click()
        p.wait_for_timeout(2000)

        print(f"已加购商品: {product_title}, 价格: {product_price}")

    def _test_add_to_cart_with_filter(self, p: Page, product_element):
        """使用过滤器后的加购测试（修正版）"""
        print("开始测试加购功能（使用过滤器）...")

        # 获取商品信息
        product_title = product_element.locator("p[data-testid='wid-product-card-title']").text_content()
        product_price = product_element.locator("span[data-testid='wid-product-card-price-value']").text_content()

        # 点击加购按钮（点击图标部分更可靠）
        atc_button = product_element.locator("div[data-testid='btn-atc-plus'][aria-label='add-to-cart']")
        atc_button.click()
        p.wait_for_timeout(2000)

        print(f"已加购过滤后的商品: {product_title}, 价格: {product_price}")

    def _verify_promotion_progress(self, p: Page):
        """验证活动进度信息"""
        print("检查活动进度信息...")

        # 检查活动进度信息是否存在
        progress_info = p.locator("section[data-testid='txt-promotion-drawer-processing-info']")

        if not progress_info.is_visible():
            print("活动进度信息不存在，跳过验证")
            return

        print("发现活动进度信息，开始验证")

        # 验证进度信息的样式
        progress_classes = progress_info.get_attribute("class")
        assert "bg-white" in progress_classes, f"活动进度信息背景样式不正确: {progress_classes}"
        assert "sticky" in progress_classes, f"活动进度信息定位样式不正确: {progress_classes}"

        # 验证进度信息的内容
        progress_text = progress_info.text_content()
        print(f"活动进度信息: {progress_text}")

        # 验证进度信息包含SVG图标
        progress_svg = progress_info.locator("svg")
        if progress_svg.is_visible():
            print("活动进度信息包含图标")

        print("活动进度信息验证完成")