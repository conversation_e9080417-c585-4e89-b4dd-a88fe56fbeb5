import allure
import pytest

from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_page_global.dweb_page_mkpl_all_store.dweb_page_mkpl_all_store import MkplAllStorePage


@allure.story("【PC-All Stores】商品加购弹窗UI/UX验证")
class TestDWebPdpAddGlobalPopupUIUX:
    pytestmark = [pytest.mark.pcstores, pytest.mark.dweb_regression, pytest.mark.transaction]

    @allure.title("【PC-All Stores】商品加购弹窗UI/UX验证")
    def test_101862_dweb_pdp_add_global_sku_pop_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        PC All Stores商品加购弹窗UI/UX验证
        测试步骤：
        1. 访问All Stores页面
        2. 验证商家和商品展示
        3. 点击商品进入PDP页面
        4. 验证PDP页面商品信息
        5. 测试加购功能和弹窗
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 使用MkplAllStorePage进入All Stores页面
        all_store_page = MkplAllStorePage(p, pc_autotest_header)

        print("All Stores页面加载完成")

        # 2. 验证商家容器     
        seller_containers = p.get_by_test_id("wid-seller-container").all()
        assert len(seller_containers) > 0, "没有找到商家容器"

        print(f"找到 {len(seller_containers)} 个商家")

        # 3. 验证第一个商家的商品
        first_seller = seller_containers[0]

        # 验证商家名称
        seller_title = first_seller.get_by_test_id("wid-seller-title")
        assert seller_title.is_visible(), "商家标题不可见"
        seller_name = seller_title.text_content()
        print(f"商家名称: {seller_name}")

        # 4. 使用页面对象的方法点击商品进入PDP
        all_store_page._click_product_card()

        print("已通过页面对象方法点击商品，进入PDP页面")

        # 5. 验证PDP页面加载     
        product_name = p.get_by_test_id("wid-pdp-product-name")
        assert product_name.is_visible(), "PDP页面商品名称不可见"
        product_name_text = product_name.text_content()
        print(f"商品名称: {product_name_text}")

        # 验证商品价格
        current_price = p.get_by_test_id("wid-pdp-current-price")
        assert current_price.is_visible(), "商品当前价格不可见"
        price_text = current_price.text_content()
        print(f"商品价格: {price_text}")

        # 验证品牌信息
        brand = p.get_by_test_id("wid-pdp-brand")
        if brand.count() > 0 and brand.is_visible():
            brand_text = brand.text_content()
            print(f"商品品牌: {brand_text}")

        # 验证原价（如果存在）
        original_price = p.get_by_test_id("wid-pdp-original-price")
        if original_price.count() > 0 and original_price.is_visible():
            original_price_text = original_price.text_content()
            print(f"商品原价: {original_price_text}")

        # 验证价格标签（如果存在）
        price_tag = p.get_by_test_id("wid-pdp-price-tag-item")
        if price_tag.count() > 0 and price_tag.is_visible():
            tag_text = price_tag.text_content()
            print(f"价格标签: {tag_text}")

        # 6. 验证加购按钮
        add_cart_container = p.get_by_test_id("wid-add-cart-container")
        assert add_cart_container.is_visible(), "加购容器不可见"

        add_cart_btn = add_cart_container.get_by_test_id("btn-atc-plus")
        assert add_cart_btn.is_visible(), "加购按钮不可见"

        # 验证收藏按钮
        favorite_btn = p.get_by_test_id("btn-add-favorite")
        if favorite_btn.count() > 0:
            assert favorite_btn.is_visible(), "收藏按钮不可见"
            print("收藏按钮验证完成")

        print("PDP页面基本信息验证完成")

        # 7. 测试加购功能和弹窗（弹窗只停留3秒，只验证是否弹出）
        print("开始测试加购功能...")

        # 点击加购按钮
        add_cart_btn.click()

        # 快速检测弹窗是否出现（不验证弹窗内容）
        popup_detected = self._quick_detect_popup(p)

        if popup_detected:
            print("✓ 加购后检测到弹窗，功能正常")
        else:
            print("⚠ 未检测到加购弹窗，可能弹窗消失太快或未触发")

        # 8. 验证商家信息模块
        self._verify_seller_info_module(p)

        print("商品PDP加购弹窗验证完成")

    def _quick_detect_popup(self, p: Page):
        """
        快速检测加购弹窗是否出现（弹窗只停留3秒，不验证内容）
        """
        popup_detected = False

        # 立即检测首次加购弹窗
        try:
            first_popup = p.get_by_test_id("wid-pdp-first-add-popup")
            if first_popup.count() > 0:
                print("✓ 检测到首次加购弹窗")
                popup_detected = True
        except:
            pass

        # 立即检测再次加购弹窗
        try:
            rest_popup = p.get_by_test_id("wid-pdp-rest-add-popup")
            if rest_popup.count() > 0:
                print("✓ 检测到再次加购弹窗")
                popup_detected = True
        except:
            pass

        # 等待一小段时间再次检测（以防弹窗延迟出现）
        if not popup_detected:
            p.wait_for_timeout(1000)
            try:
                first_popup = p.get_by_test_id("wid-pdp-first-add-popup")
                rest_popup = p.get_by_test_id("wid-pdp-rest-add-popup")
                if first_popup.count() > 0 or rest_popup.count() > 0:
                    print("✓ 延迟检测到加购弹窗")
                    popup_detected = True
            except:
                pass

        return popup_detected

    def _verify_seller_info_module(self, p: Page):
        """验证商家信息模块"""
        print("验证商家信息模块...")

        # 验证商家介绍模块
        mkpl_intro = p.get_by_test_id("wid-pdp-mkpl-intro")
        if mkpl_intro.is_visible():
            # 验证商家logo
            seller_logo = p.get_by_test_id("wid-pdp-mkpl-intro-content-logo-image")
            if seller_logo.is_visible():
                logo_src = seller_logo.get_attribute("src")
                print(f"商家logo: {logo_src}")

            # 验证商家名称
            seller_name = p.get_by_test_id("wid-pdp-mkpl-intro-content-info-name")
            if seller_name.is_visible():
                name_text = seller_name.text_content()
                print(f"商家名称: {name_text}")

            # 验证销量信息
            sales_volume = p.get_by_test_id("wid-pdp-mkpl-intro-content-info-sales-volume-value")
            if sales_volume.is_visible():
                sales_text = sales_volume.text_content()
                print(f"销量信息: {sales_text}")

            # 验证配送信息
            descriptions = p.get_by_test_id("wid-pdp-mkpl-intro-content-info-descriptions")
            if descriptions.is_visible():
                desc_text = descriptions.text_content()
                print(f"配送信息: {desc_text}")

            # 验证联系商家按钮
            contact_seller = p.get_by_test_id("btn-pdp-contact-seller")
            if contact_seller.is_visible():
                contact_text = contact_seller.text_content()
                print(f"联系商家按钮: {contact_text}")

        print("商家信息模块验证完成")