import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_product import dweb_pdp_ele
from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_page_pdp.dweb_page_pdp import DWebPDPPage
from src.common.commonui import scroll_one_page_until


@allure.story("[101480]-Global+商品-验证商品PDP-店铺推荐模块")
class TestDWebPDPSameVendorUIUX:
    pytestmark = [pytest.mark.pcpdp, pytest.mark.dweb_regression, pytest.mark.transaction]

    @allure.title("[101480]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证")
    def test_101480_dweb_pdp_same_vendor_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        [101480]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证
        测试步骤：
        1. 访问指定商品PDP页面
        2. 校验页面基本元素
        3. 滚动到店铺推荐模块
        4. 校验店铺推荐模块元素
        5. 验证商品信息（名称、价格）
        6. 测试收藏按钮功能
        7. 测试加购按钮功能
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 直接进入指定pdp页面
        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,
                               page_url="/product/SK-II-Skinpower-Airy-Milky-Lotion/2038708")

        p.wait_for_timeout(5000)

        # 2. 滚动到店铺推荐模块
        scroll_one_page_until(p, dweb_pdp_ele.ele_pdp_same_vendor_card)

        # 3. 校验店铺推荐模块存在
        assert pdp_page.FE.ele(dweb_pdp_ele.ele_pdp_same_vendor_card).is_visible(), "店铺推荐模块不可见"

        # 4. 校验店铺推荐标题
        assert pdp_page.FE.ele(dweb_pdp_ele.ele_pdp_same_vendor_title).is_visible(), "店铺推荐标题不可见"

        # 5. 获取店铺推荐商品列表
        same_vendor_products = pdp_page.FE.eles(dweb_pdp_ele.ele_pdp_same_vendor_product_card)
        assert len(same_vendor_products) > 0, "店铺推荐商品列表为空"

        # 6. 验证每个商品的基本信息
        for index, product in enumerate(same_vendor_products[:3]):  # 只验证前3个商品
            # 6.1 验证商品名称
            product_name_ele = product.query_selector("div[data-testid='wid-product-card-title']")
            assert product_name_ele, f"第{index + 1}个商品名称元素不存在"
            product_name = product_name_ele.text_content()
            assert product_name, f"第{index + 1}个商品名称为空"

            # 6.2 验证商品价格
            product_price_ele = product.query_selector("div[data-testid='wid-product-card-price']")
            assert product_price_ele, f"第{index + 1}个商品价格元素不存在"
            product_price = product_price_ele.text_content()
            assert product_price and product_price.startswith(
                "$"), f"第{index + 1}个商品价格格式不正确: {product_price}"

            # 6.3 验证商品图片
            product_img = product.query_selector("img")
            assert product_img, f"第{index + 1}个商品图片不存在"

            print(f"商品{index + 1} - 名称: {product_name}, 价格: {product_price}")

        # 7. 测试收藏按钮功能
        first_product = same_vendor_products[0]
        '''
        # hover到商品上显示收藏按钮
        first_product.hover()
        p.wait_for_timeout(1000)

        favorite_btn = first_product.query_selector("button[data-testid='btn-favorite']")
        assert favorite_btn, "收藏按钮不存在"
        assert favorite_btn.is_visible(), "收藏按钮不可见"
        '''
        # 8. 测试加购按钮功能
        atc_btn = first_product.query_selector("div[data-testid='btn-atc-plus']")
        assert atc_btn, "加购按钮不存在"

        # 点击加购按钮
        atc_btn.click()
        p.wait_for_timeout(2000)

        # 验证加购成功（可以通过购物车数量变化或其他方式验证）
        print("店铺推荐商品加购测试完成")

        # 9. 测试轮播功能（如果有多个商品）
        if len(same_vendor_products) > 2:
            next_btn = pdp_page.FE.ele(dweb_pdp_ele.ele_pdp_same_vendor_next_btn)
            if next_btn.is_visible():
                next_btn.click()
                p.wait_for_timeout(1000)
                print("轮播下一页测试完成")