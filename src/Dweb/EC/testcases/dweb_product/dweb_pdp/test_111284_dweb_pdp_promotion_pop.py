import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_product import dweb_pdp_ele
from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_page_pdp.dweb_page_pdp import DWebPDPPage
from src.api.zipcode import switch_zipcode


@allure.story("[111284][dweb]promotion-pc端pdp活动页流程验证")
class TestDWebPDPProductPromotionsPopUIUX:
    pytestmark = [pytest.mark.pcpdp, pytest.mark.dweb_regression, pytest.mark.transaction]

    @allure.title("[111284][dweb]promotion-pc端pdp活动页流程验证")
    def test_111284_dweb_pdp_promotions_pop_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        PC-PDP商品活动模块UI/UX验证
        测试步骤：
        1. 访问指定商品PDP页面
        2. 校验页面基本元素
        3. 检查是否存在活动模块
        4. 如果存在活动，校验活动元素
        5. 验证活动图标、标题、描述
        6. 测试查看更多按钮功能
        7. 验证活动弹窗显示和关闭
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 直接进入指定pdp页面
        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,
                               page_url="/product/Kao-Liese-Prettia-Bubble-Hair-Color--Natural-Black/16772")

        p.wait_for_timeout(5000)

        # 2. 检查zipcode为99991
        zipcode = p.get_by_test_id('wid-modal-zip-code').text_content()
        if zipcode != '99991':
            switch_zipcode(headers=pc_autotest_header,zipcode="99991")
            p.reload()
        # 等待页面完全加载
        p.wait_for_load_state("networkidle", timeout=30000)
        # 2. 检查活动模块是否存在
        promotions_module = p.get_by_test_id("wid-pdp-product-promotions")
        if promotions_module.count() == 0 or not promotions_module.is_visible():
            print("商品页面不存在活动模块，跳过活动验证")
            print(f"当前页面URL: {p.url}")
            pytest.skip("商品页面不存在活动模块")
            return

        print("发现商品活动模块，开始验证活动内容")

        # 3. 校验活动模块存在
        assert promotions_module.is_visible(), "活动模块不可见"
        # 4. 验证所有活动项
        self._verify_all_promotions(p)

        # 5. 测试第一个活动的弹窗功能
        self._test_specific_promotion_popup(p)

        print("活动弹窗验证完成")
        switch_zipcode(headers=pc_autotest_header, zipcode="98011")

        #校验单个活动项
    def _verify_all_promotions(self, p: Page):
        """验证所有活动的基本信息"""
        promotion_items = p.get_by_test_id("wid-pdp-product-promotion").all()
        assert len(promotion_items) > 0, "活动项不存在"

        for i, promotion_item in enumerate(promotion_items):
            print(f"验证第 {i + 1} 个活动...")

            # 验证活动图标
            promotion_icon = promotion_item.get_by_test_id("wid-pdp-product-promotion-icon")
            assert promotion_icon.count() > 0, f"第{i + 1}个活动图标元素不存在"
            assert promotion_icon.is_visible(), f"第{i + 1}个活动图标不可见"

            # 验证活动标题
            promotion_title = promotion_item.get_by_test_id("wid-pdp-product-promotion-title")
            assert promotion_title.count() > 0, f"第{i + 1}个活动标题元素不存在"
            assert promotion_title.is_visible(), f"第{i + 1}个活动标题不可见"
            title_text = promotion_title.text_content()
            assert title_text, f"第{i + 1}个活动标题为空"

            # 验证活动描述
            promotion_desc = promotion_item.get_by_test_id("wid-pdp-product-promotion-rule-desc")
            assert promotion_desc.count() > 0, f"第{i + 1}个活动描述元素不存在"
            assert promotion_desc.is_visible(), f"第{i + 1}个活动描述不可见"
            desc_text = promotion_desc.text_content()
            assert desc_text, f"第{i + 1}个活动描述为空"

            # 验证查看更多按钮
            promotion_button = promotion_item.get_by_test_id("btn-pdp-product-promotion")
            assert promotion_button.count() > 0, f"第{i + 1}个活动按钮元素不存在"
            assert promotion_button.is_visible(), f"第{i + 1}个活动按钮不可见"

            print(f"第{i + 1}个活动验证完成 - 标题: {title_text}")
            # 验证按钮文本 - 在当前活动项内查找
            button_text_element = promotion_item.get_by_test_id("wid-pdp-product-promotion-button-text")
            assert button_text_element.count() > 0, f"第{i + 1}个活动按钮文本元素不存在"
            assert button_text_element.is_visible(), f"第{i + 1}个活动按钮文本不可见"
            button_text = button_text_element.text_content()
            assert button_text, f"第{i + 1}个活动按钮文本为空"


            print(f"查看更多按钮验证完成 - 文本: {button_text}")
            # 14. 输出验证结果摘要
            print("=" * 50)
            print("活动模块验证结果摘要:")
            print(f"✓ 活动标题: {title_text}")
            print(f"✓ 活动描述: {desc_text}")
            print(f"✓ 按钮文本: {button_text}")
            #  print(f"✓ 图标alt: {icon_alt}")
            print(f"✓ 弹窗功能: 正常")
            print("=" * 50)


    def _test_specific_promotion_popup(self, p: Page):
        """测试特定活动的弹窗功能"""
        promotion_items = p.get_by_test_id("wid-pdp-product-promotion").all()

        if len(promotion_items) == 0:
            pytest.skip("没有找到活动项")
            return

        # 选择第一个活动进行弹窗测试
        first_promotion = promotion_items[0]

        # 获取第一个活动的标题用于日志
        title_element = first_promotion.get_by_test_id("wid-pdp-product-promotion-title")
        title_text = title_element.text_content() if title_element.count() > 0 else "未知活动"

        print(f"测试活动弹窗: {title_text}")

        # 点击第一个活动的按钮
        promotion_button = first_promotion.get_by_test_id("btn-pdp-product-promotion")
        assert promotion_button.count() > 0, "活动按钮不存在"
        promotion_button.click()
        p.wait_for_timeout(2000)

        # 验证弹窗出现
        promotion_drawer = p.get_by_test_id("wid-promotion-drawer-wrapper")
        assert promotion_drawer.count() > 0, "活动弹窗元素不存在"
        assert promotion_drawer.is_visible(), "活动弹窗未出现"

        print(f"活动弹窗测试完成: {title_text}")
        print("活动弹窗显示验证完成")


        # 测试弹窗关闭功能
        # 尝试通过ESC键关闭
        p.keyboard.press("Escape")
        p.wait_for_timeout(1000)

        # 验证弹窗是否关闭
        try:
            drawer_state_after = promotion_drawer.get_attribute("data-state")
            if drawer_state_after == "open":
                # 如果ESC键无效，尝试点击外部区域
                p.click("body", position={"x": 100, "y": 100})
                p.wait_for_timeout(1000)
                print("通过点击外部区域关闭弹窗")
            else:
                print("通过ESC键关闭弹窗")
        except Exception as e:
            print(f"弹窗关闭测试: {str(e)}")

        print("活动弹窗关闭测试完成")


