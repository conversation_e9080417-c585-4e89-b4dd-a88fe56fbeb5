import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_product import dweb_pdp_ele
from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_page_pdp.dweb_page_pdp import DWebPDPPage


@allure.story("[111284][dweb]promotion-pc端pdp活动页流程验证")
class TestDWebPDPProductPromotionsPopUIUX:
    pytestmark = [pytest.mark.pcpdp, pytest.mark.dweb_regression, pytest.mark.transaction]

    @allure.title("[111284][dweb]promotion-pc端pdp活动页流程验证")
    def test_111284_dweb_pdp_promotions_pop_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        PC-PDP商品活动模块UI/UX验证
        测试步骤：
        1. 访问指定商品PDP页面
        2. 校验页面基本元素
        3. 检查是否存在活动模块
        4. 如果存在活动，校验活动元素
        5. 验证活动图标、标题、描述
        6. 测试查看更多按钮功能
        7. 验证活动弹窗显示和关闭
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 直接进入指定pdp页面
        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,
                               page_url="/product/Power-Breakfast-Mix-Red-Beans-Dates-Goji/2252160")

        p.wait_for_timeout(5000)

        # 2. 检查活动模块是否存在 - 使用get_by_test_id，添加等待和调试
        # 等待页面完全加载
        p.wait_for_load_state("networkidle", timeout=30000)

        promotions_module = p.get_by_test_id("wid-pdp-product-promotions")

        if promotions_module.count() == 0 or not promotions_module.is_visible():
            print("商品页面不存在活动模块，跳过活动验证")
            print(f"当前页面URL: {p.url}")
            pytest.skip("商品页面不存在活动模块")
            return

        print("发现商品活动模块，开始验证活动内容")

        # 3. 校验活动模块存在
        assert promotions_module.is_visible(), "活动模块不可见"

        # 4. 校验单个活动项 - 使用get_by_test_id
        promotion_item = p.get_by_test_id("wid-pdp-product-promotion")
        assert promotion_item.is_visible(), "活动项不可见"

        # 验证活动项的样式
        item_classes = promotion_item.get_attribute("class")
        assert "flex items-center justify-between py-2.5" in item_classes, f"活动项样式不正确: {item_classes}"

        # 5. 校验活动图标 - 使用get_by_test_id
        promotion_icon = p.get_by_test_id("wid-pdp-product-promotion-icon")
        assert promotion_icon.count() > 0, "活动图标元素不存在"
        assert promotion_icon.is_visible(), "活动图标不可见"

        # 验证图标属性
        icon_alt = promotion_icon.get_attribute("alt")
        icon_src = promotion_icon.get_attribute("src")
        assert icon_alt, f"活动图标alt属性为空: {icon_alt}"
        assert icon_src, f"活动图标src属性为空: {icon_src}"
        assert "weeecdn.net" in icon_src or "static.weeecdn.net" in icon_src, f"活动图标src不正确: {icon_src}"

        print(f"活动图标验证完成 - alt: {icon_alt}")

        # 6. 校验活动标题 - 使用get_by_test_id
        promotion_title = p.get_by_test_id("wid-pdp-product-promotion-title")
        assert promotion_title.count() > 0, "活动标题元素不存在"
        assert promotion_title.is_visible(), "活动标题不可见"
        title_text = promotion_title.text_content()
        assert title_text, "活动标题为空"

        # 验证标题样式
        title_classes = promotion_title.get_attribute("class")
        assert "enki-body-2xs-medium" in title_classes, f"活动标题样式不正确: {title_classes}"
        assert "text-black" in title_classes, f"活动标题颜色不正确: {title_classes}"

        print(f"活动标题验证完成 - 标题: {title_text}")

        # 7. 校验活动规则描述 - 使用get_by_test_id
        promotion_desc = p.get_by_test_id("wid-pdp-product-promotion-rule-desc")
        assert promotion_desc.count() > 0, "活动规则描述元素不存在"
        assert promotion_desc.is_visible(), "活动规则描述不可见"
        desc_text = promotion_desc.text_content()
        assert desc_text, "活动规则描述为空"

        # 验证描述样式
        desc_classes = promotion_desc.get_attribute("class")
        assert "enki-body-3xs" in desc_classes, f"活动描述样式不正确: {desc_classes}"
        assert "text-[#777]" in desc_classes, f"活动描述颜色不正确: {desc_classes}"

        print(f"活动描述验证完成 - 描述: {desc_text}")

        # 8. 校验查看更多按钮 - 使用get_by_test_id
        promotion_button = p.get_by_test_id("btn-pdp-product-promotion")
        assert promotion_button.is_visible(), "查看更多按钮不可见"

        # 验证按钮样式
        button_classes = promotion_button.get_attribute("class")
        assert "flex items-center" in button_classes, f"查看更多按钮样式不正确: {button_classes}"
        assert "cursor-pointer" in button_classes, f"查看更多按钮缺少cursor样式: {button_classes}"
        assert "text-link-base-1" in button_classes, f"查看更多按钮颜色不正确: {button_classes}"

        # 9. 校验按钮文本 - 使用get_by_test_id
        button_text_element = p.get_by_test_id("wid-pdp-product-promotion-button-text")
        assert button_text_element.is_visible(), "查看更多按钮文本不可见"
        button_text = button_text_element.text_content()
        assert button_text, "查看更多按钮文本为空"

        # 验证按钮包含箭头图标
        button_arrow = promotion_button.locator("svg")
        assert button_arrow.is_visible(), "查看更多按钮箭头图标不可见"

        print(f"查看更多按钮验证完成 - 文本: {button_text}")

        # 10. 测试活动弹窗功能
        # 点击查看更多按钮
        promotion_button.click()
        p.wait_for_timeout(2000)

        # 11. 校验活动弹窗出现 - 使用get_by_test_id
        promotion_drawer = p.get_by_test_id("wid-promotion-drawer-wrapper")
        assert promotion_drawer.is_visible(), "活动弹窗未出现"

        # 验证弹窗属性
        drawer_role = promotion_drawer.get_attribute("role")
        drawer_state = promotion_drawer.get_attribute("data-state")
        assert drawer_role == "dialog", f"弹窗role属性不正确: {drawer_role}"
        assert drawer_state == "open", f"弹窗状态不正确: {drawer_state}"

        # 验证弹窗样式
        drawer_classes = promotion_drawer.get_attribute("class")
        expected_classes = ["bg-white", "flex", "flex-col", "h-full", "fixed"]
        for expected_class in expected_classes:
            assert expected_class in drawer_classes, f"弹窗缺少样式类'{expected_class}': {drawer_classes}"

        # 验证弹窗位置和尺寸
        drawer_width = promotion_drawer.evaluate("element => element.style.width")
        assert "448px" in drawer_width or promotion_drawer.evaluate("element => element.offsetWidth") >= 400, "弹窗宽度不正确"

        print("活动弹窗显示验证完成")

        # 12. 验证弹窗内容（可选）
        # 可以进一步验证弹窗内的具体内容

        # 13. 测试弹窗关闭功能
        # 尝试通过ESC键关闭
        p.keyboard.press("Escape")
        p.wait_for_timeout(1000)

        # 验证弹窗是否关闭
        try:
            drawer_state_after = promotion_drawer.get_attribute("data-state")
            if drawer_state_after == "open":
                # 如果ESC键无效，尝试点击外部区域
                p.click("body", position={"x": 100, "y": 100})
                p.wait_for_timeout(1000)
                print("通过点击外部区域关闭弹窗")
            else:
                print("通过ESC键关闭弹窗")
        except Exception as e:
            print(f"弹窗关闭测试: {str(e)}")

        print("活动弹窗关闭测试完成")

        # 14. 输出验证结果摘要
        print("=" * 50)
        print("活动模块验证结果摘要:")
        print(f"✓ 活动标题: {title_text}")
        print(f"✓ 活动描述: {desc_text}")
        print(f"✓ 按钮文本: {button_text}")
        print(f"✓ 图标alt: {icon_alt}")
        print(f"✓ 弹窗功能: 正常")
        print("=" * 50)
