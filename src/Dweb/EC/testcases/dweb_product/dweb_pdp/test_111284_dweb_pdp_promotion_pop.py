import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_product import dweb_pdp_ele
from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_page_pdp.dweb_page_pdp import DWebPDPPage


@allure.story("[111284][dweb]promotion-pc端pdp活动页流程验证")
class TestDWebPDPProductPromotionsUIUX:
    pytestmark = [pytest.mark.pcpdp, pytest.mark.dweb_regression, pytest.mark.transaction]

    @allure.title("[111284][dweb]promotion-pc端pdp活动页流程验证")
    def test_111284_dweb_pdp_promotions_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        PC-PDP商品活动模块UI/UX验证
        测试步骤：
        1. 访问指定商品PDP页面
        2. 校验页面基本元素
        3. 检查是否存在活动模块
        4. 如果存在活动，校验活动元素
        5. 验证活动图标、标题、描述
        6. 测试查看更多按钮功能
        7. 验证活动弹窗显示和关闭
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 直接进入指定pdp页面
        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,
                               page_url="/product/Synear-Shrimp-Pork-and-Scallop-Dumplings-Frozen-1/23847")

        p.wait_for_timeout(5000)

        # 2. 检查活动模块是否存在
        promotions_module = p.locator("div[data-testid='wid-pdp-product-promotions']")

        if not promotions_module.is_visible():
            print("商品页面不存在活动模块，跳过活动验证")
            pytest.skip("商品页面不存在活动模块")
            return

        print("发现商品活动模块，开始验证活动内容")

        # 3. 校验活动模块存在
        assert promotions_module.is_visible(), "活动模块不可见"

        # 4. 校验单个活动项
        promotion_item = p.locator("div[data-testid='wid-pdp-product-promotion']")
        assert promotion_item.is_visible(), "活动项不可见"

        # 验证活动项的样式
        item_classes = promotion_item.get_attribute("class")
        assert "flex items-center justify-between py-2.5" in item_classes, f"活动项样式不正确: {item_classes}"

        # 5. 校验活动图标
        promotion_icon = p.locator("img[data-testid='wid-pdp-product-promotion-icon']")
        assert promotion_icon.is_visible(), "活动图标不可见"

        # 验证图标属性
        icon_alt = promotion_icon.get_attribute("alt")
        icon_src = promotion_icon.get_attribute("src")
        assert icon_alt, f"活动图标alt属性为空: {icon_alt}"
        assert icon_src, f"活动图标src属性为空: {icon_src}"
        assert "weeecdn.net" in icon_src or "static.weeecdn.net" in icon_src, f"活动图标src不正确: {icon_src}"

        print(f"活动图标验证完成 - alt: {icon_alt}")

        # 6. 校验活动标题
        promotion_title = p.locator("div[data-testid='wid-pdp-product-promotion-title']")
        assert promotion_title.is_visible(), "活动标题不可见"
        title_text = promotion_title.text_content()
        assert title_text, "活动标题为空"

        # 验证标题样式
        title_classes = promotion_title.get_attribute("class")
        assert "enki-body-2xs-medium" in title_classes, f"活动标题样式不正确: {title_classes}"
        assert "text-black" in title_classes, f"活动标题颜色不正确: {title_classes}"

        print(f"活动标题验证完成 - 标题: {title_text}")

        # 7. 校验活动规则描述
        promotion_desc = p.locator("p[data-testid='wid-pdp-product-promotion-rule-desc']")
        assert promotion_desc.is_visible(), "活动规则描述不可见"
        desc_text = promotion_desc.text_content()
        assert desc_text, "活动规则描述为空"

        # 验证描述样式
        desc_classes = promotion_desc.get_attribute("class")
        assert "enki-body-3xs" in desc_classes, f"活动描述样式不正确: {desc_classes}"
        assert "text-[#777]" in desc_classes, f"活动描述颜色不正确: {desc_classes}"

        print(f"活动描述验证完成 - 描述: {desc_text}")

        # 8. 校验查看更多按钮
        promotion_button = p.locator("div[data-testid='btn-pdp-product-promotion']")
        assert promotion_button.is_visible(), "查看更多按钮不可见"

        # 验证按钮样式
        button_classes = promotion_button.get_attribute("class")
        assert "flex items-center" in button_classes, f"查看更多按钮样式不正确: {button_classes}"
        assert "cursor-pointer" in button_classes, f"查看更多按钮缺少cursor样式: {button_classes}"
        assert "text-link-base-1" in button_classes, f"查看更多按钮颜色不正确: {button_classes}"

        # 9. 校验按钮文本
        button_text_element = p.locator("span[data-testid='wid-pdp-product-promotion-button-text']")
        assert button_text_element.is_visible(), "查看更多按钮文本不可见"
        button_text = button_text_element.text_content()
        assert button_text, "查看更多按钮文本为空"

        # 验证按钮包含箭头图标
        button_arrow = promotion_button.locator("svg")
        assert button_arrow.is_visible(), "查看更多按钮箭头图标不可见"

        print(f"查看更多按钮验证完成 - 文本: {button_text}")

        # 10. 测试活动弹窗功能
        # 点击查看更多按钮
        promotion_button.click()
        p.wait_for_timeout(2000)

        # 11. 校验活动弹窗出现
        promotion_drawer = p.locator("div[data-testid='wid-promotion-drawer-wrapper']")
        assert promotion_drawer.is_visible(), "活动弹窗未出现"

        # 验证弹窗属性
        drawer_role = promotion_drawer.get_attribute("role")
        drawer_state = promotion_drawer.get_attribute("data-state")
        assert drawer_role == "dialog", f"弹窗role属性不正确: {drawer_role}"
        assert drawer_state == "open", f"弹窗状态不正确: {drawer_state}"

        # 验证弹窗样式
        drawer_classes = promotion_drawer.get_attribute("class")
        expected_classes = ["bg-white", "flex", "flex-col", "h-full", "fixed"]
        for expected_class in expected_classes:
            assert expected_class in drawer_classes, f"弹窗缺少样式类'{expected_class}': {drawer_classes}"

        # 验证弹窗位置和尺寸
        drawer_width = promotion_drawer.evaluate("element => element.style.width")
        assert "448px" in drawer_width or promotion_drawer.evaluate("element => element.offsetWidth") >= 400, "弹窗宽度不正确"

        print("活动弹窗显示验证完成")

        # 12. 验证弹窗内容（可选）
        # 可以进一步验证弹窗内的具体内容

        # 13. 测试弹窗关闭功能
        # 尝试通过ESC键关闭
        p.keyboard.press("Escape")
        p.wait_for_timeout(1000)

        # 验证弹窗是否关闭
        try:
            drawer_state_after = promotion_drawer.get_attribute("data-state")
            if drawer_state_after == "open":
                # 如果ESC键无效，尝试点击外部区域
                p.click("body", position={"x": 100, "y": 100})
                p.wait_for_timeout(1000)
                print("通过点击外部区域关闭弹窗")
            else:
                print("通过ESC键关闭弹窗")
        except Exception as e:
            print(f"弹窗关闭测试: {str(e)}")

        print("活动弹窗关闭测试完成")

        # 14. 输出验证结果摘要
        print("=" * 50)
        print("活动模块验证结果摘要:")
        print(f"✓ 活动标题: {title_text}")
        print(f"✓ 活动描述: {desc_text}")
        print(f"✓ 按钮文本: {button_text}")
        print(f"✓ 图标alt: {icon_alt}")
        print(f"✓ 弹窗功能: 正常")
        print("=" * 50)

    def _verify_promotion_content(self, p: Page):
        """验证活动名称和描述"""
        print("开始验证活动内容...")

        # 验证活动标题
        promotion_title = p.locator("h3[data-testid='txt-promotion-drawer-title']")
        assert promotion_title.is_visible(), "活动标题不可见"
        title_text = promotion_title.text_content()
        assert title_text, "活动标题为空"

        # 验证标题样式
        title_classes = promotion_title.get_attribute("class")
        assert "enki-body-xl-medium" in title_classes, f"活动标题样式不正确: {title_classes}"

        print(f"活动标题验证完成: {title_text}")

        # 验证活动描述
        promotion_desc = p.locator("p[data-testid='txt-promotion-drawer-description']")
        assert promotion_desc.is_visible(), "活动描述不可见"
        desc_text = promotion_desc.text_content()
        assert desc_text, "活动描述为空"

        # 验证描述样式
        desc_classes = promotion_desc.get_attribute("class")
        assert "enki-body-sm-medium" in desc_classes, f"活动描述样式不正确: {desc_classes}"

        print(f"活动描述验证完成: {desc_text[:50]}...")

        # 验证活动规则（如果存在）
        promotion_rules = p.locator("p[data-testid='txt-promotion-drawer-rules']")
        if promotion_rules.is_visible():
            rules_text = promotion_rules.text_content()
            print(f"活动规则验证完成: {rules_text[:50]}...")
        else:
            print("活动规则不存在，跳过验证")

    def _verify_product_display(self, p: Page):
        """验证商品展示"""
        print("开始验证商品展示...")

        # 验证商品列表容器
        product_list = p.locator("div[data-testid='wid-promotion-drawer-product-list']")
        assert product_list.is_visible(), "商品列表容器不可见"

        # 获取所有商品卡片
        product_cards = p.locator("div[contains(@data-testid,'wid-product-card-item')]").all()
        assert len(product_cards) > 0, "没有找到商品卡片"

        print(f"找到 {len(product_cards)} 个商品")

        # 验证前3个商品的详细信息
        for index, card in enumerate(product_cards[:3]):
            print(f"验证第 {index + 1} 个商品...")

            # 验证商品图片
            product_image = card.locator("div[data-testid='wid-product-card-image'] img")
            assert product_image.is_visible(), f"第{index + 1}个商品图片不可见"

            image_alt = product_image.get_attribute("alt")
            image_src = product_image.get_attribute("src")
            assert image_alt, f"第{index + 1}个商品图片alt属性为空"
            assert image_src and "weeecdn.net" in image_src, f"第{index + 1}个商品图片src不正确: {image_src}"

            # 验证商品标题
            product_title = card.locator("p[data-testid='wid-product-card-title']")
            assert product_title.is_visible(), f"第{index + 1}个商品标题不可见"
            title_text = product_title.text_content()
            assert title_text, f"第{index + 1}个商品标题为空"

            # 验证商品价格
            product_price = card.locator("span[data-testid='wid-product-card-price-value']")
            assert product_price.is_visible(), f"第{index + 1}个商品价格不可见"
            price_text = product_price.text_content()
            assert price_text and price_text.startswith("$"), f"第{index + 1}个商品价格格式不正确: {price_text}"

            # 验证商品原价（如果存在）
            base_price = card.locator("span[data-testid='wid-product-card-base-price']")
            if base_price.is_visible():
                base_price_text = base_price.text_content()
                print(f"第{index + 1}个商品原价: {base_price_text}")

            # 验证加购按钮
            atc_button = card.locator("div[data-testid='btn-atc-plus']")
            assert atc_button.is_visible(), f"第{index + 1}个商品加购按钮不可见"

            print(f"第{index + 1}个商品验证完成 - 标题: {title_text[:30]}..., 价格: {price_text}")

    def _check_and_test_price_filters(self, p: Page):
        """检查价格过滤器并测试相应功能"""
        print("检查价格过滤器...")

        # 检查价格过滤器是否存在
        price_filters_container = p.locator("div[data-testid='wid-promotion-drawer-price-filters']")

        if not price_filters_container.is_visible():
            print("价格过滤器不存在，直接测试加购功能")
            self._test_add_to_cart_without_filter(p)
            return

        print("发现价格过滤器，开始测试过滤功能")

        # 获取所有过滤器选项
        filter_options = p.locator("div[contains(@data-testid,'wid-promotion-drawer-price-filters-')]").all()
        assert len(filter_options) > 0, "没有找到价格过滤器选项"

        print(f"找到 {len(filter_options)} 个价格过滤器选项")

        # 记录过滤前的商品数量
        products_before = p.locator("div[contains(@data-testid,'wid-product-card-item')]").all()
        products_count_before = len(products_before)
        print(f"过滤前商品数量: {products_count_before}")

        # 测试点击第二个过滤器选项（如果存在）
        if len(filter_options) > 1:
            second_filter = filter_options[1]
            filter_text = second_filter.text_content()
            print(f"点击价格过滤器: {filter_text}")

            # 记录过滤器点击前的样式
            filter_classes_before = second_filter.get_attribute("class")

            # 点击过滤器
            second_filter.click()
            p.wait_for_timeout(2000)

            # 验证过滤器状态变化
            filter_classes_after = second_filter.get_attribute("class")
            print(f"过滤器样式变化: {filter_classes_before != filter_classes_after}")

            # 检查商品数量是否发生变化
            products_after = p.locator("div[contains(@data-testid,'wid-product-card-item')]").all()
            products_count_after = len(products_after)
            print(f"过滤后商品数量: {products_count_after}")

            # 测试加购功能
            if products_count_after > 0:
                self._test_add_to_cart_with_filter(p, products_after[0])
            else:
                print("过滤后没有商品，跳过加购测试")
        else:
            print("只有一个过滤器选项，跳过过滤测试")
            self._test_add_to_cart_without_filter(p)

    def _test_add_to_cart_without_filter(self, p: Page):
        """不使用过滤器时的加购测试"""
        print("开始测试加购功能（无过滤器）...")

        # 获取第一个商品
        first_product = p.locator("div[contains(@data-testid,'wid-product-card-item')]").first
        if not first_product.is_visible():
            print("没有找到可加购的商品")
            return

        # 获取商品信息
        product_title = first_product.locator("p[data-testid='wid-product-card-title']").text_content()
        product_price = first_product.locator("span[data-testid='wid-product-card-price-value']").text_content()

        # 点击加购按钮
        atc_button = first_product.locator("div[data-testid='btn-atc-plus']")
        atc_button.click()
        p.wait_for_timeout(2000)

        print(f"已加购商品: {product_title}, 价格: {product_price}")

    def _test_add_to_cart_with_filter(self, p: Page, product_element):
        """使用过滤器后的加购测试"""
        print("开始测试加购功能（使用过滤器）...")

        # 获取商品信息
        product_title = product_element.locator("p[data-testid='wid-product-card-title']").text_content()
        product_price = product_element.locator("span[data-testid='wid-product-card-price-value']").text_content()

        # 点击加购按钮
        atc_button = product_element.locator("div[data-testid='btn-atc-plus']")
        atc_button.click()
        p.wait_for_timeout(2000)

        print(f"已加购过滤后的商品: {product_title}, 价格: {product_price}")

    def _verify_promotion_progress(self, p: Page):
        """验证活动进度信息"""
        print("检查活动进度信息...")

        # 检查活动进度信息是否存在
        progress_info = p.locator("section[data-testid='txt-promotion-drawer-processing-info']")

        if not progress_info.is_visible():
            print("活动进度信息不存在，跳过验证")
            return

        print("发现活动进度信息，开始验证")

        # 验证进度信息的样式
        progress_classes = progress_info.get_attribute("class")
        assert "bg-white" in progress_classes, f"活动进度信息背景样式不正确: {progress_classes}"
        assert "sticky" in progress_classes, f"活动进度信息定位样式不正确: {progress_classes}"

        # 验证进度信息的内容
        progress_text = progress_info.text_content()
        print(f"活动进度信息: {progress_text}")

        # 验证进度信息包含SVG图标
        progress_svg = progress_info.locator("svg")
        if progress_svg.is_visible():
            print("活动进度信息包含图标")

        print("活动进度信息验证完成")