import allure
import pytest

from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_page_pdp.dweb_page_pdp import DWebPDPPage
from src.api.zipcode import switch_zipcode


@allure.story("【PC-PDP】活动弹窗价格过滤器UI/UX验证")
class TestDWebPDPPromotionFilterUIUX:
    pytestmark = [pytest.mark.pcpdp, pytest.mark.dweb_regression, pytest.mark.transaction]

    @allure.title("【PC-PDP】活动弹窗价格过滤器UI/UX验证")
    def test_dweb_pdp_promotion_filter_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        PC-PDP活动弹窗价格过滤器UI/UX验证
        测试步骤：
        1. 访问指定商品PDP页面
        2. 检查是否存在活动模块
        3. 点击活动按钮打开活动弹窗
        4. 验证弹窗中的商品信息
        5. 检查是否存在价格过滤器
        6. 测试价格过滤器功能
        7. 验证过滤结果的准确性
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 直接进入指定pdp页面
        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,
                               page_url="/product/HM-Rock-Sugar-Crispy-Water-Chestnut/107200")

        p.wait_for_timeout(5000)
        switch_zipcode(pc_autotest_header, '94538')
        # 2. 检查活动模块是否存在
        promotions_module = p.get_by_test_id("wid-pdp-product-promotions")

        if promotions_module.count() == 0 or not promotions_module.is_visible():
            print("商品页面不存在活动模块，跳过活动弹窗验证")
            pytest.skip("商品页面不存在活动模块")
            return

        print("发现商品活动模块，开始验证活动弹窗内容")

        # 3. 点击活动按钮打开活动弹窗
        promotion_button = p.get_by_test_id("btn-pdp-product-promotion")
        assert promotion_button.count() > 0, "活动按钮不存在"
        assert promotion_button.is_visible(), "活动按钮不可见"

        promotion_button.click()
        p.wait_for_timeout(2000)

        # 验证活动弹窗出现
        promotion_drawer = p.get_by_test_id("wid-promotion-drawer-wrapper")
        assert promotion_drawer.count() > 0, "活动弹窗元素不存在"
        assert promotion_drawer.is_visible(), "活动弹窗未出现"

        print("活动弹窗已打开")

        # 4. 验证弹窗中的商品信息
        self._verify_promotion_products(p)

        # 5. 检查价格过滤器并测试功能
        self._check_and_test_price_filters(p)

        print("活动弹窗价格过滤器验证完成")

    def _verify_promotion_products(self, p: Page):
        """验证活动弹窗中的商品信息"""
        print("开始验证活动弹窗中的商品信息...")

        # 验证商品列表容器
        product_list = p.get_by_test_id("wid-promotion-drawer-product-list")
        assert product_list.count() > 0, "商品列表容器不存在"
        assert product_list.is_visible(), "商品列表容器不可见"

        # 获取所有商品卡片
        product_cards = p.get_by_test_id("wid-product-card-promotion-drawer-item").all()
        assert len(product_cards) > 0, "没有找到商品卡片"

        print(f"找到 {len(product_cards)} 个商品")

        # 验证前3个商品的详细信息
        for index, card in enumerate(product_cards[:3]):
            print(f"验证第 {index + 1} 个商品...")

            # 验证商品图片
            product_image = card.get_by_test_id("wid-product-card-image")
            assert product_image.count() > 0, f"第{index + 1}个商品图片容器不存在"
            assert product_image.is_visible(), f"第{index + 1}个商品图片容器不可见"

            # 验证图片元素
            img_element = product_image.locator("img")
            assert img_element.count() > 0, f"第{index + 1}个商品图片不存在"
            assert img_element.is_visible(), f"第{index + 1}个商品图片不可见"

            image_alt = img_element.get_attribute("alt")
            image_src = img_element.get_attribute("src")
            assert image_alt, f"第{index + 1}个商品图片alt属性为空"
            assert image_src and "weeecdn" in image_src, f"第{index + 1}个商品图片src不正确: {image_src}"

            # 验证商品标题
            product_title = card.get_by_test_id("wid-product-card-title")
            assert product_title.count() > 0, f"第{index + 1}个商品标题不存在"
            assert product_title.is_visible(), f"第{index + 1}个商品标题不可见"
            title_text = product_title.text_content()
            assert title_text, f"第{index + 1}个商品标题为空"

            # 验证商品价格
            product_price = card.get_by_test_id("wid-product-card-price-value")
            assert product_price.count() > 0, f"第{index + 1}个商品价格不存在"
            assert product_price.is_visible(), f"第{index + 1}个商品价格不可见"
            price_text = product_price.text_content()
            assert price_text and price_text.startswith("$"), f"第{index + 1}个商品价格格式不正确: {price_text}"

            # 验证加购按钮或售罄状态
            atc_btn = card.get_by_test_id("btn-atc-plus")
            sold_out_indicator = card.get_by_test_id("wid-sold-out")

            if sold_out_indicator.count() > 0 and sold_out_indicator.is_visible():
                # 商品售罄
                print(f"第{index + 1}个商品已售罄")
                sold_out_text = sold_out_indicator.text_content()
                print(f"售罄提示: {sold_out_text}")

                # 验证售罄状态下不应该有加购按钮
                assert atc_btn.count() == 0 or not atc_btn.is_visible(), f"第{index + 1}个商品已售罄但仍显示加购按钮"

            elif atc_btn.count() > 0:
                # 商品有库存，验证加购按钮
                first_atc_btn = atc_btn.first
                assert first_atc_btn.is_visible(), f"第{index + 1}个商品加购按钮不可见"
                print(f"第{index + 1}个商品加购按钮正常")

            else:
                # 既没有加购按钮也没有售罄提示，可能是其他状态
                print(f"⚠ 第{index + 1}个商品既没有加购按钮也没有售罄提示，请检查商品状态")

            print(f"第{index + 1}个商品验证完成 - 标题: {title_text[:30]}..., 价格: {price_text}")

    def _check_and_test_price_filters(self, p: Page):
        """检查价格过滤器并测试功能"""
        print("检查价格过滤器...")

        # 检查价格过滤器容器是否存在
        price_filters_container = p.get_by_test_id("wid-promotion-drawer-price-filters")

        if price_filters_container.count() == 0 or not price_filters_container.is_visible():
            print("价格过滤器不存在，跳过过滤器测试")
            return

        print("发现价格过滤器，开始测试过滤功能")

        # 获取所有过滤器选项
        filter_items = p.get_by_test_id("wid-promotion-drawer-price-filter-item").all()
        assert len(filter_items) > 0, "没有找到价格过滤器选项"

        print(f"找到 {len(filter_items)} 个价格过滤器选项")

        # 记录所有过滤器选项的文本
        filter_texts = []
        for i, filter_item in enumerate(filter_items):
            filter_text = filter_item.text_content()
            filter_texts.append(filter_text)
            print(f"过滤器选项 {i + 1}: {filter_text}")

        # 记录过滤前的商品数量和第一个商品价格
        products_before = p.get_by_test_id("wid-product-card-promotion-drawer-item").all()
        products_count_before = len(products_before)
        print(f"过滤前商品数量: {products_count_before}")

        # 测试点击非"All"的过滤器选项
        target_filter = None
        target_filter_text = ""

        for i, filter_item in enumerate(filter_items):
            filter_text = filter_item.text_content()
            if filter_text != "All":
                target_filter = filter_item
                target_filter_text = filter_text
                break

        if target_filter:
            print(f"点击价格过滤器: {target_filter_text}")

            # 记录过滤器点击前的样式
            filter_classes_before = target_filter.get_attribute("class")

            # 点击过滤器
            target_filter.click()
            p.wait_for_timeout(3000)

            # 验证过滤器状态变化
            filter_classes_after = target_filter.get_attribute("class")
            style_changed = filter_classes_before != filter_classes_after
            print(f"过滤器样式变化: {style_changed}")

            # 检查商品数量变化
            products_after = p.get_by_test_id("wid-product-card-promotion-drawer-item").all()
            products_count_after = len(products_after)

            print(f"过滤后商品数量: {products_count_after}")

            # 验证过滤效果
            if products_count_after > 0:
                print("过滤后仍有商品，验证第一个商品价格是否符合过滤条件")
                self._verify_filtered_price_accuracy(p, products_after[0], target_filter_text)
            else:
                print("过滤后没有商品符合条件")

            # 测试点击回"All"选项
            all_filter = None
            for filter_item in filter_items:
                if filter_item.text_content() == "All":
                    all_filter = filter_item
                    break

            if all_filter:
                print("点击'All'过滤器恢复所有商品")
                all_filter.click()
                p.wait_for_timeout(2000)

                products_back_to_all = p.get_by_test_id("wid-product-card-promotion-drawer-item").all()
                count_back_to_all = len(products_back_to_all)
                print(f"点击'All'后商品数量: {count_back_to_all}")
        else:
            print("只有'All'选项，跳过过滤测试")

    def _verify_filtered_price_accuracy(self, p: Page, product_card, filter_text):
        """验证过滤后的商品价格是否符合过滤条件"""
        print(f"验证商品价格是否符合过滤条件: {filter_text}")

        # 获取商品价格
        price_element = product_card.get_by_test_id("wid-product-card-price-value")
        if price_element.count() > 0 and price_element.is_visible():
            price_text = price_element.text_content()

            try:
                # 提取价格数值
                price_value = float(price_text.replace("$", ""))
                print(f"第一个商品价格: ${price_value}")

                # 根据过滤条件验证价格
                if "Under $5" in filter_text:
                    if price_value < 5.0:
                        print(f"✓ 价格${price_value}符合'Under $5'过滤条件")
                    else:
                        print(f"⚠ 价格${price_value}不符合'Under $5'过滤条件")

                elif "$5 - $10" in filter_text:
                    if 5.0 <= price_value <= 10.0:
                        print(f"✓ 价格${price_value}符合'$5 - $10'过滤条件")
                    else:
                        print(f"⚠ 价格${price_value}不符合'$5 - $10'过滤条件")

                elif "$10 - $15" in filter_text:
                    if 10.0 <= price_value <= 15.0:
                        print(f"✓ 价格${price_value}符合'$10 - $15'过滤条件")
                    else:
                        print(f"⚠ 价格${price_value}不符合'$10 - $15'过滤条件")

                elif "$15 - $25" in filter_text:
                    if 15.0 <= price_value <= 25.0:
                        print(f"✓ 价格${price_value}符合'$15 - $25'过滤条件")
                    else:
                        print(f"⚠ 价格${price_value}不符合'$15 - $25'过滤条件")

                elif "$25 & Above" in filter_text or "Above $25" in filter_text:
                    if price_value >= 25.0:
                        print(f"✓ 价格${price_value}符合'$25 & Above'过滤条件")
                    else:
                        print(f"⚠ 价格${price_value}不符合'$25 & Above'过滤条件")

                else:
                    print(f"未知的过滤条件: {filter_text}")

            except ValueError:
                print(f"无法解析商品价格: {price_text}")
        else:
            print("无法获取商品价格信息")