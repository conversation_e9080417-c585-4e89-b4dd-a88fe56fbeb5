import allure
import pytest

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_cart import dweb_cart_ele
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_cart import DWebCartPage
from src.common.commonui import scroll_one_page_until


@allure.story("【110171】 PC/mobile购物车-匿名用户操作稍后再买 -- suqin")
class TestDWebAnnyCartSaveForLaterUIUX:
    pytestmark = [pytest.mark.pccart, pytest.mark.dweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【110171】 PC/mobile购物车-匿名用户操作稍后再买 -- suqin")
    def test_110171_dWeb_anny_cart_save_for_later_ui_ux(self, not_login_page: dict, pc_anony_header):
        """
        【110171】 PC/mobile购物车-匿名用户操作稍后再买
        此用例的校验点有：
        1. 匿名用户，点击猜你喜欢合集的商品，并加购
        2. 进入购物车，点击稍后再买按钮
        3. 断言拉起注册登录页面
        """
        p: Page = not_login_page.get("page")
        c = not_login_page.get("context")

        # 直接进入指定页面
        cart_page = DWebCartPage(p, pc_anony_header, browser_context=c, page_url="/cart?__at__=1")
        p.wait_for_timeout(3000)
        # 滚动到指定位置-猜你喜欢
        scroll_one_page_until(p, dweb_cart_ele.ele_recommend_module)

        # 获取猜你喜欢商品
        recommend_card = cart_page.FE.eles(dweb_cart_ele.ele_recommend_module_card)
        for index1, item1 in enumerate(recommend_card):
            # 加购推荐商品
            item1.query_selector(u"//div[@data-testid='btn-atc-plus']").click()
            p.wait_for_timeout(1000)
            if index1 == 2:
                break
        # 回到购物车第一个商品位置
        p.query_selector(f"{dweb_cart_ele.ele_cart_normal_card}" + "[1]").scroll_into_view_if_needed()
        p.wait_for_timeout(2000)
        # 点击购物车商品 稍后购买按钮
        normal_card = cart_page.FE.eles(dweb_cart_ele.ele_cart_normal_card)
        for index2, item2 in enumerate(normal_card):
            item2.query_selector(u"//div[@data-testid='btn-save-for-later']").click()
            if index2 == 0:
                break
        p.wait_for_timeout(1000)
        # 匿名用户，断言拉起注册登录页面
        assert p.query_selector(u"//div[contains(@class,'LoginSignupV2_loginSignupContainer')]").is_visible()
