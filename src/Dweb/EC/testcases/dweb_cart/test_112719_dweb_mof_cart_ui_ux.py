"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_112719_mweb_mof_cart_ui_ux.py
@Description    :
@CreateTime     :  2025/4/10 14:33
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/4/10 14:33
"""

import allure
import pytest
from playwright.sync_api import Page
from src.Dweb.EC.dweb_ele.dweb_cart import dweb_cart_ele
from src.Dweb.EC.dweb_ele.dweb_category import dweb_category_ele
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_cart import DWebCartPage
from src.Dweb.EC.dweb_pages.dweb_page_category.dweb_page_category import DWebCategorypage
from src.api.zipcode import switch_zipcode
from src.common.commfunc import empty_cart
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log


@allure.story("PC购物车-MOF购物车UI/UX验证")
class TestDwebMoCartUIUX:
    pytestmark = [pytest.mark.pccart, pytest.mark.todo, pytest.mark.transaction, pytest.mark.zhuli]

    @allure.title("PC购物车-MO购物车UI/UX验证")
    def test_112719_DWeb_mof_cart_ui_ux(self, page: dict, pc_autotest_header):
        """
        【112719】 PC购物车-MOF购物车UI/UX验证
        """
        p = page.get("page")
        c = page.get("context")
        switch_zipcode(pc_autotest_header, '01054')
        p.wait_for_timeout(2000)
        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c,
                                 page_url="/cart")

        # 2.清除购物车
        empty_cart(pc_autotest_header)

        p.reload()
        p.wait_for_timeout(3000)
        # 2. 使用封装方法加购Local Delivery商品
        with allure.step("使用封装方法加购MO Local Delivery商品"):
            # 创建分类页面对象
            category_page = DWebCategorypage(p, pc_autotest_header, browser_context=c, page_url="/category/sale")
            p.wait_for_timeout(2000)

            # 检查筛选模块是否存在
            filter_button = category_page.page.get_by_test_id("wid-filters-container")
            log.info(f"筛选模块是否可见: {filter_button.is_visible(timeout=1000)}")

            # 用forzen分类定位商品
            local_filter_id = category_page.page.get_by_test_id(dweb_category_ele.ele_filter_frozen)

            # 调用封装方法加购Local Delivery商品
            added_count = category_page.add_products_from_home_by_filter(
                filter_name="Fulfilled by Weee",
                filter_id=local_filter_id,
                count=3,  # 加购2个商品
            )
            p.wait_for_timeout(5000)  # 增加等待时间
            log.info(f"成功加购{added_count}个商品")
            assert added_count > 0, "未能成功加购商品"
        # 滚动到指定位置-购物车顶部
        # scroll_one_page_until(p, dweb_cart_ele.ele_cart_normal_name)
        with allure.step("验证购物车UI"):
            log.info(f"开始验证购物车ui")
            # 创建购物车页面对象
            dwebcart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url="/cart?__at__=1")
            p.wait_for_timeout(2000)
            # 关闭可能出现的广告弹窗
            if dwebcart_page.page.locator("//img[contains(@aria-label, 'close button')]").all():
                dwebcart_page.page.locator("//img[contains(@aria-label, 'close button')]").click()
                log.info("关闭广告弹窗")
            # scroll_one_page_until(p, dweb_cart_ele.ele_cart_normal_name)
            assert category_page.page.get_by_test_id(dweb_cart_ele.ele_cart_normal_name).is_visible(), "MOF购物车不存在"

            # 判断直邮购物车的标题=Direct mail
            assert "Direct mail" == p.get_by_test_id(dweb_cart_ele.ele_cart_normal_name).text_content()
            # # 判断只有购物车标题下面的文案显示正确
            assert "Shipping via FedEx, UPS, etc." == p.locator(dweb_cart_ele.ele_grocery_mkpl_text).text_content()

            # 判断shipping_fee中有美元符号存在或为free
            shipping_fee = p.get_by_test_id(dweb_cart_ele.ele_cart_mo_shipping_fee_price).all()
            for sf in shipping_fee:
                log.info("shipping_fee的content===>" + sf.text_content())
                assert "$" in sf.text_content() or 'Free shipping' == sf.text_content()

            # 判断Cold_Pack_Fee中有美元符号存在或为free
            cold_package_fee = p.get_by_test_id(dweb_cart_ele.ele_cart_cold_package_fee_price).all()
            for cp in cold_package_fee:
                log.info("cold_package_fee的content===>" + cp.text_content())
                assert "$" in sf.text_content() or 'Free shipping' == cp.text_content()

                # 检查购物车中的商品是否有冷包标签
                with allure.step("验证冷包商品标签"):
                    # 获取所有商品卡片
                    cart_items = p.get_by_test_id(dweb_cart_ele.ele_cart_normal_goods).all()
                    log.info(f"购物车中共有 {len(cart_items)} 个商品")

                    for index, item in enumerate(cart_items):
                        # 检查商品是否有冷包标签
                        cold_pack_tag = item.get_by_test_id(dweb_cart_ele.ele_cart_goods_tag)
                        if cold_pack_tag.count() > 0 and cold_pack_tag.is_visible(timeout=1000):
                            log.info(f"商品 {index + 1} 有冷包标签")
                            # 验证冷包费用显示
                            assert p.get_by_test_id(
                                dweb_cart_ele.ele_cart_cold_package_fee_price).is_visible(), "冷包费用未显示"
            # 获取所有的items total
            # items_total = p.get_by_test_id(dweb_cart_ele.ele_cart_subtotal).all()
            # assert items_total, f"Subtotal={items_total}"
            #
            # # 判断items_total中有美元符号存在
            # for item in items_total:
            #     log.info("item.text_content===>" + item.text_content())
            #     assert "$" in item.text_content()
            #     p.wait_for_timeout(2000)
        
        # 验证购物车商品
        with allure.step("验证购物车商品"):
            # 使用合并后的方法验证购物车商品
            assert cart_page.verify_cart_items(cart_type="normal"), "MO购物车商品验证失败"
        
        # # 执行稍后再买操作
        with allure.step("执行稍后再买操作"):
            assert cart_page.save_for_later_operations(cart_type="normal"), "MO稍后再买操作失败"
        p.wait_for_timeout(2000)

        # 执行购物车商品删除操作
        with allure.step("执行商品删除操作"):
            assert cart_page.remove_cart_item(cart_type="normal"), "MO购物车商品删除操作失败"
        log.info("H5购物车-MO购物车UI/UX----验证通过")

    # @allure.title("H5购物车-稍后再买功能验证")
    # def test_112719_MWeb_mo_cart_save_for_later(self, phone_page: dict, h5_autotest_header):
    #     """
    #     验证购物车稍后再买功能:
    #     1. 添加推荐商品到购物车
    #     2. 将商品移到稍后再买
    #     3. 验证稍后再买区域显示
    #     4. 将商品移回购物车
    #     5. 验证商品回到购物车
    #     """
    #     p: Page = phone_page.get("page")
    #     c = phone_page.get("context")
    #
    #     # 进入购物车页面
    #     cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
    #     p.wait_for_timeout(2000)
    #
    #     # 滚动到推荐商品区域并添加商品
    #     recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)
    #     for index1, item1 in enumerate(recommend_card):
    #         # 加购推荐商品
    #         item1.query_selector(mweb_cart_ele.ele_cart_atc_normal_plus).click()
    #         p.wait_for_timeout(1000)
    #         if index1 == 2:
    #             break
    #     # 滚动回购物车顶部
    #     scroll_one_page_until(p, mweb_cart_ele.ele_cart_normal_card)
    #
    #     # 执行稍后再买操作
    #     assert cart_page.save_for_later_operations(), "稍后再买操作失败"

        # 验证稍后再买区域
        # save_later_section = cart_page.FE.ele("[data-testid='save-for-later-section']")
        # assert save_later_section.is_visible(), "稍后再买区域未显示"

        # 将商品移回购物车
        # assert cart_page.move_to_cart_from_save_later(), "移回购物车操作失败"
        #
        # # 验证商品已回到购物车
        # normal_cards = cart_page.FE.eles("[data-testid='cart-normal-card']")
        # assert len(normal_cards) > 0, "商品未成功移回购物车"

