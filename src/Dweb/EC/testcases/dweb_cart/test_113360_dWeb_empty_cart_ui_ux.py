import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_cart import dweb_cart_ele
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_cart import DWebCartPage
from playwright.sync_api import Page
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart


@allure.story("PC购物车-空购物车UI/UX验证 -- suqin")
class TestDWebEmptyCartUIUX:
    pytestmark = [pytest.mark.pccart, pytest.mark.dweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("PC购物车-空购物车UI/UX验证 -- suqin")
    def test_113360_dWeb_empty_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【113360】 PC购物车-空购物车UI/UX验证
        此用例的校验点有：
        1. 进入购物车，清空购物车
        2. 断言空购物车img存在
        3. 断言空购物车 文案存在
        4. 点击空购物车的start_shopping按钮
        5. 断言跳转到了首页
        """
        p: Page = page.get("page")
        c = page.get("context")
        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url="/cart?__at__=1")

        # 清空购物车
        try:
            empty_cart(pc_autotest_header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        # 刷新页面
        p.reload()
        p.wait_for_timeout(3000)

        # 断言空购物车img存在
        assert p.get_by_test_id(dweb_cart_ele.ele_cart_img).is_visible()
        # 断言空购物车 文案存在
        assert p.get_by_test_id(dweb_cart_ele.ele_cart_text).is_visible()
        # 点击空购物车的start_shopping按钮
        cart_page.start_shopping()
        p.wait_for_timeout(5000)
        # 断言跳转到了首页,判断能找到首页banner即可
        assert p.get_by_test_id("wid-sidebar-item-top-charts").all()[0].is_visible()


