import re

import allure
import pytest
from typing import List
from playwright.sync_api import ElementHandle

from src.Dweb.EC.dweb_ele.dweb_cart import dweb_cart_ele
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_cart import DWebCartPage
from src.Dweb.EC.dweb_pages.dweb_page_category.dweb_page_category import DWebCategorypage
from playwright.sync_api import Page
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart


@allure.story("【109549】 PC购物车-单个生鲜购物车样式")
class TestDWebSingleGroceryCartUIUX:
    pytestmark = [pytest.mark.pccart, pytest.mark.regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【109549】 PC购物车-单个生鲜购物车样式")
    def test_109549_dWeb_single_grocery_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【109549】 PC购物车-单个生鲜购物车样式
        该测试用例的测试点有：
        1. 进入分类页面，加购Local类型的商品进购物车
        2. 进入购物车，校验购物车的样式
        3. 校验购物车中商品的样式
        """
        p: Page = page.get("page")
        c = page.get("context")
        # 构造的购物车页面
        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url="/cart")
        # 清空购物车
        try:
            empty_cart(pc_autotest_header)
            # 清空购物车之后刷新页面
            p.reload()
            p.wait_for_timeout(2000)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        # 构造的分类页面
        category_page = DWebCategorypage(p, pc_autotest_header, browser_context=c)
        # 去分类页加购Local类型的商品进购物车
        category_page.add_to_local_product_cart_from_category()
        # 去购物车结算
        category_page.go_to_cart_from_category()
        # 光标移到global+ 位置
        p.get_by_test_id("wid-direct-from-japan").hover()

        # 购物车断言
        p.wait_for_selector(dweb_cart_ele.ele_cart_summary)
        assert cart_page.FE.ele(dweb_cart_ele.ele_cart_summary).is_visible()
        assert "title" in cart_page.FE.ele(dweb_cart_ele.ele_cart_summary).get_attribute("class")
        # 判断只有一个购物车
        assert len(cart_page.FE.ele(dweb_cart_ele.ele_cart_summary_list)) == 1 and cart_page.FE.ele(
            dweb_cart_ele.ele_cart_summary_list)

        # 0. 判断第一个购物车是local delivery
        assert "Local delivery" == cart_page.FE.ele(dweb_cart_ele.ele_cart_summary_local_delivery).text_content()
        # 1. 判断subtotal元素存在
        assert cart_page.FE.ele(dweb_cart_ele.ele_cart_subtotal).is_visible()
        sub_total_fee = cart_page.FE.ele(dweb_cart_ele.ele_cart_subtotal_fee)
        # 2. 判断subtotal值
        assert sub_total_fee.is_visible() and "$" in sub_total_fee.text_content()

        # 获取所有的items total
        items_total = cart_page.FE.eles(dweb_cart_ele.ele_cart_items_total)
        assert items_total, f"items_total={items_total}"
        # 3. 判断items_total中有美元符号存在
        for item in items_total:
            log.debug("item.text_content===>" + item.text_content())
            assert "$" in item.text_content()

        # 4. 判断delivery_fee中有美元符号存在或为free
        delivery_fee = cart_page.FE.eles(dweb_cart_ele.ele_cart_delivery_fee)
        for df in delivery_fee:
            log.debug("delivery_fee的content===>" + df.text_content())
            assert "$" in df.text_content() or 'FREE' == df.text_content()

        # 5. 判断左侧的购物车
        all_cart_div = cart_page.FE.eles(dweb_cart_ele.ele_cart_each_cart_div)
        assert all_cart_div, f"all_cart_div={all_cart_div}"
        for acd in all_cart_div:
            "//div[@data-testid='btn-atc-plus']"
            all_goods: List[ElementHandle] = acd.query_selector_all(dweb_cart_ele.ele_cart_products)
            # all_goods: List[ElementHandle] = acd.query_selector_all("//div[contains(@class, 'GoodsInCart_goods__')]")
            assert all_goods, f"购物车下的所有商品all_goods={all_goods}"
            for index, ag in enumerate(all_goods):
                # 正则表达式匹配以$开头，后面是数字，且小数点后有两位数字的模式
                price_pattern = r'^\$\d+\.\d{2}$'
                # 拿到每个商品卡片模块元素信息
                # ag_card = ag.query_selector(cart_elements.ele_cart_products)
                # 查到购物车商品卡片上加/减元素部分
                goods_in_cart_price_action = ag.query_selector(u"//div[contains(@class, 'GoodsInCart_priceAction')]")
                # 校验购物车里"每个商品"的div
                assert goods_in_cart_price_action.is_visible()
                # 查到购物车商品卡片上商品的总价格元素部分
                goods_in_cart_price = ag.query_selector(
                    u"//div[contains(@class, 'leading-none font-semibold text-center')]")
                log.info("each product total price in cart===>" + goods_in_cart_price.text_content())
                # 校验商品的价格以$开头，如果是赠品，就是Free
                assert "$" in goods_in_cart_price.text_content() or "Free" in goods_in_cart_price.text_content()
                # 校验价格是两位小数
                assert re.match(price_pattern, goods_in_cart_price.text_content()), f"Price 价格不是两位小数"
                # 查到购物车商品卡片上单个价格元素部分
                good_in_cart_price = ag.query_selector(u"//span[contains(@class,'GoodsInCart_priceUsed')]")
                log.info("each product single price in cart===>" + good_in_cart_price.text_content())
                # 校验商品的价格以$开头，如果是赠品，就是Free
                print(good_in_cart_price.text_content())
                assert "$" in good_in_cart_price.text_content()

                # 使用re.match来检查字符串是否匹配正则表达式模式
                # 校验价格是两位小数
                assert re.match(price_pattern, good_in_cart_price.text_content()), f"Price 价格不是两位小数"
                # 查到购物车商品卡片上单个划线价价格元素部分
                good_in_cart_base_price = ag.query_selector(u"//del[contains(@class,'GoodsInCart_priceIgnore')]")
                # 如果有划线价
                if good_in_cart_base_price:
                    log.info("each product single price in cart===>" + good_in_cart_base_price.text_content())
                    # 校验商品的价格以$开头，如果是赠品，就是Free
                    assert "$" in good_in_cart_price.text_content()
                    # 校验价格是两位小数
                    assert re.match(price_pattern, good_in_cart_base_price.text_content()), f"Price 价格不是两位小数"

                # 第一个商品有可能是gift商品，没有remove和save_for_later
                if index >= 2:
                    # 校验remove按钮
                    remove = ag.query_selector(dweb_cart_ele.ele_remove)
                    # 校验save_for_later
                    save_for_later = ag.query_selector(dweb_cart_ele.ele_save_for_later)
                    assert remove.is_visible() and save_for_later.is_visible()
                product_name = ag.query_selector(dweb_cart_ele.ele_cart_product_title).text_content()
                log.info("product_name is: " + product_name)
                assert len(product_name) > 2

        # 7. check底部的recommendations
        # 先滚动到Recommendations
        while True:
            p.evaluate('window.scrollBy(0, window.innerHeight)')
            p.wait_for_timeout(2000)
            if cart_page.FE.ele(u"//span[text()='Recommendations']"):
                cart_page.FE.ele(u"//span[text()='Recommendations']").scroll_into_view_if_needed()
                break

        # 7.1 校验标题
        assert cart_page.FE.ele(dweb_cart_ele.ele_cart_recommendations).text_content() == 'Recommendations'
        recommendations_all_goods = cart_page.FE.eles(dweb_cart_ele.ele_cart_recommendations_all_goods)
        assert recommendations_all_goods, f"购物车推荐商品为0"
        # 7.2 校验recommendations下面的商品
        for index, i in enumerate(recommendations_all_goods):
            # 后面隐藏的商品，继续找加购按钮，找不到，可能因为不可见，需要划动
            if index <= 2:
                r_add_to_cart_btn = i.query_selector(u"//i[@data-role]")
                assert r_add_to_cart_btn.is_enabled()
                r_add_to_cart_btn.click()

        # 8 校验右侧的总结文案
        # assert "Summary" == cart_page.FE.ele(cart_elements.ele_cart_summary).text_content()
        # 8.1 判断购物车右侧总结有中有美元符号

