import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_cart import dweb_cart_ele
from src.Dweb.EC.dweb_ele.dweb_cart import dweb_trade_in_ele

from src.Dweb.EC.dweb_ele.dweb_category import dweb_category_ele
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_cart import DWebCartPage
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_trade_in import DWebTradeInPage
from playwright.sync_api import Page

from src.Dweb.EC.dweb_pages.dweb_page_category.dweb_page_category import DWebCategorypage
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart


@allure.story("PC购物车-金额小于$35时显示免运费banner验证")
class TestDWebNormalTradeInUIUX:
    pytestmark = [pytest.mark.pccart, pytest.mark.regression, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("PC购物车-金额小于$35时显示免运费banner验证")
    def test_107320_dWeb_normal_trade_in_low_35_ui_ux_v3(self, page: dict, pc_autotest_header, login_trace):
        """
        [107320][dWeb]-金额小于$35时显示免运费banner验证
        测试步骤：

        1、访问https://www.sayweee.com/en/cart购物车页面，
        2、清除购物车
        3、add_filter_product_to_cart_from_category 这个方法包括进入指定分类，勾选指定filter，加购商品
        ele_sale_categ，ele_delivery_type_local
        5、判断购物车商品金额ele_cart_normal_item_total小于$35，如果不小于35 删除购物车的商品
        6、会显示免运费banner 入口
        """
        p: Page = page.get("page")
        c = page.get("context")
        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url="/cart")

        category_page = DWebCategorypage(p, pc_autotest_header, browser_context=c, page_url="")

        # 切换到98011 zipcode 下
        cart_page.home_page_switch_zipcode("98011")
        # 进入购物车
        p.get_by_test_id(dweb_cart_ele.ele_mini_cart_wrapper).click()
        
        # 如果是空购物车
        if p.get_by_test_id("wid-cart-empty-cart").is_visible():
            # 点击go shopping
            p.get_by_test_id("wid-cart-empty-start-shopping").click()
            # 进入sale 分类，勾选 local filter，加购6件商品
            category_page.add_filter_product_to_cart_from_category("sale", dweb_category_ele.ele_sale_categ,
                                                                   dweb_category_ele.ele_delivery_type_local, 6)
            # 进入购物车
            p.get_by_test_id(dweb_cart_ele.ele_mini_cart_wrapper).click()
        
        # 获取当前购物车金额
        cart_amount = cart_page.get_specify_cart_total_amount(dweb_cart_ele.ele_cart_normal,
                                                              dweb_cart_ele.ele_cart_normal_cart_item_total_price)
        log.info(f"当前购物车金额: ${cart_amount}")
        
        # 如果金额大于等于$35，移除商品直到金额小于$35
        while cart_amount >= 35:
            log.info("购物车金额大于等于$35，移除商品")
            cart_page.remove_normal_cart_item(0)
            cart_amount = cart_page.get_specify_cart_total_amount(dweb_cart_ele.ele_cart_normal,
                                                                  dweb_cart_ele.ele_cart_normal_cart_item_total_price)
            log.info(f"移除商品后，当前购物车金额: ${cart_amount}")
            if cart_amount == 0:
                break
        
        # 最终确认购物车金额
        log.info(f"最终购物车金额: ${cart_amount}")
        # 确认金额小于$35
        assert cart_amount < 35, f"购物车金额应小于$35，实际为${cart_amount}"
        log.info(f"确认购物车金额小于$35: ${cart_amount}")
        
        # 6. 验证显示免运费banner
        assert p.get_by_test_id(dweb_cart_ele.ele_cart_banner_normal).is_visible(), "购物车金额小于$35时未显示免运费banner"
        banner_text = cart_page.get_free_shipping_banner_text()
        # assert "$35" in banner_text, "免运费banner文案不包含$35"
        log.info(f"验证购物车金额小于$35时显示免运费banner: {banner_text}")

    @allure.title("PC购物车-金额小于$68时换购验证")
    def test_107320_dWeb_normal_trade_in_35_to_68_ui_ux_v3(self, page: dict, pc_autotest_header, login_trace):
        """
        [107320][dWeb]-金额小于$68时换购验证
        测试步骤：
        1、访问https://www.sayweee.com/en/cart购物车页面，
        2、清除购物车
        3、滚动到购物车底部推荐模块 ele_cart_recommendations
        4、加购推荐商品大于35 小于68 ele_recommend_module_card
        5、判断购物车商品金额ele_cart_normal_item_total大于35小于68
        6、会显示显示换购入口banner ele_cart_trade_in_normal
        7、此时banner文案为$x away from unlocking more discounts!
        8、点击换购入口ele_cart_trade_in_normal，右侧弹出换购列表ele_trade_in
        9、此时换购商品ele_trade_in_products不可加购
        10、关闭右侧的换购ele_trade_in_close
        """
        p: Page = page.get("page")
        c = page.get("context")
        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url="/cart")
        category_page = DWebCategorypage(p, pc_autotest_header, browser_context=c, page_url="")
        trade_in_page = DWebTradeInPage(p, pc_autotest_header, browser_context=c, page_url="")
        # 切换到98011 zipcode 下
        cart_page.home_page_switch_zipcode("98011")
        # 进入购物车
        p.get_by_test_id(dweb_cart_ele.ele_mini_cart_wrapper).click()

        # 如果是空购物车
        if p.get_by_test_id("wid-cart-empty-cart").is_visible():
            # 点击go shopping
            p.get_by_test_id("wid-cart-empty-start-shopping").click()
            # 进入sale 分类，勾选 local filter，加购6件商品
            category_page.add_filter_product_to_cart_from_category("sale", dweb_category_ele.ele_sale_categ,
                                                                   dweb_category_ele.ele_delivery_type_local, 6)
            # 进入购物车
            p.get_by_test_id(dweb_cart_ele.ele_mini_cart_wrapper).click()

        # 获取当前购物车金额
        cart_amount = cart_page.get_specify_cart_total_amount(dweb_cart_ele.ele_cart_normal,
                                                              dweb_cart_ele.ele_cart_normal_cart_item_total_price)
        log.info(f"当前购物车金额: ${cart_amount}")

        # 如果金额小于$35，继续加购直到大于等于$35
        while cart_amount < 35:
            log.info("购物车金额小于$35，继续加购商品")
            category_page.add_filter_product_to_cart_from_category("sale", dweb_category_ele.ele_sale_categ,
                                                                   dweb_category_ele.ele_delivery_type_local, 3)
            p.get_by_test_id(dweb_cart_ele.ele_mini_cart_wrapper).click()
            p.wait_for_timeout(1000)
            cart_amount = cart_page.get_specify_cart_total_amount(dweb_cart_ele.ele_cart_normal,
                                                                  dweb_cart_ele.ele_cart_normal_cart_item_total_price)
            log.info(f"加购后，当前购物车金额: ${cart_amount}")

        # 如果金额大于等于$68，移除商品直到金额小于$68
        while cart_amount >= 68:
            log.info("购物车金额大于等于$68，移除商品")
            cart_page.remove_normal_cart_item(0)
            p.reload()
            p.wait_for_timeout(2000)
            cart_amount = cart_page.get_specify_cart_total_amount(dweb_cart_ele.ele_cart_normal,
                                                                  dweb_cart_ele.ele_cart_normal_cart_item_total_price)
            log.info(f"移除商品后，当前购物车金额: ${cart_amount}")
            if cart_amount == 0:
                break

        # 确认金额在$35-$68之间
        assert 35 <= cart_amount < 68, f"购物车金额应在$35-$68之间，实际为${cart_amount}"
        log.info(f"确认购物车金额在$35-$68之间: ${cart_amount}")

        # 6-7. 验证显示换购入口banner及文案
        assert p.get_by_test_id(dweb_cart_ele.ele_cart_trade_in_normal_banner).is_visible(), "购物车金额在$35-$68之间时未显示换购入口banner"
        banner_text = p.get_by_test_id(dweb_cart_ele.ele_cart_trade_in_normal_banner).text_content()
        assert "away from unlocking more discounts" in banner_text, "换购入口banner文案不正确"
        log.info(f"验证购物车金额在$35-$68之间时显示换购入口banner: {banner_text}")

        # 8-9. 点击换购入口，验证右侧弹出换购列表，并且换购商品不可加购
        # 点击换购banner，进入换购侧拉页面
        p.get_by_test_id(dweb_cart_ele.ele_cart_trade_in_normal_banner_icon).click()
        trade_in_page.is_trade_in_drawer_visible()
        trade_in_products = p.get_by_test_id(dweb_trade_in_ele.ele_trade_in_products).all()
        for index, item in enumerate(trade_in_products) :
            trade_in_page.trade_in_product(item)
            # 选择按钮不可加购
            assert item.get_by_test_id(dweb_trade_in_ele.ele_trade_in_non_selectable).is_visible()
            if index ==5:
                break

        # 10. 关闭右侧的换购
        assert trade_in_page.close_trade_in_drawer(), "关闭换购列表失败"
        log.info("成功关闭换购列表")
        log.info("PC购物车-金额小于$68时换购验证完成")

    @allure.title("PC购物车-金额大于$68时换购验证")
    def test_107320_dWeb_normal_trade_in_more_68_ui_ux_v3(self, page: dict, pc_autotest_header, login_trace):
        """
        [107320][dWeb]-金额大于$68时换购验证
        测试步骤：
        1、访问https://www.sayweee.com/en/cart购物车页面，
        2、清除购物车
        3、滚动到购物车底部推荐模块 ele_cart_recommendations
        4、加购推荐商品大于68 ele_recommend_module_card
        5、显示换购入口banner 文案为：You have selected 0/5 special deals!
        6、点击换购入口，右侧再次弹出换购列表ele_trade_in，
        7、此时换购商品ele_trade_in_products可加购
        8、加购换购商品ele_trade_in_products，商品会进入购物车
        9、注意，换购商品最多可支持5件，加购5件之后不可再加购，
        10、回到购物车，文案会更新为You have selected 5/5 special deals!
        """
        p: Page = page.get("page")
        c = page.get("context")
        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url="/cart")
        category_page = DWebCategorypage(p, pc_autotest_header, browser_context=c, page_url="")
        trade_in_page = DWebTradeInPage(p, pc_autotest_header, browser_context=c, page_url="")
        
        # 切换到98011 zipcode 下
        cart_page.home_page_switch_zipcode("98011")
        # 进入购物车
        p.get_by_test_id(dweb_cart_ele.ele_mini_cart_wrapper).click()
        
        # 如果是空购物车
        if p.get_by_test_id("wid-cart-empty-cart").is_visible():
            # 点击go shopping
            p.get_by_test_id("wid-cart-empty-start-shopping").click()
            # 进入sale 分类，勾选 local filter，加购6件商品
            category_page.add_filter_product_to_cart_from_category("sale", dweb_category_ele.ele_sale_categ,
                                                                   dweb_category_ele.ele_delivery_type_local, 6)
            # 进入购物车
            p.get_by_test_id(dweb_cart_ele.ele_mini_cart_wrapper).click()
        
        # 获取当前购物车金额
        cart_amount = cart_page.get_specify_cart_total_amount(dweb_cart_ele.ele_cart_normal,
                                                              dweb_cart_ele.ele_cart_normal_cart_item_total_price)
        log.info(f"当前购物车金额: ${cart_amount}")
        
        # 如果金额小于$68，继续加购直到大于等于$68
        while cart_amount < 68:
            log.info("购物车金额小于$68，继续加购商品")
            category_page.add_filter_product_to_cart_from_category("sale", dweb_category_ele.ele_sale_categ,
                                                                   dweb_category_ele.ele_delivery_type_local, 3)
            p.get_by_test_id(dweb_cart_ele.ele_mini_cart_wrapper).click()
            p.wait_for_timeout(1000)
            cart_amount = cart_page.get_specify_cart_total_amount(dweb_cart_ele.ele_cart_normal,
                                                                  dweb_cart_ele.ele_cart_normal_cart_item_total_price)
            log.info(f"加购后，当前购物车金额: ${cart_amount}")
        
        # 确认金额大于等于$68
        assert cart_amount >= 68, f"购物车金额应大于等于$68，实际为${cart_amount}"
        log.info(f"确认购物车金额大于等于$68: ${cart_amount}")
        
        # 验证显示换购入口banner及文案
        assert p.get_by_test_id(dweb_cart_ele.ele_cart_trade_in_normal_banner).is_visible(), "购物车金额大于等于$68时未显示换购入口banner"
        banner_text = p.get_by_test_id(dweb_cart_ele.ele_cart_trade_in_normal_banner).text_content()
        assert "You have selected 0/5 special deals" in banner_text, "换购入口banner文案不正确"
        log.info(f"验证购物车金额大于等于$68时显示换购入口banner: {banner_text}")
        
        # 点击换购banner，进入换购侧拉页面
        p.get_by_test_id(dweb_cart_ele.ele_cart_trade_in_normal_banner_icon).click()
        trade_in_page.is_trade_in_drawer_visible()
        
        # 1、进入换购侧拉pop之后，按钮是可选状态，勾选5件换购
        trade_in_products = p.get_by_test_id(dweb_trade_in_ele.ele_trade_in_products).all()
        selected_count = 0
        
        for index, product in enumerate(trade_in_products):
            if selected_count >= 5:
                break
                
            # 获取选择按钮
            select_btn = product.get_by_test_id("select_btn")
            
            # 验证按钮状态为可选择状态
            btn_status = select_btn.get_attribute("data-status")
            assert btn_status == "unselect", f"第{index+1}个商品的按钮状态应为unselect，实际为{btn_status}"
            log.info(f"第{index+1}个商品按钮状态验证通过: {btn_status}")
            
            # 点击选择按钮
            select_btn.click()
            p.wait_for_timeout(500)
            
            # 验证点击后状态变为已选择
            btn_status_after = select_btn.get_attribute("data-status")
            assert btn_status_after == "selected", f"第{index+1}个商品点击后状态应为selected，实际为{btn_status_after}"
            log.info(f"第{index+1}个商品选择成功，状态变为: {btn_status_after}")
            
            selected_count += 1
        
        log.info(f"成功选择了{selected_count}件换购商品")
        assert selected_count == 5, f"应该选择5件换购商品，实际选择了{selected_count}件"
        # 10. 关闭右侧的换购
        assert trade_in_page.close_trade_in_drawer(), "关闭换购列表失败"
        log.info("成功关闭换购列表")
        # 验证换购商品进入购物车

        log.info("PC购物车-金额小于$68时换购验证完成")