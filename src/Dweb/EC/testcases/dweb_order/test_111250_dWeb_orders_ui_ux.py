import allure
import pytest

from playwright.sync_api import Page
from src.config.weee.log_help import log
from src.Dweb.EC.dweb_pages.dweb_page_orders.dweb_page_orders import DWebOrdersPage


@allure.story("PC端-订单页面UI/UX验证 -- xuzhongyuan")
class TestDWebOrdersUIUX:
    pytestmark = [pytest.mark.pcorders, pytest.mark.dweb_regression]

    @allure.title("PC端-订单页面状态标签切换UI/UX验证 -- xuzhongyuan")
    def test_111250_dWeb_orders_status_tabs_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        case_id: 111250 查看我的订单
        PC端-订单页面状态标签切换UI/UX验证
        此用例的校验点有：
        1. 进入account页面
        2. 点击my orders，进入order页面
        3. 依次点击"pending, unshipped, shipped, to review, cancelled"标签，查看订单
        4. 验证每个标签页面正确加载并显示相应状态的订单
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 创建订单页面对象
        orders_page = DWebOrdersPage(p, pc_autotest_header, browser_context=c)

        # 1. 进入订单页面
        log.info("步骤1: 进入订单页面")
        orders_page.navigate_to_orders_page()
        assert p.url.endswith("/account/my_orders"), "未成功进入订单页面"

        # 2. 依次点击订单状态标签并验证
        log.info("步骤2: 依次点击订单状态标签并验证")

        # 定义要测试的标签
        status_tabs = ["All", "Pending", "Unshipped", "Shipped", "Review", "Cancelled"]

        # 遍历每个标签并点击
        for tab_name in status_tabs:
            log.info(f"点击 {tab_name} 标签")

            # 切换到指定标签
            orders_page.switch_to_tab(tab_name)

        log.info("订单页面状态标签切换测试完成")
