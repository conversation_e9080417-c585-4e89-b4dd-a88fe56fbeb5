import allure
import pytest

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_account.dweb_order import dweb_order_list_ele, dweb_buy_again_ele
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_order_list import DWebOrderListPage
from src.config.weee.log_help import log


@allure.story("【108616】 订单列表已取消tab-再来一单流程验证")
class TestDWebMyCanceledOrderBuyAgainUIUX:
    pytestmark = [pytest.mark.dweb_porder, pytest.mark.dweb_regression, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("【108616】 订单列表已取消tab-再来一单流程验证")
    def test_108616_dWeb_my_canceled_order_buy_again_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【108616】 订单列表已取消tab-再来一单流程验证
        测试步骤：
        1、进入订单列表页面，点击切换到已取消tab下
        2、找到订单列表下再来一单按钮，右侧会撤拉出商品选择页面
        3、商品选择页面，默认勾选全选按钮，勾选掉某个商品，全选去掉，加入购物车按钮置灰，点击全选商品全部选中，这里如果选中几个商品，加入购物车按钮上就会显示几个
        4、点击加入购物车按钮，自动回到订单列表页面
        3\4 两条在 再来一单case 已包含
        """
        p: Page = page.get("page")
        c = page.get("context")
        
        # 1. 进入订单列表页面
        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url="/account/my_orders")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")

        # 切换到已取消tab
        canceled_tab = p.get_by_test_id(dweb_order_list_ele.order_cancelled_tab_ele)
        assert canceled_tab.is_visible(), "未找到已取消tab"
        canceled_tab.click()
        p.wait_for_timeout(2000)
        log.info("成功切换到已取消tab")
        # 2. 检查存在已取消订单
        order_list_items = p.locator(dweb_order_list_ele.order_list_card_ele).all()
        assert len(order_list_items) > 0,"已取消tab下没有订单，无法继续测试"
        for index, item in enumerate(order_list_items):
            # 查找订单列表中的按钮
            button_lists = item.locator(dweb_order_list_ele.order_list_cart_btn_ele).all()
            for index,item2 in enumerate(button_lists):
                print(item2.locator(u"//button").text_content())
                if item2.locator(u"//button").text_content() == "Reorder":
                    log.info("找到再来一单按钮")
                    # 点击再来一单
                    item2.locator(u"//button").click()
                    assert p.locator(dweb_buy_again_ele.buy_again_reorder_header_ele).is_visible(), "未成功拉出再来一单选择商品页面"
                    break






