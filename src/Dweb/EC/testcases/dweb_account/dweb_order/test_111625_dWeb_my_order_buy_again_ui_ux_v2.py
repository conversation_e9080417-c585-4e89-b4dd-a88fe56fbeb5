import allure
import pytest
import re

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele import dweb_common_ele
from src.Dweb.EC.dweb_ele.dweb_account.dweb_order import dweb_order_list_ele, dweb_buy_again_ele
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_order_list import DWebOrderListPage
from src.config.weee.log_help import log


@allure.story("【111625】 订单列表-再来一单功能验证 V2")
class TestDWebMyOrderBuyAgainUIUXV2:
    pytestmark = [pytest.mark.dweb_porder, pytest.mark.dweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【111625】 订单列表-再来一单功能验证 V2")
    def test_111625_dWeb_my_order_buy_again_ui_ux_v2(self, page: dict, pc_autotest_header, login_trace):
        """
        【111625】 订单列表-再来一单功能验证 V2
        测试步骤：
        1、进入订单列表页面，切换到已取消tab下
        2、找到其中一个order_R_items类型的订单
        3、点击再来一单按钮data-testid="wid-order-btn-buy_again"
        4、判断右侧会侧拉出再来一单商品选择页面data-testid="wid-popup-order-buy-again"
        5、判断页面有如下内容并验证交互逻辑
        6、验证无效商品置灰状态且无法勾选
        7、验证全选按钮联动
        8、验证加购按钮状态变化
        9、点击加入购物车按钮，自动回到订单列表页面
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 进入订单列表页面，切换到已取消tab下
        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url="/account/my_orders")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")

        # 切换到已取消tab
        cancelled_tab = p.get_by_test_id(dweb_order_list_ele.order_cancelled_tab_ele)
        assert cancelled_tab.is_visible(), "未找到已取消tab"
        cancelled_tab.click()
        p.wait_for_timeout(2000)
        assert "filter_status=4" in p.url, "未切换到已取消tab"

        # 2. 获取订单类型
        order_R_items = p.get_by_test_id(dweb_order_list_ele.order_list_R_card_ele).all()
        order_S_items = p.get_by_test_id(dweb_order_list_ele.order_list_S_card_ele).all()
        order_P_items = p.get_by_test_id(dweb_order_list_ele.order_list_P_card_ele).all()
        order_G_items = p.get_by_test_id(dweb_order_list_ele.order_list_G_card_ele).all()
        order_Pre_items = p.get_by_test_id(dweb_order_list_ele.order_list_Pre_card_ele).all()

        # 找到R类型订单
        if len(order_R_items) == 0:
            log.warning("已取消tab下没有R类型订单，无法继续测试")
            pytest.skip("已取消tab下没有R类型订单，跳过测试")

        # 3. 尝试最多2个订单找到有有效商品的再来一单页面
        available_items = []
        max_attempts = min(2, len(order_R_items))
        
        for attempt in range(max_attempts):
            log.info(f"尝试第{attempt + 1}个订单的再来一单功能")
            
            current_order = order_R_items[attempt]
            buy_again_btn = current_order.get_by_test_id(dweb_order_list_ele.order_buy_again_btn)
            assert buy_again_btn.is_visible(), f"第{attempt + 1}个订单的再来一单按钮不可见"
            buy_again_btn.click()
            p.wait_for_timeout(3000)

            buy_again_page = p.get_by_test_id(dweb_buy_again_ele.buy_again_page)
            assert buy_again_page.is_visible(), "再来一单商品选择页面未显示"

            title = p.get_by_test_id(dweb_buy_again_ele.buy_again_title)
            assert title.is_visible(), "标题不可见"

            available_items = p.get_by_test_id(dweb_buy_again_ele.buy_again_product_available_item).all()
            
            if len(available_items) > 0:
                log.info(f"第{attempt + 1}个订单找到{len(available_items)}个有效商品，继续测试")
                break
            else:
                log.info(f"第{attempt + 1}个订单没有有效商品，验证无效商品后尝试下一个")
                
                unavailable_title = p.get_by_test_id(dweb_buy_again_ele.buy_again_unavailable_title)
                assert unavailable_title.is_visible(), "无效商品标题不可见"
                
                unavailable_items = p.get_by_test_id(dweb_buy_again_ele.buy_again_product_unavailable_item).all()
                if len(unavailable_items) > 0:
                    log.info(f"验证到{len(unavailable_items)}个无效商品")
                    # 验证无效商品的基础信息
                    for unavailable_item in unavailable_items:
                        unavailable_checkbox = unavailable_item.get_by_test_id(dweb_buy_again_ele.buy_again_product_unavailable_item_checkbox)
                        assert unavailable_item.get_by_test_id(dweb_buy_again_ele.buy_again_product_unavailable_item_name).is_visible(), "无效商品名称不可见"
                        assert unavailable_item.get_by_test_id(dweb_buy_again_ele.buy_again_product_unavailable_item_price).is_visible(), "无效商品价格不可见"
                        assert unavailable_item.get_by_test_id(dweb_buy_again_ele.buy_again_product_unavailable_item_image).is_visible(), "无效商品图片不可见"
                
                # 关闭当前页面，尝试下一个订单
                close_btn = p.get_by_test_id(dweb_buy_again_ele.buy_again_page_close)
                if close_btn.is_visible():
                    close_btn.click()
                    p.wait_for_timeout(2000)

        # 如果所有尝试都没有找到有效商品，跳过测试
        if attempt == max_attempts - 1 and len(available_items) == 0:
            log.info(f"已尝试{max_attempts}个订单，均无有效商品，结束测试")
            pytest.skip(f"已尝试{max_attempts}个订单，均无有效商品，跳过测试")

        # 确保找到了有效商品
        assert len(available_items) > 0, "没有找到有效商品"

        # 获取页面元素
        unavailable_title = p.get_by_test_id(dweb_buy_again_ele.buy_again_unavailable_title)
        unavailable_items = p.get_by_test_id(dweb_buy_again_ele.buy_again_product_unavailable_item).all()
        
        add_cart_btn = p.get_by_test_id(dweb_buy_again_ele.buy_again_add_cart_btn)
        assert add_cart_btn.is_visible(), "加购按钮不可见"

        select_all_btn = p.get_by_test_id(dweb_buy_again_ele.buy_again_select_all)
        assert select_all_btn.is_visible(), "全选按钮不可见"

        change_date_btn = p.get_by_test_id(dweb_buy_again_ele.buy_again_date_info_value)
        assert change_date_btn.is_visible(), "切换日期按钮不可见"

        # 6. 商品选择页面，默认勾选全选按钮
        select_all_checkbox = select_all_btn.get_by_test_id(dweb_buy_again_ele.buy_again_select_all_checkbox)
        assert select_all_checkbox.get_attribute("data-selected") == "true", "全选按钮默认未勾选"
        log.info("验证全选按钮默认勾选状态成功")

        # 验证所有有效商品默认被选中
        for item in available_items:
            item_checkbox = item.get_by_test_id(dweb_buy_again_ele.buy_again_product_available_item_checkbox)
            assert item_checkbox.get_attribute("data-selected") == "true", "有效商品默认未被选中"

            # 验证商品基础信息
            # 1. 商品标题不为空
            item_title = item.get_by_test_id(dweb_buy_again_ele.buy_again_product_item_content_title)
            assert item_title.is_visible(), "商品标题元素不可见"
            assert item_title.text_content().strip() != "", "商品标题内容为空"

            # 2. 商品图片存在且src属性包含.auto
            item_image = item.get_by_test_id(dweb_buy_again_ele.buy_again_product_item_content_image)
            assert item_image.is_visible(), "商品图片元素不可见"
            image_src = item_image.get_attribute("src")
            assert image_src is not None and ".auto" in image_src, "商品图片src属性不包含.auto"

            # 3. 商品价格格式为$x.xx
            item_price = item.get_by_test_id(dweb_buy_again_ele.buy_again_product_item_content_price)
            assert item_price.is_visible(), "商品价格元素不可见"
            price_text = item_price.text_content().strip()
            assert price_text != "", "商品价格内容为空"
            price_pattern = r'^\$\d+\.\d{2}$'
            assert re.match(price_pattern, price_text), f"商品价格格式不正确，期望$x.xx格式，实际为: {price_text}"

        # 7. 验证无效商品状态
        if len(unavailable_items) > 0:
            log.info(f"无效商品区域存在{len(unavailable_items)}个商品")
            assert unavailable_title.is_visible(), "无效商品标题不可见"
            for unavailable_item in unavailable_items:
                unavailable_checkbox = unavailable_item.get_by_test_id(
                    dweb_buy_again_ele.buy_again_product_unavailable_item_checkbox)
                # 验证无效商品都是置灰状态且无法勾选
                assert unavailable_checkbox.get_attribute("data-selected") == "false", "无效商品不应该被勾选"

                # 尝试点击无效商品，验证无法勾选
                unavailable_checkbox.click()
                p.wait_for_timeout(500)
                assert unavailable_checkbox.get_attribute("data-selected") == "false", "无效商品点击后仍应保持未勾选状态"
                log.info("验证无效商品置灰状态且无法勾选成功")
        else:
            log.info("无效商品区域没有商品")

        # 8. 勾选掉有效商品下某个商品
        first_available_item = available_items[0]
        first_item_checkbox = first_available_item.get_by_test_id(
            dweb_buy_again_ele.buy_again_product_available_item_checkbox)
        first_item_checkbox.click()
        p.wait_for_timeout(1000)

        # 验证商品勾选状态变为false
        assert first_item_checkbox.get_attribute("data-selected") == "false", "有效商品取消勾选失败"
        log.info("验证有效商品取消勾选状态成功")

        # 9. 验证全选按钮状态变为false
        assert select_all_checkbox.get_attribute("data-selected") == "false", "全选按钮状态未更新"
        log.info("验证全选按钮状态变化成功")

        # 10. 验证加购按钮状态
        add_cart_submit_btn = p.get_by_test_id(dweb_buy_again_ele.buy_again_add_cart_btn)
        if len(available_items) == 1:
            # 如果只有一个商品且被取消选择，按钮应该置灰
            assert add_cart_submit_btn.get_attribute("data-disabled") == "true", "加购按钮未置灰"
            log.info("验证加购按钮置灰状态成功")
        else:
            # 如果还有其他商品被选中，按钮应该可用
            assert add_cart_submit_btn.get_attribute("data-disabled") == "false", "加购按钮状态错误"

        # 重新选中商品以确保有商品可以加购
        first_item_checkbox.click()
        p.wait_for_timeout(1000)
        assert first_item_checkbox.get_attribute("data-selected") == "true", "重新选中商品失败"

        # 验证至少有一个商品被选中时，加购按钮可用
        add_cart_submit_btn = p.get_by_test_id(dweb_buy_again_ele.buy_again_add_cart_btn)
        assert add_cart_submit_btn.get_attribute("data-disabled") == "false", "有商品选中时加购按钮应该可用"
        log.info("验证加购按钮可用状态成功")

        # 11. 点击加入购物车按钮
        add_cart_submit_btn.click()
        p.wait_for_timeout(3000)

        # 验证自动回到订单列表页面
        assert "/account/my_orders" in p.url, "未成功回到订单列表页面"
        log.info("验证回到订单列表页面成功")

        log.info("PC版再来一单功能验证V2完成")