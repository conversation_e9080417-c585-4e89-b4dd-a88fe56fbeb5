"""
<AUTHOR>  Assistant
@Version        :  V1.0.0
------------------------------------
@File           :   test_112224_dweb_giftcard_popup_verify.py
@Description    :  礼品卡弹窗验证测试用例
@CreateTime     :  2025/08/01
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/08/01
"""

import allure
import pytest
from playwright.sync_api import Page, TimeoutError
from src.Dweb.EC.dweb_pages.dweb_page_giftcard.dweb_giftcard_page import DWebGiftCardPage
from src.Dweb.EC.dweb_ele.dweb_account.dweb_giftcard.dweb_giftcard_ele import ele_giftcard_got_it_button
from src.config.weee.log_help import log


@allure.story("礼品卡弹窗验证")
class TestDWebGiftCardPopupVerify:
    pytestmark = [pytest.mark.dweb_regression, pytest.mark.popup_verify]

    @allure.title("礼品卡弹窗存在性验证")
    def test_112224_dweb_giftcard_popup_verify(self, page: dict, pc_autotest_header):
        """
        测试礼品卡弹窗存在性验证
        步骤：
        1. 初始化礼品卡页面
        2. 点击 Got it 按钮验证弹窗存在
        3. 验证测试成功
        """
        p: Page = page.get("page")
        c = page.get("context")
        giftcard_url = "product/gift-card/2189607"

        try:
            # 1. 初始化礼品卡页面
            with allure.step("初始化礼品卡页面"):
                giftcard_page = DWebGiftCardPage(p, pc_autotest_header, browser_context=c, page_url=giftcard_url)
                log.info("成功初始化礼品卡页面")
                p.wait_for_timeout(2000)  # 等待页面完全加载

            # 2. 点击 Got it 按钮验证弹窗存在
            with allure.step("点击 Got it 按钮验证弹窗存在"):
                try:
                    # 直接尝试点击 Got it 按钮，如果成功则说明弹窗存在
                    giftcard_page.close_gift_card_popup()
                    log.info("成功点击 Got it 按钮，弹窗存在且可操作")
                    
                except Exception as e:
                    log.error(f"点击 Got it 按钮失败，可能弹窗不存在: {str(e)}")
                    assert False, "弹窗不存在或 Got it 按钮不可点击"

            # 3. 验证测试成功
            with allure.step("验证测试成功"):
                log.info("礼品卡弹窗验证测试执行成功")
                assert True, "弹窗验证测试通过"

        except Exception as e:
            log.error(f"测试过程中发生异常: {str(e)}")
            try:
                log.info(f"当前页面URL: {p.url}")
                log.info(f"页面标题: {p.title()}")
                import time
                screenshot_path = f"popup_verify_error_screenshot_{int(time.time())}.png"
                p.screenshot(path=screenshot_path)
                log.info(f"错误截图已保存: {screenshot_path}")
            except Exception as debug_e:
                log.error(f"保存调试信息时出错: {str(debug_e)}")
            raise 