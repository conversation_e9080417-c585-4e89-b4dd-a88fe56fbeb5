import allure
import pytest
from playwright.sync_api import Page, TimeoutError
from src.Dweb.EC.dweb_ele.dweb_account.dweb_refer_friends import dweb_refer_friends_ele
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_refer_friends.dweb_page_refer_friends_page_entry import ReferFriendsEntryPage
from src.config.weee.log_help import log


@allure.story("邀请好友入口-顶部与mini account入口")
class TestReferFriendsPageEntry:

	@allure.title("邀请好友入口-顶部文案与mini account文案跳转验证")
	def test_102193_refer_friends_entry(self, page: dict, pc_autotest_header, login_trace):
		"""
		步骤：
		1. 初始化 人气热卖 页面
		2. 点击 PC端顶部的邀请好友文案元素
		3. 断言 URL 是否包含 account/referral
		4. 重新初始化 人气热卖 页面
		5. 悬浮 PC端顶部 mini account 组件入口元素
		6. 点击 mini account 的邀请好友文案元素
		7. 再次断言 URL 是否包含 account/referral
		"""
		p: Page = page.get("page")
		c = page.get("context")

		try:
			# 1) 初始化 人气热卖 页面（由页面对象在 __init__ 中完成跳转）
			entry_page = ReferFriendsEntryPage(p, pc_autotest_header, browser_context=c)
			log.info("成功初始化 人气热卖 页面")

			# 等待页面完全加载
			p.wait_for_load_state("load")
			log.info("页面加载完成")

			# 2) 点击 PC 端顶部的邀请好友文案元素
			p.wait_for_selector(dweb_refer_friends_ele.ele_pc_top_refer_friends_text, state="visible", timeout=30000)
			entry_page.click_pc_header_invite_friends()
			log.info("已点击 PC 顶部邀请好友文案元素")
			p.wait_for_timeout(1500)

			# 3) 断言 URL 包含 account/referral
			try:
				p.wait_for_url("**/account/referral*", timeout=20000, wait_until="domcontentloaded")
				log.info(f"检测到邀请好友页面 URL: {p.url}")
				assert "account/referral" in p.url, f"未进入邀请好友页面，当前URL: {p.url}"
			except TimeoutError:
				log.error("等待跳转至邀请好友页面超时")
				log.info(f"当前URL: {p.url}")
				assert "account/referral" in p.url, f"未进入邀请好友页面，当前URL: {p.url}"

			# 4) 重新初始化 人气热卖 页面
			entry_page = ReferFriendsEntryPage(p, pc_autotest_header, browser_context=c)
			log.info("已重新进入 人气热卖 页面")
			p.wait_for_load_state("load")

			# 在悬浮 mini account 入口前打印log，便于排查问题
			log.info("准备悬浮 PC 端顶部 mini account 组件入口元素（第5步前）")

			# 5) 悬浮 PC 端顶部 mini account 组件入口元素
			p.wait_for_selector(dweb_refer_friends_ele.ele_pc_top_mini_account_entry, state="visible", timeout=3000)
			entry_page.hover_pc_header_mini_account_entry()
			log.info("完成悬浮 PC 端顶部 mini account 组件入口元素（第5步后）")
			p.wait_for_timeout(800)

			# 6) 点击 mini account 的邀请好友文案元素
			p.wait_for_selector(dweb_refer_friends_ele.ele_pc_top_refer_friends_text, state="visible", timeout=20000)
			entry_page.click_mini_account_invite_friends()
			log.info("已点击 mini account 邀请好友文案元素")
			p.wait_for_timeout(1500)

			# 7) 再次断言 URL 包含 account/referral
			try:
				p.wait_for_url("**/account/referral*", timeout=20000, wait_until="domcontentloaded")
				log.info(f"检测到邀请好友页面 URL: {p.url}")
				assert "account/referral" in p.url, f"未进入邀请好友页面，当前URL: {p.url}"
			except TimeoutError:
				log.error("等待跳转至邀请好友页面超时（mini account 路径）")
				log.info(f"当前URL: {p.url}")
				assert "account/referral" in p.url, f"未进入邀请好友页面，当前URL: {p.url}"

		except Exception as e:
			log.error(f"测试过程中发生异常: {str(e)}")
			# 添加调试信息
			try:
				log.info(f"当前页面URL: {p.url}")
				log.info(f"页面标题: {p.title()}")
				import time
				screenshot_path = f"error_screenshot_{int(time.time())}.png"
				p.screenshot(path=screenshot_path)
				log.info(f"错误截图已保存: {screenshot_path}")
			except Exception as debug_e:
				log.error(f"保存调试信息时出错: {str(debug_e)}")
			raise


