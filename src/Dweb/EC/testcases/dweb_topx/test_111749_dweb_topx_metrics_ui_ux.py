import pytest
import allure
from playwright.sync_api import Page, expect, Error

from src.Dweb.EC.dweb_ele.dweb_topx.dweb_topx_ele import ele_dweb_topx_chart_category_icon, ele_dweb_top_rated_tag, \
    ele_dweb_topx_chart_list_see_all, ele_dweb_topx_prod_metrics
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log

class TestDWebTOPXMetrics:
    def test_111749_dweb_topx_metrics_ui_ux(self, page: dict, pc_autotest_header):
        """
        111749
        1、topx 详情页所有语言针对好评榜展示对应的卖点
        2、topx 详情页所有语言针对好评榜展示对应的卖点
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 打开topx list 页面
        page_url = TEST_URL + "/promotion/top-x/chart"
        with allure.step(f"打开TopX Chart页面: {page_url}"):
            p.goto(page_url)
            p.wait_for_timeout(3000)

        # 获取分类icon 列表,并找对应分类icon下
        dweb_category_tabs = p.locator(ele_dweb_topx_chart_category_icon).all()
        for i in dweb_category_tabs:
            i.click()
            if p.locator(ele_dweb_top_rated_tag).is_visible():
                p.locator(ele_dweb_top_rated_tag).click()
                p.wait_for_timeout(1000)
                p.locator(ele_dweb_topx_chart_list_see_all).click()
                break

        # 验证list 页面对应卖点文案展示正常
        topx_dweb_list_page_metrics = p.locator(ele_dweb_topx_prod_metrics).all()
        for i in topx_dweb_list_page_metrics:
            p.locator(i).is_visible()
        p.wait_for_timeout(1000)


