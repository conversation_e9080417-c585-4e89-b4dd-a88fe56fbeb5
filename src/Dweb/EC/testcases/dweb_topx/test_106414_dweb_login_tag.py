import re
import pytest
import allure
from playwright.sync_api import Page, expect, Error
from src.Dweb.EC.dweb_ele.dweb_topx.dweb_topx_ele import ele_dweb_topx_prod_tag
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log

# pc 端topx tag 入口点击验证
class TestDwebTopxTagToDetailPage:
    def test_106414_dweb_category_login_tag(self, page: dict, pc_autotest_header):
        """
        【106414】 点击category 页面对应topx 标签可以正常跳转至topx 详情页面
        """
         # 验证topx标签入口展示正常，点击标签可以正常跳转
        p: Page = page.get("page")
        c = page.get("context")

        # 打开category 页面
        page_url = TEST_URL + "/category"
        with allure.step(f"打开category 页面: {page_url}"):
            p.goto(page_url)
            p.wait_for_timeout(3000)

        #找到分类页的第一个商品卡片tag 并点击跳转
        prod_tag = p.locator(ele_dweb_topx_prod_tag).first
        prod_tag.click()
        p.wait_for_timeout(3000)


