import pytest
import allure
from playwright.sync_api import Page
from src.Dweb.EC.dweb_ele.dweb_home.dweb_home_ele import (
    ele_pc_basket_start_zh_off_label,
    ele_pc_basket_start,
    more_link
)
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log

# 常量定义
HOME_URL = "https://www.sayweee.com/zh"
MODAL_CLOSE_BUTTON = "wid-modal-btn-close"
WAIT_TIMEOUT = 5000
NAVIGATION_WAIT = 3000


@allure.epic("DWeb首页功能测试")
@allure.feature("新人专区功能")
class TestDwebHomeBasketStartLabel:
    
    @allure.story("新人专区立减折扣标签展示和查看更多跳转")
    @allure.title("验证新人专区组件展示和查看更多按钮跳转功能")
    def test_112215_dweb_basket_start(self, not_login_page: dict):
        """
        测试新人专区功能
        1. 验证新人专区立减折扣标签展示
        2. 验证查看更多按钮跳转功能
        """
        p: Page = not_login_page.get("page")
        
        try:
            # 1. 进入首页
            log.info("开始测试新人专区功能")
            p.goto(HOME_URL)
            log.info("成功进入首页")
            
            # 2. 处理弹窗
            self._handle_modal_dialog(p)
            
            # 3. 滚动到新人专区组件
            self._scroll_to_basket_start_section(p)
            
            # 4. 验证立减折扣标签
            if not self._verify_discount_labels(p):
                pytest.skip("首页没有立减折扣标签，请手动检查")
            
            # 5. 测试查看更多按钮跳转
            self._test_see_all_navigation(p)
            
            log.info("新人专区功能测试完成")
            
        except Exception as e:
            log.error(f"测试执行失败: {e}")
            raise
    
    def _handle_modal_dialog(self, p: Page):
        """处理弹窗对话框"""
        try:
            p.wait_for_timeout(WAIT_TIMEOUT)
            modal_button = p.get_by_test_id(MODAL_CLOSE_BUTTON)
            if modal_button.is_visible():
                modal_button.click()
                log.info("成功关闭弹窗")
        except Exception as e:
            log.warning(f"处理弹窗时出现异常: {e}")
    
    def _scroll_to_basket_start_section(self, p: Page):
        """滚动到新人专区组件"""
        scroll_one_page_until(p, ele_pc_basket_start)
        log.info("成功滚动到新人专区组件")
    
    def _verify_discount_labels(self, p: Page) -> bool:
        """验证立减折扣标签是否存在"""
        discount_labels = p.locator(ele_pc_basket_start_zh_off_label).all()
        if discount_labels:
            log.info(f"找到 {len(discount_labels)} 个立减折扣标签")
            return True
        else:
            log.warning("未找到立减折扣标签")
            return False
    
    def _test_see_all_navigation(self, p: Page):
        """测试查看更多按钮跳转功能"""
        # 记录跳转前URL
        original_url = p.url
        log.info(f"跳转前URL: {original_url}")
        
        # 点击查看更多按钮
        see_all_button = ele_pc_basket_start + more_link
        p.click(see_all_button)
        p.wait_for_timeout(NAVIGATION_WAIT)
        
        # 验证跳转成功
        current_url = p.url
        log.info(f"跳转后URL: {current_url}")
        
        assert current_url != original_url, "点击查看更多按钮后页面未跳转"
        log.info("查看更多按钮跳转成功")