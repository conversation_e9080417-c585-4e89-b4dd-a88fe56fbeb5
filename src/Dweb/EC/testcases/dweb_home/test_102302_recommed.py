# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2025/8/4
@Software       :  PyCharm
------------------------------------
"""
import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_home.dweb_home_ele import ele_pc_rec, more_link,ele_home_rec_add_to_cart
from src.common.commonui import scroll_one_page_until,close_popup_on_home
from src.common.commfunc import empty_cart
from playwright.sync_api import Page
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
@allure.story("Dweb-首页-推荐合集UI/UX验证")
class TestDwebRecommendForYouUI:
    pytestmark = [pytest.mark.dweb_regression, pytest.mark.dweb_wangyue]
    @allure.title("dweb-首页-推荐合集UI/UX验证")
    @pytest.mark.home
    def test_102302_dweb_recommed_verify(self, page: dict, pc_autotest_header, login_trace):
        """
        1、滚动到为你推荐模块
        2、验证为你推荐区域标题
        3、加购前两个商品
        4、点击查看更多按钮可以正常跳转至猜你喜欢页面
        """
        p: Page = page.get("page")
        c = page.get("context")
        # 清空购物车
        empty_cart(pc_autotest_header)
        p.goto(TEST_URL)
        p.wait_for_timeout(10000)
        # 关闭popup
        close_popup_on_home(p)
        p.wait_for_timeout(10000)
        # 1. 滚动到为你推荐模块
        scroll_one_page_until(p, ele_pc_rec)
        # 2. 验证为你推荐区域标题
        rec_title = p.locator(ele_pc_rec)
        assert rec_title.is_visible(), "RecommendForYou组件未找到"
        log.info("RecommendForYou标题展示正常")
        # 3. 加购前两个商品
        try:
            rec_add_buttons  = p.locator(ele_home_rec_add_to_cart).all()[:2]
            for index, btn in enumerate(rec_add_buttons, start=1):
                log.info(f"正在加购第 {index} 个商品...")
                btn.click()
                p.wait_for_timeout(3000)
            log.info("前两个商品加购成功")
        except Exception as e:
            log.error(f"加购过程中发生异常: {str(e)}")
            raise AssertionError("前两个商品加购失败") from e
        # 4.点击查看更多按钮跳转
        rec_more_link = ele_pc_rec + more_link
        p.click(rec_more_link)
        p.wait_for_timeout(10000)
        current_url = p.url
        reload_url = TEST_URL + "/category/perference"
        if not current_url.startswith(reload_url):
            log.error(f"未成功进入perference页面, 当前URL: {current_url}")
            pytest.fail(f"未成功进入perference页面, 当前URL: {current_url}")
        else:
            log.info("more link点击成功")
