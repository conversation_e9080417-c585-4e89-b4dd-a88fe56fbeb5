import allure
import pytest

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_account.dweb_order import dweb_order_list_ele, dweb_buy_again_ele
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_order_list import DWebOrderListPage
from src.config.weee.log_help import log


@allure.story("【108560】 再来一单页面切换日期按钮流程")
class TestDWebChangeDeliveryDateOnBuyAgainPageUIUX:
    pytestmark = [pytest.mark.dweb_porder, pytest.mark.dweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108560】 再来一单页面切换日期按钮流程")
    def test_108560_dWeb_change_delivery_date_on_buy_again_page_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【108560】 再来一单页面切换日期按钮流程
        测试步骤：
        1、进入订单列表页面，切换到已取消tab下
        2、找到其中一个order_R_items类型的订单
        3、点击再来一单按钮data-testid="wid-order-btn-buy_again"
        4、判断进入再来一单商品选择页面data-testid="wid-popup-order-buy-again"
        5、点击切换日期按钮 data-testid="wid-account-buy-again-date-info-value"
        6、拉起切换日期pop，data-testid="chenge_data_pop"
        7、点击 data-testid="wid-change-date-0" 第一周里的日期
        8、弹窗pop消失，回到再来一单右侧页面
        9、点击关闭按钮data-testid="btn-drawer-close"，回到订单列表页面
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 进入订单列表页面，切换到已取消tab下
        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url="/account/my_orders")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")

        # 切换到已取消tab
        cancelled_tab = p.get_by_test_id(dweb_order_list_ele.order_cancelled_tab_ele)
        assert cancelled_tab.is_visible(), "未找到已取消tab"
        cancelled_tab.click()
        p.wait_for_timeout(2000)
        assert "filter_status=4" in p.url, "未切换到已取消tab"

        # 2. 找到R类型订单
        order_R_items = p.get_by_test_id(dweb_order_list_ele.order_list_R_card_ele).all()
        if len(order_R_items) == 0:
            log.warning("已取消tab下没有R类型订单，无法继续测试")
            pytest.skip("已取消tab下没有R类型订单，跳过测试")

        # 3. 点击再来一单按钮
        first_r_order = order_R_items[0]
        buy_again_btn = first_r_order.get_by_test_id(dweb_order_list_ele.order_buy_again_btn)
        assert buy_again_btn.is_visible(), "再来一单按钮不可见"
        buy_again_btn.click()
        p.wait_for_timeout(3000)

        # 4. 判断进入再来一单商品选择页面
        buy_again_page = p.get_by_test_id(dweb_buy_again_ele.buy_again_page)
        assert buy_again_page.is_visible(), "再来一单商品选择页面未显示"

        # 5. 点击切换日期按钮
        date_info_btn = p.get_by_test_id(dweb_buy_again_ele.buy_again_date_info_value)
        assert date_info_btn.is_visible(), "切换日期按钮不可见"
        date_info_btn.click()
        p.wait_for_timeout(2000)

        # 6. 验证切换日期弹窗出现
        change_date_popup = p.get_by_test_id(dweb_buy_again_ele.change_date_popup)
        assert change_date_popup.is_visible(), "切换日期弹窗未显示"

        # 7. 点击第一周里的日期
        first_week_date = p.get_by_test_id(dweb_buy_again_ele.change_date_first_week)
        assert first_week_date.is_visible(), "第一周日期选项不可见"
        first_week_date.click()
        p.wait_for_timeout(2000)

        # 8. 验证弹窗消失，回到再来一单页面
        assert not change_date_popup.is_visible(), "切换日期弹窗未消失"
        assert buy_again_page.is_visible(), "未回到再来一单页面"

        # 9. 点击关闭按钮，回到订单列表页面
        close_btn = p.get_by_test_id(dweb_buy_again_ele.buy_again_page_close)
        assert close_btn.is_visible(), "关闭按钮不可见"
        close_btn.click()
        p.wait_for_timeout(2000)

        # 验证回到订单列表页面
        assert "/account/my_orders" in p.url, "未成功回到订单列表页面"
        log.info("再来一单页面切换日期按钮流程验证完成")