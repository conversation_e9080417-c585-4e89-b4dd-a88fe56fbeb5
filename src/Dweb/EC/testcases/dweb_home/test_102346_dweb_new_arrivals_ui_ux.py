import allure
import pytest
from playwright.sync_api import Page
from src.Dweb.EC.dweb_ele.dweb_home.dweb_home_ele import more_link,ele_pc_new_arrivals,ele_pc_new_arrivals_product,ele_home_new_arrivals_add_to_cart
from src.common.commonui import scroll_one_page_until
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
class TestDwebBestsellerUI:

    def test_102346_dweb_homepage_new_arrivals_ui_ux(self, page: dict, pc_autotest_header):
        """
        1、首页组件new arrivals可以正常展示
        2、获取对应new arrivals商品为20个
        3、加购商品可以正常展示
        4、点击查看更多按钮可以正常跳转至分类页new arrivals标签
        """
        p: Page = page.get("page")

        # 1. 进入首页
        log.info("开始测试new arrivals加购按钮UI")
        p.goto(TEST_URL)
        p.wait_for_timeout(5000)

        # 2. 滚动到new arrivals区域
        scroll_one_page_until(p, ele_pc_new_arrivals)
        p.wait_for_timeout(1000)

        # 3. 验证new arrivals区域标题
        new_arrivals_title = p.locator(ele_pc_new_arrivals)
        assert new_arrivals_title.is_visible(), "new arrivals组件未找到"
        log.info("new arrivals标题展示正常")

        # 4. 获取所有产品卡片
        new_arrivals_product_cards = p.locator(ele_pc_new_arrivals_product).all()
        product_count = len(new_arrivals_product_cards)

        # 5. 验证产品个数为20
        assert product_count == 20, f"new arrivals组件产品个数不对，实际{product_count}个"
        log.info(f"new arrivals产品个数验证通过，共{product_count}个产品")

        # 6.加购组件产品卡片
        new_arrivals_add_to_cart = p.locator(ele_home_new_arrivals_add_to_cart)
        assert new_arrivals_add_to_cart,"产品加购成功"

        # 7.点击查看更多按钮跳转
        new_arrivals_more_link = ele_pc_new_arrivals + more_link
        p.click(new_arrivals_more_link)
