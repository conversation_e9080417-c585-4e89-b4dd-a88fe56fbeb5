import allure
import pytest

from src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_home import DWebHomePage


@allure.story("首页搜索by category")
class TestAtHome:
    pytestmark = [pytest.mark.dweb_regression]
    @pytest.fixture(scope='class')
    def cl(self, page: dict):
        category_list = page.get("page").locator("//div[contains(@class, 'Layout_sidebarContainer')]//div[contains(@class, 'grid')]//a[@aria-label]//div[@title]/span").all()
        category_list_text = [category.text_content() for category in category_list if category.text_content() != 'Lantern Festival']
        # 第一个category一般为活动，页面与其他category不一样，所以去掉
        return category_list_text[1:]

    @pytest.fixture(scope='class')
    def setup(self, page: dict, pc_autotest_header):
        hp = DWebHomePage(page["page"], pc_autotest_header, page.get("context"))
        yield hp



    @allure.title("从首页进入category")
    @pytest.mark.parametrize("cl", ["a"],  indirect=True)
    def test_search_by_category(self, setup, cl, login_trace):
        """
        111095: MS用例号
        """
        for i in cl:
            setup.goto_category(i)

    @allure.title("首页切换相同zipcode")
    def test_switch_same_zipcode(self, setup, login_trace):
        """
        MS用例号: 110756: 切换zipcode逻辑
        """
        setup.switch_same_zipcode_and_check_products()

    @allure.title("首页切换不同zipcode")
    def test_switch_different_zipcode(self, setup, login_trace):
        """
        MS用例号: 110756: 切换zipcode逻辑
        """
        setup.switch_different_zipcode_and_check_products()

    @allure.title("explore store的页面验证")
    def test_each_store_page_check(self, setup, login_trace):
        """
        MS用例号: 110756: 切换zipcode逻辑
        """
        setup.home_switch_specific_store_and_check(store_name="Explorer")

    def test_home_banner(self, setup, login_trace):
        """
        测试Home_banner
        """
        setup.home_banner_check()


    # @pytest.mark.repeat(10)
    def _test_tb1_main_page(self, setup):
        setup.tb1_home()




