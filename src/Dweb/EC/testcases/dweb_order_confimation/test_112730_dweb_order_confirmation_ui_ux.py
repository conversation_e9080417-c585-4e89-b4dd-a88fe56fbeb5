"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_112730_dweb_order_confirmation_ui_ux.py
@Description    :  PC订单成功页UI/UX验证
@CreateTime     :  2025/6/8 11:13
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/8 11:13
"""
import allure
import pytest
from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_order_confirmation import dweb_order_confirmation_ele
from src.Dweb.EC.dweb_pages.dweb_page_order_confirmation.dweb_page_order_confirmation import DWebOrderComfirmationPage
from src.config.weee.log_help import log


@allure.story("PC-订单成功页样式验证")
class TestWebOrderConfirmationUIUX:
    pytestmark = [pytest.mark.dweb_regression]
    
    @allure.title("PC-订单成功页样式验证")
    @pytest.mark.present
    def test_112730_dweb_order_confirmation_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【112730】 PC-订单成功页样式验证
        还要优化目前只写了生鲜订单的
        
        测试步骤:
        1. 打开订单成功页
        2. 验证弹窗存在并且标题正常展示
        3. 点击开始赚取积分按钮打开弹窗
        4. 点击关闭按钮关闭弹窗
        5. 验证订单摘要信息
        6. 验证操作按钮
        """
        p: Page = page.get("page")
        c = page.get("context")
        
        # 1. 创建订单成功页面对象
        order_confirmation_page = DWebOrderComfirmationPage(p, pc_autotest_header, browser_context=c, page_url="/order/success/v2/69581331")
        log.info("成功加载订单确认页面")
        p.wait_for_timeout(5000)
        # # 订单成功页弹窗存在
        order_confirmation_page.page.get_by_test_id(dweb_order_confirmation_ele.ele_share_popup).is_visible()
        p.wait_for_timeout(3000)
        # 2. 验证弹窗标题
        # 验证订单成功页基本元素和功能
        with allure.step("验证订单成功页弹窗基本元素和功能"):
            order_confirmation_page.verify_order_confirmation_popup()
        
        # 验证订单成功页基本元素和功能
        with allure.step("验证订单成功页基本元素和功能"):
            order_confirmation_page.verify_order_confirmation_basic_elements()

        log.info("订单确认页UI验证测试完成")