import allure
import pytest
from playwright.sync_api import Page, TimeoutError
from src.Dweb.EC.dweb_ele.dweb_rewards import dweb_rewards_ele
from src.Dweb.EC.dweb_pages.dweb_page_rewards.dweb_page_rewards_total_saving import RewardsTotalSavingPage
from src.config.weee.log_help import log
from src.common.commonui import scroll_one_page_until


@allure.story("Total Saving-绿色横幅点击流程")
class TestRewardsTotalSaving:
    pytestmark = [pytest.mark.pcrewards, pytest.mark.transaction]

    @allure.title("Total Saving-绿色横幅点击流程验证")
    def test_001_total_saving_banner_click(self, page: dict, pc_autotest_header, login_trace):
        """
        测试 Total Saving 绿色横幅点击流程
        步骤：
        1. 进入rewards页面
        2. 等待页面完全加载
        3. 点击 Total saving 绿色横幅
        4. 等待弹窗弹出
        5. 验证弹窗中出现 total saving 文案
        """
        p: Page = page.get("page")
        c = page.get("context")

        try:
            # 初始化 Total Saving 页面
            total_saving_page = RewardsTotalSavingPage(p, pc_autotest_header, browser_context=c)
            log.info("成功初始化 Total Saving 页面")

            # 等待页面完全加载
            p.wait_for_load_state("load")
            log.info("页面加载完成")

            # 1. 等待 Total saving 绿色横幅可见
            p.wait_for_selector(dweb_rewards_ele.ele_rewards_total_saving_banner, state="visible", timeout=30000)
            log.info("Total saving 绿色横幅已可见")

            # 2. 点击 Total saving 绿色横幅
            total_saving_page.click_total_saving_banner()
            log.info("成功点击 Total saving 绿色横幅")
            p.wait_for_timeout(2000)

            # 3. 等待弹窗弹出（等待文案元素出现）
            log.info("开始等待弹窗弹出...")
            p.wait_for_selector(dweb_rewards_ele.ele_rewards_delivery_service_fee_savings, state="visible", timeout=30000)
            log.info("弹窗已弹出")

            # 4. 验证弹窗中出现 total saving 文案
            try:
                # 验证 Delivery and service fee savings 文案
                verification_result = total_saving_page.verify_delivery_service_fee_savings_text()
                
                if verification_result:
                    log.info("✅ 成功验证弹窗中的 total saving 文案")
                else:
                    log.error("❌ 验证弹窗中的 total saving 文案失败")
                    raise AssertionError("弹窗中未找到预期的 total saving 文案")
                
            except Exception as e:
                log.error(f"验证弹窗文案时发生异常: {str(e)}")
                raise

        except Exception as e:
            log.error(f"测试过程中发生异常: {str(e)}")
            # 添加调试信息
            try:
                log.info(f"当前页面URL: {p.url}")
                log.info(f"页面标题: {p.title()}")
                # 截图保存
                import time
                screenshot_path = f"error_screenshot_{int(time.time())}.png"
                p.screenshot(path=screenshot_path)
                log.info(f"错误截图已保存: {screenshot_path}")
            except Exception as debug_e:
                log.error(f"保存调试信息时出错: {str(debug_e)}")
            raise
