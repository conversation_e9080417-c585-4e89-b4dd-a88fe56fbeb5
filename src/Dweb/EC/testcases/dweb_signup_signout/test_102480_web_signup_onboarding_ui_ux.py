import allure
import pytest

from src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_home import DWebHomePage
from playwright.sync_api import Page

from src.common.commonui import scroll_one_page_until


@allure.story("WEB-注册-onboarding-页面UI/UX验证 -- xuzhongyuan")
class TestWebSignupOnboardingUIUX:
    pytestmark = [pytest.mark.dweb_regression]

    @allure.title("WEB-注册-onboarding-页面UI/UX验证 -- xuzhongyuan")
    def test_102480_web_signup_onboarding_ui_ux(self, page: dict, pc_autotest_header,login_trace):
        """
        【102480】 WEB-注册-onboarding-页面UI/UX验证
        """
        p:Page = page.get("page")
        c = page.get("context")
        home_page = DWebHomePage(p, pc_autotest_header, browser_context=c)
        # 1. 关闭time banner
        home_page.close_time_banner()
        # 断言time banner不存在
        assert not p.locator("#timeBanner img").is_visible()
        # 2. 切换store到Chinese
        home_page.home_switch_specific_store("Chinese")
        # 3. 点击底部各种城市
        # 3.1 要滚动到底部，否则无法点击城市，此时各城市element未加载
        scroll_one_page_until(p, "div[class*='Footer_ft_locationsWrapper']")
        _new_page = home_page.click_all_cities_on_bottom_home_page("SF Bay Area")
        _new_page.wait_for_selector("//a/img[@alt='Weee!']")
        assert _new_page.locator("//a/img[@alt='Weee!']").all()
        _new_page.close()
        # 4. 输入正确的zipcode和错误的zipcode
        home_page.change_zipcode()
        assert p.locator("#changeZipCode").is_visible()
        home_page.change_zipcode("77777")
        assert p.locator('div[class="ant-form-item-explain-error"]').is_visible()
        # 报错后将zipcode对话框关掉
        p.get_by_test_id("wid-modal-btn-close").click()
        # 5. 点击app下载按钮（AppStore、Googlestore、QRcode）
        home_page.download_app("ios")
        assert len(p.locator("div[data-framer-name='app_store_btn']").all()) == 3






