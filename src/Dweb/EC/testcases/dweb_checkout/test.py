# """
# <AUTHOR>  li.zhu
# @Version        :  V1.0.0
# ------------------------------------
# @File           :   test.py
# @Description    :
# @CreateTime     :  2025/3/14 15:48
# @Software       :  PyCharm
# ------------------------------------
# @ModifyTime     :  2025/3/14 15:48
# """
# import allure
# import pytest
# from playwright.sync_api import Page
#
# from src.Dweb.EC.dweb_elements import cart_elements, checkout_elements
# from src.Dweb.EC.dweb_pages.page_cart.page_cart import CartPage
# from src.Dweb.EC.dweb_pages.page_category.page_category import CategoryPage
# from src.Dweb.EC.dweb_pages.page_checkout.page_checkout import CheckoutPage
# from src.config.weee.log_help import log
# from src.common.commfunc import empty_cart
#
#
# @allure.story("购物车加购商品并进入结算页面")
# class TestCartToCheckout:
#
#     @allure.title("从购物车添加商品并进入结算页面")
#     @pytest.mark.present
#     def test_cart_to_checkout_flow(self, page: dict, pc_autotest_header, login_trace):
#         """
#         测试流程：
#         1. 清空购物车
#         2. 从分类页添加商品到购物车
#         3. 进入购物车页面
#         4. 点击结算按钮
#         5. 验证成功进入结算页面
#         """
#         p: Page = page.get("page")
#         c = page.get("context")
#
#         # 构建购物车页面
#         cart_page = CartPage(p, pc_autotest_header, browser_context=c)
#
#         # 清空购物车
#         try:
#             empty_cart(pc_autotest_header)
#             p.reload()
#             p.wait_for_timeout(2000)
#         except Exception as e:
#             log.info("清空购物车发生异常: " + str(e))
#
#         # 构造分类页面并添加商品
#         category_page = CategoryPage(p, pc_autotest_header, browser_context=c)
#         category_page.add_to_local_product_cart_from_category()
#         p.wait_for_timeout(2000)
#
#         # 进入购物车
#         p.get_by_test_id("wid-mini-cart").click()
#         p.wait_for_timeout(1000)
#
#         # 点击结算按钮
#         p.locator(cart_elements.ele_cart_checkout).click()
#         p.wait_for_timeout(2000)
#
#         # 构造结算页面并验证
#         checkout_page = CheckoutPage(p, pc_autotest_header, browser_context=c)
#
#         # 验证结算页面元素
#         assert p.locator(checkout_page.FE.ele(checkout_elements.ele_checkout_top_logo)).is_visible(), \
#             "结算页面logo未显示"
#
#         p.wait_for_timeout(2000)
#         p.close()