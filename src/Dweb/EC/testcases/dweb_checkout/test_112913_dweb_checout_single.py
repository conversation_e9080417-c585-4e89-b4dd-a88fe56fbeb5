"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_112913_checout_single.py
@Description    :  
@CreateTime     :  2025/3/10 13:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/3/10 13:53
"""
import allure
import pytest
from playwright.sync_api import Page, expect
from src.Dweb.EC.dweb_ele.dweb_cart import dweb_cart_ele
from src.Dweb.EC.dweb_ele.dweb_checkout import checkout_elements
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_cart import DWebCartPage
from src.Dweb.EC.dweb_pages.dweb_page_category.dweb_page_category import DWebCategorypage
from src.Dweb.EC.dweb_pages.page_checkout.page_checkout import CheckoutPage
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart




@allure.story("PC结算页-单个生鲜购物车样式验证")
class TestWebSingleCartUIUX:

    @allure.title("PC结算页-单个生鲜购物车样式验证")
    @pytest.mark.dweb_regression
    def test_112913_checkout_single_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【112913】 PC结算页-单个生鲜购物车样式验证
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 清空购物车
        with allure.step("清空购物车"):
            try:
                empty_cart(pc_autotest_header)
                p.reload()
                p.wait_for_timeout(2000)
                log.info("购物车清空成功")
            except Exception as e:
                log.error(f"清空购物车失败: {str(e)}")
                raise
        # scroll_one_page_until(p, "div[class*='Footer_ft_locationsWrapper']")
        # 去分类页加购Local类型的商品进购物车
        with allure.step("从分类页面添加商品"):
            try:
                category_page = DWebCategorypage(p, pc_autotest_header, browser_context=c)
                # 添加一个不满$35的商品，以触发凑单banner
                category_page.add_to_local_product_cart_from_category()
                p.wait_for_timeout(2000)
                log.info("商品添加成功")
            except Exception as e:
                log.error(f"添加商品失败: {str(e)}")
                raise
        # category_page.add_to_local_product_cart_from_category()
        p.wait_for_timeout(2000)
        # 进入购物车
        with allure.step("进入购物车并点击结算"):
            # # 构建购物车页面
            cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c)
            try:
                p.get_by_test_id("wid-mini-cart").click()
                p.wait_for_timeout(1000)
                p.locator(dweb_cart_ele.ele_cart_checkout).click()
                p.wait_for_timeout(2000)
                log.info("成功进入结算页")
            except Exception as e:
                log.error(f"进入结算页失败: {str(e)}")
                raise
        self.page.wait_for_timeout(3000)

        # 处理可能的upsell弹窗
        try:
            upsell_button = self.page.get_by_test_id("wid-upsell-continue-to-checkout")
            if upsell_button.is_visible(timeout=3000):
                log.info("检测到upsell弹窗，点击继续结算按钮")
                upsell_button.click()
                self.page.wait_for_timeout(3000)
            else:
                log.info("未检测到upsell弹窗，直接进行下一步")
        except Exception as e:
            log.debug(f"处理upsell弹窗时出现异常或未检测到upsell弹窗: {str(e)}")
        p.wait_for_timeout(1000)
        # 点击购物车的去结算按钮
        # p.locator(cart_elements.ele_cart_checkout).click()
        # p.wait_for_timeout(2000)
        # 构造的结算页页面
        checkout_page = CheckoutPage(p, pc_autotest_header, browser_context=c)
        # 验证凑单banner是否存在
        # with allure.step("验证凑单banner"):
        #     try:
        #         banner = p.get_by_test_id('wid-checkout-free-shipping-banner')
        #         expect(banner).to_be_visible()
        #         # 验证banner文案包含凑单金额信息
        #         banner_text = banner.text_content()
        #         assert "$35" in banner_text, "凑单banner金额不正确"
        #         log.info("凑单banner验证成功")
        #     except Exception as e:
        #         log.error(f"验证凑单banner失败: {str(e)}")
        #         raise
        # 验证会员等级模块
        with allure.step("验证会员等级显示"):
            try:
                # 获取会员等级信息
                member_level = p.get_by_test_id('wid-checkout-rewards-header')
                level_text = member_level.text_content()

                # 根据不同等级验证对应的图标和文案
                if "Bronze" in level_text.upper():
                    # p.get_by_test_id('wid-checkout-rewards-header')
                    expect(checkout_page.FE.ele("//div[@data-testid='btn-atc-plus']").locator('img')).to_be_visible()
                    expect(p.locator("text=Bronze Rewards member")).to_be_visible()
                elif "Silver" in level_text.upper():
                    expect(p.get_by_test_id('wid-checkout-rewards-header').locator('img')).to_be_visible()
                    expect(p.locator("text=Silver Rewards member")).to_be_visible()
                elif "Gold" in level_text.upper():
                    expect(p.get_by_test_id('wid-checkout-rewards-header').locator('img')).to_be_visible()
                    expect(p.locator("text=Gold Rewards member")).to_be_visible()

                log.info(f"会员等级验证成功: {level_text}")
            except Exception as e:
                log.error(f"验证会员等级显示失败: {str(e)}")
                raise

        # with allure.step("验证地址列表功能"):
        #     try:
        #         # 点击地址箭头
        #         address_arrow = p.locator("[data-testid='wid-checkout-address-selector']")
        #         address_arrow.click()
        #         p.wait_for_timeout(1000)
        #
        #         # 验证地址列表是否加载
        #         address_list = p.locator("[data-testid='wid-checkout-address-list']")
        #         expect(address_list).to_be_visible()
        #
        #         # 验证是否至少有一个地址选项
        #         address_items = address_list.locator("li")
        #         expect(address_items).to_have_count(greater_than=0)
        #
        #         log.info("地址列表功能验证成功")
        #     except Exception as e:
        #         log.error(f"验证地址列表功能失败: {str(e)}")
        #         raise
        with allure.step("验证小费模块功能"):
            # 验证小费功能
            try:
                # 获取当前小费标题
                assert "Delivery tip" == p.locator(checkout_elements.ele_checkout_tip_title).text_content()
                # 小费模块底部的文案存在
                p.locator("//div[@data-testid='wid-checkout-delivery-tip']/div[3]").is_visible()

                log.info("小费模块文案验证成功")
            except Exception as e:
                log.error(f"小费模块文案验证失败: {str(e)}")
                raise

        with allure.step("验证最终总计下面的文案存在"):
            # 验证结算功能
            try:
                # 获取最终总计下面的文案
                p.locator(checkout_elements.ele_checkout_final_text).is_visible()
                # 获取提交订单的文案
                p.locator(checkout_elements.ele_checkout_final_submit_text).is_visible()
                p.locator("//a[@class='font-medium']").click()
                p.wait_for_timeout(2000)

                log.info("最终总计下面的文案验证成功")
            except Exception as e:
                log.error(f"最终总计下面的文案验证验证失败: {str(e)}")
                raise

        # tip_amount = p.locator("[data-testid='wid-checkout-tip-amount']")
        # initial_tip = tip_amount.text_content()
        #
        # # 点击小费选择下拉框
        # p.locator("[data-testid='wid-checkout-tip-selector']").click()
        # p.wait_for_timeout(1000)
