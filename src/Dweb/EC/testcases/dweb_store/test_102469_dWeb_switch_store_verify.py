# test_102469_dWeb_switch_store_verify.py
import allure
import pytest
import random

# Import the log object from your configuration
from src.config.weee.log_help import log

from src.Dweb.EC.dweb_pages.dweb_page_store.dweb_page_store import DWebStorePage
from src.common.commonui import close_advertise_on_home


@allure.story("首页商店切换")
class TestDwebStoreSwitch:
    pytestmark = [pytest.mark.dweb_regression]

    @pytest.fixture(scope='class')
    def setup_store_page(self, page: dict, pc_autotest_header):
        store_page = DWebStorePage(page=page["page"], header=pc_autotest_header)
        store_page.page.goto(store_page.base_url)
        store_page.page.wait_for_load_state("networkidle")
        close_advertise_on_home(store_page.page)
        yield store_page

    @allure.title("切换到非当前商店")
    def test_switch_to_non_current_store(self, setup_store_page: DWebStorePage, login_trace):
        """
        测试从当前商店切换到任意一个非当前商店。
        """
        # 1. 获取当前选中的商店
        current_store = setup_store_page.get_current_store_name()
        if not current_store:
            pytest.fail("无法确定当前选中的商店")

        # 2. 获取所有未选中的商店列表
        unselected_stores = setup_store_page.get_unselected_store_names()
        if not unselected_stores:
            pytest.skip("没有其他商店可供切换")

        # 3. 从未选中的列表中随机选择一个商店
        target_store = random.choice(unselected_stores)

        # Now this line will work correctly because `log` is imported
        log.info(f"当前商店: {current_store}，将切换到: {target_store}")

        # 4. 执行切换操作
        setup_store_page.switch_to_store(target_store)

        # 5. 验证切换是否成功
        # 验证 URL 参数
        expected_url_param = setup_store_page.get_url_param_for_store(target_store)
        current_url = setup_store_page.page.url
        assert expected_url_param in current_url, f"URL 验证失败。期望URL包含 '{expected_url_param}'，实际为 '{current_url}'"

        # 验证页面选中状态
        assert setup_store_page.is_store_selected(target_store), f"页面上没有找到 '{target_store}' 商店已选中的元素。"