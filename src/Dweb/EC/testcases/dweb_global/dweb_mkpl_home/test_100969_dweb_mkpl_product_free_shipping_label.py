import allure
import pytest
from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_page_global.dweb_page_mkpl_all_store.dweb_page_mkpl_search import DWebMkplSearchPage
from src.Dweb.EC.dweb_ele.dweb_mkpl_global.dweb_mkpl_global_ele import global_product_free_shipping_label
from src.config.weee.log_help import log


@allure.story("【100969】PC端商品免运费标签功能")
class TestDWebMkplProductFreeShippingLabel:
    pytestmark = [pytest.mark.dweb_regression, pytest.mark.present]

    @allure.title("【100969】PC端商品免运费标签测试")
    def test_100969_dweb_mkpl_product_free_shipping_label(self, page: dict, pc_autotest_header):
        """
        【100969】PC端商品免运费标签测试
        该测试用例主要的测试点为：
        1. 访问主页
        2. 搜索商品
        3. 检查免运费标签内容和样式
        """
        p: Page = page.get("page")
        c = page.get("context")
        
        # 构造搜索页面操作实例
        search_page = DWebMkplSearchPage(p)

        # 执行搜索流程测试
        with allure.step("执行搜索流程测试"):
            # 访问主页
            search_page.navigate_to_homepage()
            
            # 搜索商品
            assert search_page.search_mkpl_product(), "搜索商品失败"
            
            # 等待6秒检查搜索结果
            p.wait_for_timeout(6000)
            
            # 检查免运费标签
            free_shipping_label = p.get_by_test_id(global_product_free_shipping_label)
            if free_shipping_label.count() > 0:
                # 检查标签内容不为空
                label_text = free_shipping_label.first.text_content()
                assert label_text.strip(), "免运费标签内容为空"
                log.info(f"免运费标签内容: {label_text}")
                
                """检查标签样式
                bg_color = free_shipping_label.first.evaluate("element => getComputedStyle(element).backgroundColor")
                text_color = free_shipping_label.first.evaluate("element => getComputedStyle(element).color")
                
                expected_bg_color = "rgb(198, 229, 255)"
                expected_text_color = "rgb(0, 27, 165)"
                
                assert bg_color == expected_bg_color, f"免运费标签背景色不匹配，期望: {expected_bg_color}, 实际: {bg_color}"
                assert text_color == expected_text_color, f"免运费标签文字颜色不匹配，期望: {expected_text_color}, 实际: {text_color}"
                log.info(f"免运费标签样式验证通过 - 背景色: {bg_color}, 文字颜色: {text_color}")"""
            else:
                pytest.skip("免运费标签不存在，跳过样式检查")
            
            log.info("免运费标签测试完成")