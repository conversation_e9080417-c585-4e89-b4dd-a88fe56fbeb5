import allure
import pytest
from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_page_global.dweb_page_mkpl_all_store.dweb_page_mkpl_search import DWebMkplSearchPage
from src.Dweb.EC.dweb_ele.dweb_mkpl_global.dweb_mkpl_global_ele import close_popup_on_home
from src.config.weee.log_help import log


@allure.story("PC端商家快速搜索功能")
class TestDWebSellerQuickSearch:
    pytestmark = [pytest.mark.dweb_regression, pytest.mark.present]

    @allure.title("[102326] PC端商家快速搜索测试")
    def test_102326_dweb_seller_quick_search(self, page: dict, pc_autotest_header):
        """
        [102326] PC端商家快速搜索测试
        测试步骤：
        1. 访问主页https://www.sayweee.com/zh
        2. 检查是否有弹窗close_popup_on_home，如果有就先关闭
        3. 输入商家名称: Woo Japan
        4. 回车确认发起搜索
        5. 等待3秒后，断言url检查页面是否跳转到: https://www.sayweee.com/en/mkpl/vendor/6887
        """
        p: Page = page.get("page")
        c = page.get("context")
        
        # 构造搜索页面操作实例
        search_page = DWebMkplSearchPage(p)

        # 执行商家搜索测试
        with allure.step("执行商家搜索测试"):
            # 执行商家快速搜索流程
            search_page.seller_quick_search("Woo Japan")

            # 等待6秒检查搜索结果
            p.wait_for_timeout(6000)
            
            # 检查URL跳转
            current_url = p.url
            
            # 断言URL中包含mkpl/vendor/6887
            assert "mkpl/vendor/6887" in current_url, f"页面跳转失败，URL中不包含mkpl/vendor/6887: {current_url}"
            log.info(f"商家搜索跳转验证成功: {current_url}")
            
            log.info("商家快速搜索测试完成")