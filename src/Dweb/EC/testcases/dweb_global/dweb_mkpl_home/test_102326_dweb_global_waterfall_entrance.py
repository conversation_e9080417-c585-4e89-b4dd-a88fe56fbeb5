import allure
import pytest
from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_page_global.dweb_page_mkpl_home.dweb_page_global_entrance import DWebGlobalEntrancePage
from src.config.weee.log_help import log


@allure.story("PC端Global+瀑布流入口功能")
class TestDWebGlobalWaterfallEntrance:
    pytestmark = [pytest.mark.dweb_regression, pytest.mark.present]

    @allure.title("[102326] PC端Global+瀑布流入口测试")
    def test_102326_dweb_global_waterfall_entrance(self, page: dict, pc_autotest_header):
        """
        [102326] PC端Global+瀑布流入口测试
        测试步骤：
        1. 访问主站：https://www.sayweee.com
        2. 等待8秒，检查close_popup_on_home元素是否存在，如果存在则点击，如果不存在则跳过
        3. 检查当前页面可视范围内ele_pc_home_global_entrance元素是否存在，如果存在则点击该元素
        4. 断言点击ele_pc_home_global_entrance元素后的url包含'mkpl/waterfall'字符
        """
        p: Page = page.get("page")

        # 构造Global+入口页面操作实例
        entrance_page = DWebGlobalEntrancePage(p)

        # 执行Global+瀑布流入口测试
        with allure.step("执行Global+ Waterfall入口测试"):
            # 执行导航和点击Global+入口流程
            assert entrance_page.navigate_and_click_global_entrance(), "Global+入口点击失败"
            
            # 等待3秒让页面跳转完成
            p.wait_for_timeout(3000)
            
            # 检查URL跳转
            current_url = p.url
            
            # 断言URL中包含mkpl/waterfall
            assert "mkpl/waterfall" in current_url, f"页面跳转失败，URL中不包含mkpl/waterfall: {current_url}"
            log.info(f"Global+瀑布流入口跳转验证成功: {current_url}")
            
            log.info("Global+瀑布流入口测试完成")
