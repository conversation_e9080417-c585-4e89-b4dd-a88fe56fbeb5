import datetime
from src.config.base_config import TEST_URL
from playwright.sync_api import Page
from src.Dweb.EC.dweb_ele.dweb_product.dweb_pdp_ele import *
from src.api.porder import update_pickup_date
from src.common.commfunc import empty_cart

from src.config.weee.log_help import log
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage


class DWebPDPPage(DWebCommonPage):
    """
    这个类主要封装了PC web端对pdp页面的封装, pdp页面表示的是商品详情页面
    """
    def __init__(self, page: Page, header, browser_context, page_url: str = ''):
        """
        构造方法主要包含以下功能：
        1. 进入pdp页面
        2. 清空购物车
        3. 更新送货日期，否则fbw容易没货
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入PDP页面
        self.page.goto(TEST_URL + page_url)
        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        # 更新送货日期，否则fbw容易没货
        update_pickup_date(self.header, (datetime.datetime.now() + datetime.timedelta(days=2)).strftime('%Y-%m-%d'))

    def goto_pdp_and_check(self, pdp_url):
        """
        此方法包含以下功能：
        1. 进入pdp页面
        2. 校验页面， 包括商品的title, price, thumbnail, primary image
        3. 将related商品加入购物车
        """
        # 1. 进入pdp页面
        self.page.goto(pdp_url)

        # 2. 校验页面
        self._check_pdp_page()

        # 3. 将related商品加入购物车
        related_add_to_cart_button_list = self.FE.eles(ele_pdp_related_add_to_cart_button)
        self.add_pdp_related_products_to_cart(related_add_to_cart_button_list, 3)

    def goto_fbw_pdp_and_check(self, url):
        """
        此方法包含以下功能：
        1. 进入fbw pdp页面, fbw指的是fresh bakery的商品
        2. 校验页面， 包括商品的title, price, thumbnail, primary image
        3. 将related商品加入购物车
        """
        # 1. 进入fbw pdp页面
        self.page.goto(url)

        # 2. 元素校验
        self._check_fbw_pdp_page()

        # 3. 将fbw mweb_page_pdp related商品加入购物车
        # 这种用locator的方式有问题，会导致页面刷新，按钮次序不对
        # fbw_related_products_add_to_cart_buttons = self.page.get_by_test_id("wid-mweb_page_pdp-related-card").get_by_test_id("btn-atc-plus").all()
        fbw_related_products_add_to_cart_buttons = self.page.get_by_test_id("wid-mweb_page_pdp-related-card").get_by_test_id(
            "btn-atc-plus").element_handles()
        if fbw_related_products_add_to_cart_buttons:
            self.add_pdp_related_products_to_cart(fbw_related_products_add_to_cart_buttons, 3)

    def goto_pdp_and_check_same_vendor(self, pdp_url):
        """
        此方法包含以下功能：
        1. 进入pdp页面
        2. 校验页面基本元素
        3. 校验店铺推荐模块
        4. 验证店铺推荐商品信息（商品名、价格）
        5. 加购店铺推荐商品
        """
        # 1. 进入pdp页面
        self.page.goto(pdp_url)

        # 2. 校验页面
        self._check_pdp_page()

        # 3. 滚动到店铺推荐模块
        from src.common.commonui import scroll_one_page_until
        scroll_one_page_until(self.page, ele_pdp_same_vendor_card)

        # 4. 校验店铺推荐模块
        self._check_same_vendor_module()

        # 5. 加购店铺推荐商品
        self._add_same_vendor_products_to_cart()

    def _check_same_vendor_module(self):
        """
        校验店铺推荐模块元素
        """
        # 校验店铺推荐模块存在
        assert self.FE.ele(ele_pdp_same_vendor_card).is_visible(), "店铺推荐模块不可见"

        # 校验店铺推荐标题
        assert self.FE.ele(ele_pdp_same_vendor_title).is_visible(), "店铺推荐标题不可见"

        # 校验商品卡片存在
        same_vendor_products = self.FE.eles(ele_pdp_same_vendor_product_card)
        assert len(same_vendor_products) > 0, "店铺推荐商品卡片不存在"

        # 校验第一个商品的基本信息
        first_product = same_vendor_products[0]
        product_name = first_product.query_selector("div[@data-testid='wid-product-card-title']").text_content()
        product_price = first_product.query_selector("div[@data-testid='wid-product-card-price']").text_content()

        assert product_name, "商品名称为空"
        assert product_price and product_price.startswith("$"), "商品价格格式不正确"

        log.info(f"店铺推荐商品名称: {product_name}")
        log.info(f"店铺推荐商品价格: {product_price}")

    def _add_same_vendor_products_to_cart(self):
        """
        加购店铺推荐商品
        """
        # 获取加购按钮
        atc_buttons = self.FE.eles(ele_pdp_same_vendor_atc_btn)

        if atc_buttons:
            # 点击第一个商品的加购按钮
            first_atc_btn = atc_buttons[0]
            # 先hover到商品卡片上显示加购按钮
            product_cards = self.FE.eles(ele_pdp_same_vendor_product_card)
            if product_cards:
                product_cards[0].hover()
                self.page.wait_for_timeout(1000)
                first_atc_btn.click()
                log.info("成功加购店铺推荐商品")
    def _check_pdp_page(self):
        """
        该方法包含以下功能：
        1. 校验pdp页面的元素，包括商品的title, price, thumbnail, primary image
        2. pdp_dweb_page_common_check方法是基类提供的，在子类中调用
        """
        # networkidle比load, domcontentloaded更稳定
        self.page.wait_for_load_state("networkidle", timeout=60000)
        self.pdp_dweb_page_common_check()
        self.FE.ele(ele_pdp_header_content).is_visible()
        self.page.get_by_test_id("wid-mweb_page_pdp-related-card").is_visible()

    def _check_fbw_pdp_page(self):
        """
        该方法包含以下功能：
        1. 校验fbw pdp页面的元素，包括商品的title, price, thumbnail, primary image
        2. pdp_dweb_page_common_check方法是基类提供的，在子类中调用
        3. fbw商品与普通商品有细微区别：页面上有Freshly Made的span
        """
        self.page.wait_for_timeout(3000)
        self.pdp_dweb_page_common_check()
        assert self.FE.ele("//span[text()=' Freshly Made']").is_visible()
        assert self.FE.ele("//span[text()='Shop more']").is_visible()
        # 商品名
        assert self.page.locator("div[class^='Header_desc'] h2").is_visible()
        # 商品 subname
        assert self.page.locator("div[class^='Header_subname']").is_visible()
        # 商品价格
        assert self.page.locator(
            "div[class^='Header_desc'] div[class^='Header_price_price']").text_content().startswith("$")
        # related products
        assert self.page.get_by_test_id("wid-pdp-related-card").get_by_test_id(
            "wid-product-card-container").all(), f"fbw pdp页面没有找到related products"

    def goto_global_fbw_pdp_and_check(self, pdp_url):
        """
        此方法包含以下功能：
        1. 进入Global FBW pdp页面
        2. 校验页面基本元素
        3. 校验Global FBW配送信息模块
        4. 验证配送信息内容的完整性
        """
        # 1. 进入pdp页面
        self.page.goto(pdp_url)

        # 2. 校验页面基本元素
        self._check_pdp_page()

        # 3. 校验Global FBW配送信息模块
        self._check_global_fbw_info()

    def _check_global_fbw_info(self):
        """
        校验Global FBW配送信息模块的所有元素
        """
        from src.Dweb.EC.dweb_ele.dweb_product import dweb_pdp_ele

        # 校验配送信息模块主容器存在
        promotion_module = self.FE.ele(dweb_pdp_ele.ele_pdp_global_fbw_info)
        assert promotion_module.is_visible(), "Global FBW配送信息模块不可见"

        # 校验配送图标
        promotion_icon = self.FE.ele(dweb_pdp_ele.ele_pdp_global_fbw_info_icon)
        assert promotion_icon.is_visible(), "配送图标不可见"

        # 验证图标属性
        icon_alt = promotion_icon.get_attribute("alt")
        assert icon_alt == "promotion fulfilled by weee", f"图标alt属性不正确: {icon_alt}"

        icon_width = promotion_icon.get_attribute("width")
        icon_height = promotion_icon.get_attribute("height")
        assert icon_width == "30" and icon_height == "30", f"图标尺寸不正确: {icon_width}x{icon_height}"

        # 校验标题元素
        title_element = self.FE.ele(dweb_pdp_ele.ele_pdp_global_fbw_info_title)
        assert title_element.is_visible(), "配送标题不可见"
        title_text = title_element.text_content()
        assert "Fulfilled by" in title_text, f"标题文本不包含'Fulfilled by': {title_text}"

        # 校验内嵌的Weee logo
        weee_logo = self.FE.ele(dweb_pdp_ele.ele_pdp_global_fbw_info_weee_logo)
        assert weee_logo.is_visible(), "Weee logo不可见"

        logo_width = weee_logo.get_attribute("width")
        logo_height = weee_logo.get_attribute("height")
        assert logo_width == "41" and logo_height == "12", f"Weee logo尺寸不正确: {logo_width}x{logo_height}"

        # 校验副标题元素
        subtitle_element = self.FE.ele(dweb_pdp_ele.ele_pdp_global_fbw_info_subtitle)
        assert subtitle_element.is_visible(), "配送副标题不可见"
        subtitle_text = subtitle_element.text_content()
        assert "Sold by" in subtitle_text, f"副标题不包含销售商信息: {subtitle_text}"
        assert "Delivered with your groceries" in subtitle_text, f"副标题不包含配送信息: {subtitle_text}"

        # 验证模块样式
        module_classes = promotion_module.get_attribute("class")
        assert "flex items-center justify-between py-2.5" in module_classes, f"模块样式类不正确: {module_classes}"

        log.info(f"Global FBW配送标题: {title_text}")
        log.info(f"Global FBW配送副标题: {subtitle_text}")
        log.info("Global FBW配送信息模块校验完成")

    def goto_pdp_and_check_promotions(self, pdp_url):
        """
        此方法包含以下功能：
        1. 进入pdp页面
        2. 校验页面基本元素
        3. 检查是否存在活动模块
        4. 如果存在活动，则校验活动元素
        5. 测试活动弹窗功能
        """
        # 1. 进入pdp页面
        self.page.goto(pdp_url)

        # 2. 校验页面基本元素
        self._check_pdp_page()

        # 3. 检查并校验活动模块
        self._check_product_promotions()

    def _check_product_promotions(self):
        """
        检查并校验商品活动模块
        """
        from src.Dweb.EC.dweb_ele.dweb_product import dweb_pdp_ele

        # 检查活动模块是否存在
        promotions_module = self.FE.ele(dweb_pdp_ele.ele_pdp_product_promotions)

        if not promotions_module or not promotions_module.is_visible():
            log.info("商品页面不存在活动模块，跳过活动验证")
            return False

        log.info("发现商品活动模块，开始验证活动内容")

        # 校验活动模块存在
        assert promotions_module.is_visible(), "活动模块不可见"

        # 校验单个活动项
        promotion_item = self.FE.ele(dweb_pdp_ele.ele_pdp_product_promotion)
        assert promotion_item.is_visible(), "活动项不可见"

        # 校验活动图标
        promotion_icon = self.FE.ele(dweb_pdp_ele.ele_pdp_product_promotion_icon)
        assert promotion_icon.is_visible(), "活动图标不可见"

        # 验证图标属性
        icon_alt = promotion_icon.get_attribute("alt")
        icon_src = promotion_icon.get_attribute("src")
        assert icon_alt, "活动图标alt属性为空"
        assert icon_src, "活动图标src属性为空"

        # 校验活动标题
        promotion_title = self.FE.ele(dweb_pdp_ele.ele_pdp_product_promotion_title)
        assert promotion_title.is_visible(), "活动标题不可见"
        title_text = promotion_title.text_content()
        assert title_text, "活动标题为空"

        # 校验活动规则描述
        promotion_desc = self.FE.ele(dweb_pdp_ele.ele_pdp_product_promotion_rule_desc)
        assert promotion_desc.is_visible(), "活动规则描述不可见"
        desc_text = promotion_desc.text_content()
        assert desc_text, "活动规则描述为空"

        # 校验查看更多按钮
        promotion_button = self.FE.ele(dweb_pdp_ele.ele_pdp_product_promotion_button)
        assert promotion_button.is_visible(), "查看更多按钮不可见"

        # 校验按钮文本
        button_text_element = self.FE.ele(dweb_pdp_ele.ele_pdp_product_promotion_button_text)
        assert button_text_element.is_visible(), "查看更多按钮文本不可见"
        button_text = button_text_element.text_content()
        assert button_text, "查看更多按钮文本为空"

        log.info(f"活动标题: {title_text}")
        log.info(f"活动描述: {desc_text}")
        log.info(f"按钮文本: {button_text}")

        # 测试活动弹窗功能
        self._test_promotion_drawer(promotion_button)

        return True

    def _test_promotion_drawer(self, promotion_button):
        """
        测试活动弹窗功能
        """
        from src.Dweb.EC.dweb_ele.dweb_product import dweb_pdp_ele

        # 点击查看更多按钮
        promotion_button.click()
        self.page.wait_for_timeout(2000)

        # 校验活动弹窗出现
        promotion_drawer = self.FE.ele(dweb_pdp_ele.ele_pdp_promotion_drawer)
        assert promotion_drawer.is_visible(), "活动弹窗未出现"

        # 验证弹窗属性
        drawer_role = promotion_drawer.get_attribute("role")
        drawer_state = promotion_drawer.get_attribute("data-state")
        assert drawer_role == "dialog", f"弹窗role属性不正确: {drawer_role}"
        assert drawer_state == "open", f"弹窗状态不正确: {drawer_state}"

        # 验证弹窗样式
        drawer_classes = promotion_drawer.get_attribute("class")
        expected_classes = ["bg-white", "flex", "flex-col", "h-full", "fixed"]
        for expected_class in expected_classes:
            assert expected_class in drawer_classes, f"弹窗缺少样式类'{expected_class}': {drawer_classes}"

        log.info("活动弹窗显示正常")

        # 关闭弹窗（通过ESC键或点击外部区域）
        self.page.keyboard.press("Escape")
        self.page.wait_for_timeout(1000)

        # 验证弹窗已关闭
        try:
            drawer_state_after = promotion_drawer.get_attribute("data-state")
            if drawer_state_after == "open":
                # 如果ESC键无效，尝试点击外部区域
                self.page.click("body", position={"x": 100, "y": 100})
                self.page.wait_for_timeout(1000)
        except:
            pass  # 弹窗可能已经完全移除

        log.info("活动弹窗关闭测试完成")

