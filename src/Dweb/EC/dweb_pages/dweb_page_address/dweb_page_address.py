import pytest
from playwright.sync_api import Page

from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from src.Dweb.EC.dweb_ele.dweb_home.dweb_porder.dweb_address.dweb_address_ele import *


class DWebAddressPage(DWebCommonPage):
    """
    这个类主要封装了PC web端对地址页面的操作
    """
    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011", page_url: str = None):

        """
        构造方法
        """
        super().__init__(page, header)
        self.bc = browser_context
        self.page.goto(TEST_URL + page_url)
        self.page.wait_for_timeout(10000)
        close_advertise_on_home(self.page)
        # # 获取顶部语言
        # self.home_page_switch_lang(lang="English")
        # # 获取顶部zipocde
        # self.home_page_switch_zipcode(zipcode)

    def add_new_address_from_home(self):
        """
        从首页添加新地址
        """
        # 点击首页地址按钮
        self.page.get_by_test_id(ele_home_zipcode).click()
        self.page.wait_for_timeout(2000)

        # 点击新增地址按钮
        self.page.get_by_test_id(zipcode_pop_add_btn).click()
        self.page.wait_for_timeout(3000)

        # 填写地址表单
        self._fill_address_form()

        # # 验证地址是否添加成功
        # self.page.get_by_test_id("wid-zipcode").click()
        # self.page.wait_for_timeout(2000)
        #
        # # 检查地址簿中是否有新增的地址
        # assert self.page.get_by_test_id("wid-address-name").filter(has_text="Test Automation").is_visible(), "首页地址簿中未找到新增的地址"

        # 清理：删除测试地址
        self.page.goto(TEST_URL + "/account/settings")
        self.page.wait_for_timeout(4000)
        self._delete_test_address()
        assert self.page.locator("div[data-name='me_delivery_address']").is_visible()

        log.info("从首页添加新地址成功")

    def add_new_address_from_account(self):
        """
        从账户页面添加新地址
        """
        # 进入账户页面
        self.page.goto(TEST_URL + "/account/settings")
        self.page.wait_for_timeout(3000)

        # 点击地址管理
        self.page.locator("div[data-name='me_delivery_address']").click()
        self.page.wait_for_timeout(3000)

        # 点击新增地址按钮
        self.page.locator("//span[text()='Add a new address']").click()
        self.page.wait_for_timeout(2000)

        # 填写地址表单
        self._fill_address_form()

        # 清理：删除测试地址
        self._delete_test_address()

        log.info("从账户页面添加新地址成功")

    def add_new_address_from_checkout(self):
        """
        从结算页面添加新地址
        """
        # 添加商品到购物车（因为空购物车无法进入结算页面）
        # 点击首页上的第一个商品的加购按钮
        try:
            # 尝试点击首页上的第一个加购按钮
            self.page.get_by_test_id("btn-atc-plus").first.click()
            self.page.wait_for_timeout(2000)
            log.info("成功添加商品到购物车")
        except Exception as e:
            raise Exception("首页没有找到加购按钮")

        # 点击购物车图标进入购物车页面
        self.page.get_by_test_id("wid-mini-cart").click()
        self.page.wait_for_timeout(3000)
        self.page.locator("//a[@aria-label='Weee! Logo']").hover()
        self.page.wait_for_timeout(3000)

        # 点击结算按钮
        self.page.get_by_test_id("wid-cart-summary-checkout").click()
        self.page.wait_for_timeout(5000)

        # 偶现弹出alcohol agreement，还要处理alcohol agreement

        # 如果有多购物车，则要处理多购物车
        if self.page.get_by_test_id("wid-cart-select-modal-select-all-btn").all():
            self.page.get_by_test_id("wid-cart-select-modal-select-all-btn").click()
            self.page.wait_for_timeout(3000)
            self.page.get_by_test_id("wid-cart-select-modal-checkout-btn").click()
            self.page.wait_for_timeout(5000)

        # 如果有upsell,处理upsell
        if self.page.get_by_test_id("wid-upsell-continue-to-checkout").all():
            self.page.get_by_test_id("wid-upsell-continue-to-checkout").click()
            self.page.wait_for_timeout(3000)

        # 点击新建地址按钮
        # 如果已有地址
        if self.page.get_by_test_id("wid-checkout-address-info-btn").all():
            self.page.get_by_test_id("wid-checkout-address-info-btn").locator("//span").click()
            self.page.wait_for_timeout(2000)
        # 如果没有地址
        else:
            self.page.locator("//i[contains(@class, 'cursor-pointer')]").first.click()
            self.page.wait_for_timeout(2000)
            if self.page.get_by_test_id("wid-checkout-address-info-btn").all():
                self.page.get_by_test_id("wid-checkout-address-info-btn").locator("//span").click()
                self.page.wait_for_timeout(5000)
            else:
                pytest.skip(reason="没有找到新建地址按钮")


        # 填写地址表单
        self._fill_address_form()

        # 清理：删除测试地址（需要进入账户页面删除）
        self.page.goto(TEST_URL + "/account/settings")
        self.page.wait_for_timeout(3000)
        self._delete_test_address()

        log.info("从结算页面添加新地址成功")

    def add_new_address_from_order_detail(self, order_id):
        """
        从订单详情页添加新地址
        """
        # 进入订单详情页
        self.page.goto(TEST_URL + f"/account/orders/{order_id}")
        self.page.wait_for_timeout(3000)

        # 点击更改地址
        self.page.get_by_test_id("btn-change-address").click()
        self.page.wait_for_timeout(2000)

        # 点击新增地址按钮
        self.page.get_by_test_id("btn-add-new-address").click()
        self.page.wait_for_timeout(2000)

        # 填写地址表单
        self._fill_address_form()

        # 验证地址是否添加成功
        assert self.page.get_by_test_id("wid-address-name").filter(has_text="Test Automation").is_visible(), "订单详情页地址簿中未找到新增的地址"

        # 清理：删除测试地址（需要进入账户页面删除）
        self.page.goto(TEST_URL + "/account/settings")
        self.page.wait_for_timeout(3000)
        self._delete_test_address()

        log.info("从订单详情页添加新地址成功")

    def fill_address_form_before_save(self, first_name="Bill", last_name="Smitch", phone="**********", 
                                street="15006 104th Ave NE", city="Bothell", state="WA", zipcode="98011", 
                                note="PC UI自动化测试"):
        """
        在Add a new address页面填写表单到点击保存之前
        
        Args:
            first_name: 名字
            last_name: 姓氏
            phone: 电话号码
            street: 街道地址
            city: 城市
            state: 州/省
            zipcode: 邮编
            note: 备注
        """
        # 输入姓名（先清空再输入）
        self.page.get_by_test_id(firstname_input_field).fill("")
        self.page.get_by_test_id(firstname_input_field).fill(first_name)
        self.page.get_by_test_id(lastname_input_field).fill("")
        self.page.get_by_test_id(lastname_input_field).fill(last_name)

        # 输入电话（先清空再输入）
        self.page.get_by_test_id(phone_input).fill("")
        self.page.get_by_test_id(phone_input).fill(phone)

        # 输入街道地址（先清空再输入）
        self.page.get_by_test_id(street_input).fill("")
        self.page.get_by_test_id(street_input).fill(street)
        self.page.get_by_test_id(city_input).fill("")
        self.page.get_by_test_id(city_input).fill(city)
        self.page.get_by_test_id(state_input).fill("")
        self.page.get_by_test_id(state_input).fill(state)
        # 选择州
        self.page.get_by_test_id(state_input).click()
        self.page.get_by_test_id(zipcode_input).fill("")
        self.page.get_by_test_id(zipcode_input).fill(zipcode)

        # 输入备注（先清空再输入）
        self.page.get_by_test_id(note_textarea).fill("")
        self.page.get_by_test_id(note_textarea).fill(note)
        
        log.info("地址表单填写完成，等待保存")
        
    def _fill_address_form(self):
        """
        填写地址表单
        """
        # 调用公共方法填写表单
        self.fill_address_form_before_save()
        
        # 点击保存按钮
        self.page.get_by_test_id(save_btn).click()
        self.page.wait_for_timeout(3000)
        # 处理可能出现的"Not now"按钮
        if self.page.locator("//button[text()='Not now']").all():
            self.page.locator("//button[text()='Not now']").click()
            self.page.wait_for_timeout(2000)

    def _delete_test_address(self):
        """
        删除测试地址
        """
        # 找到address book div
        self.page.locator("div[data-name='me_delivery_address']").click()
        self.page.wait_for_timeout(3000)
        # 进入地址列表
        test_address = self.page.locator("//div[@data-id]//div[contains(text(), 'Bill')]//..//following-sibling::div[@data-testid]")
        if test_address.is_visible():
            log.info("找到测试地址")
            # 点击edit按钮
            test_address.click()
            self.page.wait_for_timeout(1000)

            # 确认删除
            self.page.locator("//button[text()='Delete the address']").click()
            self.page.wait_for_timeout(2000)

            log.info("测试地址删除成功")

    def verify_fresh_order_change_address_entry(self, order_id: str):
        """验证生鲜订单显示修改地址入口"""
        # self.page.goto(TEST_URL + f"/order/detail/{order_id}")
        self.page.wait_for_timeout(2000)
        
        change_address_btn = self.page.get_by_test_id("btn-order-detail-delivery-address-edit")
        from playwright.sync_api import expect
        expect(change_address_btn).to_be_visible()
        log.info("生鲜订单修改地址入口验证成功")
        return True

    def verify_non_fresh_order_no_change_address_entry(self, order_id: str = "42664253"):
        """验证非生鲜订单不显示修改地址入口"""
        self.page.goto(TEST_URL + f"/order/detail/{order_id}")
        self.page.wait_for_timeout(2000)
        
        change_address_btn = self.page.get_by_test_id("btn-order-detail-delivery-address-edit")
        from playwright.sync_api import expect
        expect(change_address_btn).not_to_be_visible()
        log.info("非生鲜订单不显示修改地址入口验证成功")
        return True

    def verify_cross_region_address_change_error(self, order_id: str):
        """验证跨区域修改地址提示错误"""
        # self.page.goto(TEST_URL + f"/order/detail/{order_id}")
        self.page.wait_for_timeout(2000)
        
        # 点击修改地址按钮
        # self.page.get_by_test_id("btn-order-detail-delivery-address-edit").click()
        # self.page.wait_for_timeout(2000)
        
        # 获取当前选中地址的zipcode
        current_zipcode = self.page.locator(".address-item.selected .zipcode").text_content()
        
        # 选择一个不同 zipcode 的地址
        address_items = self.page.get_by_test_id("wid-order-address-content-address-list-items")
        for i in range(address_items.count()):
            address_zipcode = address_items.nth(i).locator(".zipcode").text_content()
            if address_zipcode != current_zipcode:
                address_items.nth(i).click()
                self.page.wait_for_timeout(1000)
                break
        
        # 验证错误提示
        error_msg = self.page.get_by_test_id("wid-order-address-content-address-list-item-error")
        assert error_msg.is_visible()
        log.info("跨区域修改地址错误提示验证成功")
        return True

    def verify_no_service_address_change_error(self, order_id: str = "123456"):
        """验证修改到无服务地址提示错误--这个case线上不好复现"""
        self.page.goto(TEST_URL + f"/order/detail/{order_id}")
        self.page.wait_for_timeout(2000)
        self.page.locator("[data-testid='btn-change-address']").click()
        self.page.wait_for_timeout(1000)
        
        self.page.locator("[data-testid='address-option-no-service']").click()
        self.page.wait_for_timeout(1000)
        
        error_msg = self.page.locator("[data-testid='error-message']")
        from playwright.sync_api import expect
        expect(error_msg).to_contain_text("很抱歉，您所在的区域暂不提供服务")
        log.info("无服务地址错误提示验证成功")
        return True

    def verify_same_region_meet_threshold_success(self, order_id: str = "123456"):
        """验证同区域满足门槛成功修改地址"""
        self.page.goto(TEST_URL + f"/order/detail/{order_id}")
        self.page.wait_for_timeout(2000)
        self.page.locator("[data-testid='btn-change-address']").click()
        self.page.wait_for_timeout(1000)
        
        self.page.locator("[data-testid='address-option-same-region-meet-threshold']").click()
        self.page.wait_for_timeout(1000)
        
        self.page.locator("[data-testid='btn-confirm-change']").click()
        self.page.wait_for_timeout(2000)
        
        success_msg = self.page.locator("[data-testid='success-message']")
        from playwright.sync_api import expect
        expect(success_msg).to_be_visible()
        log.info("同区域满足门槛修改地址成功")
        return True

    def verify_same_region_lower_shipping_fee_success(self, order_id: str = "123456"):
        """验证同区域运费更低允许修改"""
        self.page.goto(TEST_URL + f"/order/detail/{order_id}")
        self.page.wait_for_timeout(2000)
        self.page.locator("[data-testid='btn-change-address']").click()
        self.page.wait_for_timeout(1000)
        
        self.page.locator("[data-testid='address-option-lower-shipping-fee']").click()
        self.page.wait_for_timeout(1000)
        
        self.page.locator("[data-testid='btn-confirm-change']").click()
        self.page.wait_for_timeout(2000)
        
        success_msg = self.page.locator("[data-testid='success-message']")
        from playwright.sync_api import expect
        expect(success_msg).to_be_visible()
        log.info("运费更低地址修改成功")
        return True

    def verify_same_region_higher_shipping_fee_not_allowed(self, order_id: str = "123456"):
        """验证同区域运费更高不允许修改"""
        self.page.goto(TEST_URL + f"/order/detail/{order_id}")
        self.page.wait_for_timeout(2000)
        self.page.locator("[data-testid='btn-change-address']").click()
        self.page.wait_for_timeout(1000)
        
        self.page.locator("[data-testid='address-option-higher-shipping-fee']").click()
        self.page.wait_for_timeout(1000)
        
        error_msg = self.page.locator("[data-testid='error-message']")
        from playwright.sync_api import expect
        expect(error_msg).to_be_visible()
        log.info("运费更高地址不允许修改验证成功")
        return True