from playwright.sync_api import Locator

from src.Dweb.EC.dweb_ele import dweb_common_ele
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from playwright.sync_api import Page
from src.Dweb.EC.dweb_ele.dweb_home import dweb_home_ele
from src.Dweb.EC.dweb_ele.dweb_category.dweb_category_ele import *
from src.common.commonui import scroll_one_page_until, close_advertise_on_home
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage


class DWebCategorypage(DWebCommonPage):
    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011",page_url=None):
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入指定页面
        if page_url:
            self.page.goto(TEST_URL + page_url)
        else:
            self.page.goto(TEST_URL)
        # # 获取顶部语言
        # if not self.page.get_by_test_id("wid-language").locator(
        #         "//span[text()='English'and contains(@class,'Header')]").all():
        #     # 切换为英文
        #     pass
        # # 获取顶部zipocde
        # page_zipcode = self.page.locator(home_elements.ele_zipcode).text_content()
        # if page_zipcode != zipcode:
        #     switch_zipcode(headers=self.header)
        #     self.page.reload()
        self.page.wait_for_timeout(10000)
        close_advertise_on_home(self.page)

    def go_to_specify_category_from_home(self, category, category_selector):
        """ 进入指定分类页面，使用get_by_test_id方法 """
        try:
            # 确保在首页
            if not self.page.url.endswith(TEST_URL.split("//")[1]):
                log.info(f"当前不在首页，正在跳转到首页...")
                self.page.goto(TEST_URL)
                self.page.wait_for_timeout(3000)
            
            # 关闭可能的弹窗
            close_advertise_on_home(self.page)
            
            # 使用get_by_test_id点击指定分类元素
            log.info(f"正在查找{category}分类元素: {category_selector}")
            category_element = self.page.get_by_test_id(category_selector)
            
            # 验证元素是否可见
            if not category_element.is_visible():
                log.error(f"未找到{category}分类元素或元素不可见")
                return False
            
            # 点击分类元素
            log.info(f"找到{category}分类元素，正在点击")
            category_element.click()
            
            # 等待页面加载
            self.page.wait_for_load_state("networkidle", timeout=30000)
            self.page.wait_for_timeout(3000)
            
            # 验证是否成功进入分类页面
            current_url = self.page.url
            if category.lower() in current_url.lower():
                log.info(f"成功进入{category}分类页面: {current_url}")
                return True
            else:
                log.error(f"未成功进入{category}分类页面，当前URL: {current_url}")
                return False
            
        except Exception as e:
            log.error(f"进入{category}分类页面失败: {str(e)}")
            return False



    def go_to_special_category_from_hone(self, category_selector):
        """
        从首页进入特定分类页面，增强错误处理和备选方案
        """
        try:
            # 确保在首页
            if not self.page.url.endswith(TEST_URL.split("//")[1]):
                log.info(f"当前不在首页，正在跳转到首页...")
                self.page.goto(TEST_URL)
                self.page.wait_for_timeout(3000)
            
            # 关闭可能的弹窗
            # close_popup_on_home(self.page)
            # close_advertise_on_home(self.page)
            
            # 尝试多种方式查找Deals元素
            deals_found = False
            
            # 方法1: 使用提供的选择器
            try:
                deals_element = self.page.wait_for_selector(category_selector, timeout=5000)
                if deals_element and deals_element.is_visible():
                    log.info("找到Deals元素，使用提供的选择器")
                    deals_element.click()
                    deals_found = True
            except Exception as e:
                log.warning(f"使用提供的选择器未找到Deals元素: {str(e)}")
            
            # 方法2: 使用备选选择器
            if not deals_found:
                backup_selectors = [
                    "//a[contains(@href,'/category/sale')]",
                    "//a[contains(text(),'Deals')]",
                    "//a[contains(text(),'优惠')]",
                    "//a[@data-testid='deals-link']",
                    "//div[contains(@class,'HeaderWithCategory')]//a[contains(text(),'Deals')]"
                ]
                
                for selector in backup_selectors:
                    try:
                        deals_element = self.page.wait_for_selector(selector, timeout=3000)
                        if deals_element and deals_element.is_visible():
                            log.info(f"找到Deals元素，使用备选选择器: {selector}")
                            deals_element.click()
                            deals_found = True
                            break
                    except Exception as e:
                        log.debug(f"使用备选选择器 {selector} 未找到Deals元素: {str(e)}")
            
            # 方法3: 直接导航到Deals页面URL
            if not deals_found:
                log.info("未找到Deals元素，直接导航到Deals页面URL")
                self.page.goto(f"{TEST_URL}/category/sale?filter_sub_category=sale")
                deals_found = True
            
            # 等待页面加载
            self.page.wait_for_load_state("networkidle", timeout=30000)
            self.page.wait_for_timeout(3000)
            
            # 验证是否成功进入Deals页面
            current_url = self.page.url
            if "category/sale" in current_url or "deals" in current_url.lower():
                log.info(f"成功进入Deals页面: {current_url}")
                return True
            else:
                log.warning(f"可能未成功进入Deals页面，当前URL: {current_url}")
                return False
            
        except Exception as e:
            log.error(f"进入Deals分类页面失败: {str(e)}")
            # 尝试直接导航到Deals页面作为最后的备选方案
            try:
                self.page.goto(f"{TEST_URL}/category/sale?filter_sub_category=sale")
                self.page.wait_for_load_state("networkidle", timeout=30000)
                log.info("通过直接URL导航进入Deals页面")
                return True
            except Exception as e2:
                log.error(f"直接导航到Deals页面也失败: {str(e2)}")
                return False
    def select_specify_filter_on_category_page(self, filter_test_id):
        """
        该方法包含以下功能：
        1. 分类页点击勾选filter_delivery_type对应的filter来筛选不同的搜索结果
        """
        assert self.page.get_by_test_id("wid-filters-container").is_visible()
        # 滚动到 filter 模块
        self.scroll_to_pos("wid-filters-container")
        self.page.wait_for_timeout(2000)
        # 点击filter
        self.page.get_by_test_id(filter_test_id).click()
        self.page.wait_for_timeout(2000)

    def category_filter_delivery_type_check(self, filter_test_id):
        """
        该方法包含以下功能：
        1. 判断分类页filter是否选中
        2. 如果未选中，则点击filter确保选中
        3. 每次点击后重新滚动到filter位置
        """
        try:
            # 滚动到filter位置
            self.scroll_to_pos("wid-filters-container")
            self.page.wait_for_timeout(1000)

            # 获取filter元素
            filter_element = self.page.get_by_test_id(filter_test_id)

            # 检查选中状态
            data_state = filter_element.get_attribute("data-state")
            log.info(f"Filter {filter_test_id} 当前状态: {data_state}")

            # 如果未选中，则点击选中
            if data_state != "checked":
                log.info(f"Filter {filter_test_id} 未选中，正在点击选中")
                filter_element.click()
                self.page.wait_for_timeout(2000)

                # 页面刷新后重新滚动到filter位置
                self.scroll_to_pos("wid-filters-container")
                self.page.wait_for_timeout(1000)

                # 验证是否选中成功
                updated_state = self.page.get_by_test_id(filter_test_id).get_attribute("data-state")
                if updated_state == "checked":
                    log.info(f"Filter {filter_test_id} 选中成功")
                    return True
                else:
                    log.error(f"Filter {filter_test_id} 选中失败，当前状态: {updated_state}")
                    return False
            else:
                assert data_state == "checked",f"{data_state}"
                log.info(f"Filter {filter_test_id} 已经选中")
                return True

        except Exception as e:
            log.error(f"检查并选中filter {filter_test_id} 失败: {str(e)}")
            return False

    def category_filter_delivery_type_uncheck(self, filter_test_id):
        """
        该方法包含以下功能：
        1. 判断分类页filter是否选中
        2. 如果已选中，则点击filter取消选中
        3. 每次点击后重新滚动到filter位置
        """
        try:
            # 滚动到filter位置
            scroll_one_page_until(self.page, "wid-filters-container")
            self.page.wait_for_timeout(1000)

            # 获取filter元素
            filter_element = self.page.get_by_test_id(filter_test_id)

            # 检查选中状态
            data_state = filter_element.get_attribute("data-state")
            log.info(f"Filter {filter_test_id} 当前状态: {data_state}")

            # 如果已选中，则点击取消选中
            if data_state == "checked":
                log.info(f"Filter {filter_test_id} 已选中，正在点击取消选中")
                filter_element.click()
                self.page.wait_for_timeout(2000)

                # 页面刷新后重新滚动到filter位置
                scroll_one_page_until(self.page, "wid-filters-container")
                self.page.wait_for_timeout(1000)

                # 验证是否取消选中成功
                updated_state = self.page.get_by_test_id(filter_test_id).get_attribute("data-state")
                if updated_state != "checked":
                    log.info(f"Filter {filter_test_id} 取消选中成功")
                    return True
                else:
                    log.error(f"Filter {filter_test_id} 取消选中失败，当前状态: {updated_state}")
                    return False
            else:
                log.info(f"Filter {filter_test_id} 未选中，无需操作")
                return True

        except Exception as e:
            log.error(f"取消选中filter {filter_test_id} 失败: {str(e)}")
            return False

    def mweb_go_to_category_with_filter(self, category_test_id, filter_test_id):
        """
        移动端公共方法：进入指定分类并应用指定filter
        操作步骤：
        1. 点击weee logo进入首页
        2. 点击指定分类
        3. 点击分类filter按钮
        4. 在弹出的filter pop中勾选指定filter
        5. 点击应用按钮
        
        Args:
            category_test_id: 分类的test-id，如"wid-nav-tab-deals"
            filter_test_id: filter的test-id，如"btn-filter-delivery_type-delivery_type_local"
        """
        try:
            # 1. 点击weee logo进入首页
            log.info("点击weee logo进入首页")
            self.page.get_by_test_id("wid-weee-logo").click()
            self.page.wait_for_timeout(2000)
            
            # 2. 点击指定分类
            log.info(f"点击分类: {category_test_id}")
            self.page.get_by_test_id(category_test_id).click()
            self.page.wait_for_timeout(2000)
            
            # 3. 点击分类filter按钮
            log.info("点击分类filter按钮")
            self.page.get_by_test_id("btn-sub-category-filter").click()
            self.page.wait_for_timeout(1000)
            
            # 4. 验证filter pop弹出
            log.info("验证filter pop弹出")
            assert self.page.get_by_test_id("wid-sort-filter-popup").is_visible(), "Filter弹窗未显示"
            
            # 5. 勾选指定filter
            log.info(f"勾选filter: {filter_test_id}")
            self.page.get_by_test_id(filter_test_id).click()
            self.page.wait_for_timeout(500)
            
            # 6. 点击应用按钮
            log.info("点击应用按钮")
            self.page.get_by_test_id("btn-sort-filter-apply").click()
            self.page.wait_for_timeout(2000)
            
            log.info("移动端进入分类并应用filter操作完成")
            return True
            
        except Exception as e:
            log.error(f"移动端进入分类并应用filter操作失败: {str(e)}")
            return False
            if data_state != "unchecked":
                log.info(f"Filter {filter_test_id} 已选中，正在点击取消选中")
                filter_element.click()
                self.page.wait_for_timeout(2000)

                # 页面刷新后重新滚动到filter位置
                scroll_one_page_until(self.page, "wid-filters-container")
                self.page.wait_for_timeout(1000)

                # 验证是否取消选中成功
                updated_state = self.page.get_by_test_id(filter_test_id).get_attribute("data-state")
                if updated_state == "unchecked":
                    log.info(f"Filter {filter_test_id} 取消选中成功")
                    return True
                else:
                    log.error(f"Filter {filter_test_id} 取消选中失败，当前状态: {updated_state}")
                    return False
            else:
                assert data_state == "unchecked",f"{data_state}"
                log.info(f"Filter {filter_test_id} 已经取消选中")
                return True

        except Exception as e:
            log.error(f"检查并取消选中filter {filter_test_id} 失败: {str(e)}")
            return False

    def category_filter_product_type_check(self, filter_product_type):
        """
        该方法包含以下功能：
        1. 分类页点击勾选filter:Product type, 根据产品类型来筛选产品，获得不同的搜索结果
        """
        filter_product_type.check()
        self.page.wait_for_timeout(2000)

    def category_filter_product_type_uncheck(self, filter_product_type):
        """
        该方法包含以下功能：
        1. 分类页点击取消勾选filter:Product type, 根据产品类型来筛选产品，获得不同的搜索结果
        """
        filter_product_type.uncheck()
        self.page.wait_for_timeout(2000)

    def special_category_filter_sub_category(self, sub_category):
        """
        该方法包含以下功能：
        1. 根据传入的特殊分类页点击切换子分类
        """
        self.FE.ele(sub_category).click()
        self.page.wait_for_timeout(2000)

    def add_product_to_cart(self, product_id):
        """
        该方法包含以下功能：
        1. 根据传入的product_id, 加购指定商品
        """
        product_id.click()
        self.page.wait_for_timeout(2000)

    def go_to_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 点击购物车按钮，进入购物车页面
        """
        self.page.get_by_test_id("wid-mini-cart").click()
        self.page.wait_for_load_state("networkidle", timeout=60000)

    def add_to_local_product_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 分类页delivery_type为local类型的商品
        """
        # 进入deals分类页
        self.go_to_special_category_from_hone(dweb_home_ele.ele_deals)
        # 滚动到指定位置
        scroll_one_page_until(self.page, ele_filter_reset)
        # 点击Delivery type = Local Delivery
        self.category_filter_delivery_type_check(ele_local_delivery_test_id)

        self.page.wait_for_timeout(2000)
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break

        # 滚动到指定位置
        scroll_one_page_until(self.page, ele_filter_reset)
        # 再调一次，点击取消Delivery type = Local Delivery
        self.category_filter_delivery_type_check(ele_local_delivery_test_id)

    def add_filter_product_to_cart_from_category(self, category,category_selector=None,
                                                 filter_type=None, add_count=2):
        """
        该方法包含以下功能：
        1. 进入指定分类页
        2. 选择指定的过滤器（如 global 过滤器）
        3. 加购指定数量的商品
        
        参数：
        - category_selector: 分类页选择器，默认为deals分类
        - filter_type: 过滤器类型，默认为global
        - add_count: 加购商品数量，默认为2件
        """
        # 使用提供的分类选择器或默认使用deals分类
        category_selector = category_selector
        # 进入指定分类页
        self.go_to_specify_category_from_home(category,category_selector)
        # 等待筛选结果加载完成
        self.page.wait_for_timeout(2000)
        # 点击过滤指定filter
        self.select_specify_filter_on_category_page(filter_type)
        # 确保 filter 选中
        self.category_filter_delivery_type_check(filter_type)
        # 刷新页面
        self.page.reload()

        # 等待商品加载
        self.page.wait_for_timeout(2000)
        add_buttons = self.page.get_by_test_id("btn-atc-plus").all()
        log.info(f"找到{len(add_buttons)}个加购按钮")
        added_count = 0
        for index, add_btn in enumerate(add_buttons):
            try:
                if add_btn.is_visible() and add_btn.is_enabled():
                    add_btn.click()
                    self.page.wait_for_timeout(1500)  # 等待加购完成
                    added_count += 1
                    log.info(f"成功加购第 {added_count} 个商品")

                    # 达到指定数量后停止
                    if added_count >= add_count:
                        break
            except Exception as e:
                log.warning(f"第 {index + 1} 个加购按钮点击失败: {str(e)}")
                continue


        # # 使用公共元素获取所有商品卡片
        # product_main_container = self.page.get_by_test_id("mod-product-main-container")
        # product_cards = product_main_container.get_by_test_id(dweb_common_ele.ele_product_card_container).all()
        # if not product_cards:
        #     log.warning("没有找到符合条件的商品")
        #     return
        # # 加购商品
        # added_count = 0
        # # 遍历商品并加购指定数量
        # for card in product_cards:
        #     try:
        #         # 使用公共元素找到加购按钮并点击
        #
        #         # add_button = card.get_by_test_id(dweb_common_ele.ele_add_to_cart)
        #         add_button = card.locator(u"div[@data-testid='btn-atc-plus']")
        #         if add_button and add_button.is_visible():
        #             add_button.hover()
        #             self.page.wait_for_timeout(2000)
        #             add_button.click()
        #             self.page.wait_for_timeout(1000)  # 等待加购操作完成
        #             added_count += 1
        #             log.info(f"已加购第 {added_count} 件商品")
        #
        #             # 如果已经加购了指定数量的商品，停止加购
        #             if added_count >= add_count:
        #                 break
        #         else:
        #             log.warning("未找到加购按钮或按钮不可见")
        #
        #     except Exception as e:
        #         log.warning(f"加购商品时出错: {str(e)}")
        #         continue
        
        log.info(f"共加购 {added_count} 件商品")
        
        # 如果需要，可以取消过滤器选择
        # scroll_one_page_until(self.page, ele_filter_reset)
        # self.category_filter_delivery_type(delivery_type_filter)

    def add_to_many_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 分类页加购Deals类型的商品
        增强错误处理和备选方案
        """
        # 进入deals分类页
        deals_page_success = self.go_to_special_category_from_hone(dweb_home_ele.ele_deals)
        if not deals_page_success:
            log.warning("进入Deals页面可能不成功，尝试直接搜索商品")
            # 备选方案：使用搜索添加商品
            self.page.goto(TEST_URL)

            # 关闭可能的弹窗
            # close_popup_on_home(self.page)
            close_advertise_on_home(self.page)
            
            # 使用搜索
            try:
                search_input = self.page.get_by_placeholder("Search")
                if search_input.is_visible(timeout=5000):
                    search_input.click()
                    self.page.wait_for_timeout(2000)
                    self.page.keyboard.type("popular")
                    self.page.keyboard.press('Enter')
                    self.page.wait_for_timeout(5000)
                    log.info("使用搜索功能查找商品")
                else:
                    log.warning("未找到搜索框，尝试其他方式")
                    # 尝试点击任意分类
                    self.page.locator("//a[contains(@href,'/category/')]").first.click()
                    self.page.wait_for_timeout(5000)
            except Exception as e:
                log.error(f"搜索商品失败: {str(e)}")
        
        # 尝试滚动页面以确保商品加载
        try:
            self.page.evaluate("window.scrollBy(0, 300)")
            self.page.wait_for_timeout(1000)
            self.page.evaluate("window.scrollBy(0, 300)")
            self.page.wait_for_timeout(1000)
        except Exception as e:
            log.warning(f"滚动页面失败: {str(e)}")
        
        # 尝试使用多种选择器定位加购按钮
        selectors = [
            "//div[@data-testid='btn-atc-plus']",
            "//button[contains(@class, 'add-to-cart')]",
            "//div[contains(@class, 'add-to-cart')]",
            "//div[contains(@class, 'add-button')]",
            "//i[@data-role='addButtonPlusIcon']",
            "//i[contains(@class, 'icon-plus')]"
        ]
        
        product_buttons = []
        for selector in selectors:
            try:
                buttons = self.page.query_selector_all(selector)
                if buttons and len(buttons) > 0:
                    product_buttons = buttons
                    log.info(f"使用选择器 '{selector}' 找到{len(buttons)}个加购按钮")
                    break
            except Exception as e:
                log.warning(f"使用选择器 '{selector}' 定位加购按钮失败: {str(e)}")
        
        if not product_buttons:
            log.error("未找到任何加购按钮，无法添加商品")
            return False
        
        # 加购商品
        added_count = 0
        for index, item in enumerate(product_buttons):
            if index >= 3:  # 只添加3个商品
                break
            try:
                log.info(f"尝试加购第{index+1}个商品")
                # 确保元素可见
                item.scroll_into_view_if_needed()
                self.page.wait_for_timeout(2000)
                
                # 尝试点击
                try:
                    item.click()
                    self.page.wait_for_timeout(2000)
                    added_count += 1
                    log.info(f"成功加购第{index+1}个商品")
                except Exception as e:
                    log.warning(f"点击加购按钮失败，尝试使用JavaScript: {str(e)}")
                    # 尝试使用JavaScript点击
                    try:
                        self.page.evaluate("(element) => element.click()", item)
                        self.page.wait_for_timeout(2000)
                        added_count += 1
                        log.info(f"使用JavaScript成功加购第{index+1}个商品")
                    except Exception as js_e:
                        log.error(f"使用JavaScript加购也失败: {str(js_e)}")
            except Exception as e:
                log.error(f"加购第{index+1}个商品失败: {str(e)}")
        
        log.info(f"成功加购{added_count}个商品")
        return added_count > 0

    def add_products_from_home_by_filter(self, filter_name, filter_id, count=1):
        # 通过传入的筛选条件加购商品到购物车
        filter_element = self.page.get_by_test_id(filter_id) if isinstance(filter_id, str) else filter_id

        log.info(f"点击筛选条件: {filter_name}")
        filter_element.click()

        # 等待筛选结果加载完成
        self.page.wait_for_timeout(2000)

        # 等待加购按钮出现
        self.page.wait_for_timeout(2000)
        add_buttons = self.page.get_by_test_id("btn-atc-plus").all()
        log.info(f"找到{len(add_buttons)}个加购按钮")
        added_count = 0
        for index, add_btn in enumerate(add_buttons):
            try:
                if add_btn.is_visible() and add_btn.is_enabled():
                    add_btn.click()
                    self.page.wait_for_timeout(1500)  # 等待加购完成
                    added_count += 1
                    log.info(f"成功加购第 {added_count} 个商品")

                    # 达到指定数量后停止
                    if added_count >= count:
                        break
            except Exception as e:
                log.warning(f"第 {index + 1} 个加购按钮点击失败: {str(e)}")
                continue

        log.info(f"成功加购 {added_count} 个商品")
        return added_count