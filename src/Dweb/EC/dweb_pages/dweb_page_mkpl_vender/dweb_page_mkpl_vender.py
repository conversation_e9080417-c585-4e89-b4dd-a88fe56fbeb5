import re
from typing import List

from playwright.sync_api import Page, Element<PERSON>and<PERSON>
from src.common.commfunc import empty_cart
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage


class MkplVenderPage(DWebCommonPage):
    """
    Global+ vender page
    """

    def __init__(self, page: Page, header):
        super().__init__(page, header)
        self.page.goto(TEST_URL + '/mkpl/vendor/6887')
        self.page.wait_for_timeout(10000)
        self.page.wait_for_load_state("networkidle", timeout=60000)
        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        self.page.reload()
