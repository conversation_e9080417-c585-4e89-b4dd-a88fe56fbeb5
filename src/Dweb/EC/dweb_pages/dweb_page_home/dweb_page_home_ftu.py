# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2025/6/17
@Software       :  PyCharm
------------------------------------
"""
import random
import string

from playwright.sync_api import Page
from src.config.weee.log_help import log
class DWebTimeBannerPage:
    """Time Banner and FTU Popup handling """

    def __init__(self, page: Page, header, browser_context=None):
        self.page = page
        self.header = header
        self.browser_context = browser_context
    def p_verify_ftu_visibility(self):
        """验证 ftu popupr 是否可见"""
        log.info("Ftu Popup 可见")
        return self.page.get_by_test_id("wid-ftu-dialog-content").is_visible()
    def p_close_ftu(self):
        """关闭 Ftu Popup"""
        self.page.get_by_test_id("wid-modal-btn-close").click()
        self.page.wait_for_timeout(5000)  # 等待关闭动画完成
        assert not self.page.get_by_test_id("wid-ftu-dialog-content").is_visible()
        log.info("关闭 Ftu Popup 成功")
    def p_ftu_email_signup(self):
        "在ftu popup使用email注册"
        random_string = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
        random_email = f"{random_string}@autotest.com"
        self.page.get_by_test_id("wid-ftu-login-method-dialog-login-email").click()
        try:
            email_input = self.page.get_by_test_id("wid-ftu-login-method-dialog-login-email-input-input")
            email_input.wait_for(state="visible", timeout=5000)
            email_input.click()
            email_input.fill(random_email)
            self.page.get_by_test_id("wid-ftu-login-method-dialog-login-email-input-button").click()
            self.page.wait_for_timeout(10000)
            log.info(f"输入邮箱 {random_email} 注册")
            return True
        except Exception as e:
            log.error(f"邮箱注册失败: {str(e)}")
            return False

    def p_store_pop_visibility(self):
        ""
        try:
            store_pop = self.page.get_by_test_id("wid-ftu-select-shop-content")
            store_pop.wait_for(state="visible", timeout=10000)
            return True
        except Exception as e:
            log.error(f"Store Popup不可见: {str(e)}")
            return False
    def p_store_pop_click(self):
        try:
            self.p_store_pop_visibility()
            self.page.get_by_test_id("wid-modal-btn-close").click()
            self.page.wait_for_timeout(10000)
            self.page.get_by_test_id("btn-ftu-set-zipcode-input-see-availability").click()
            log.info("注册成功")
            return True
        except Exception as e:
            log.error(f"点击Store Popup失败: {str(e)}")
            return False

    def p_verify_time_banner_visibility(self):
        """验证 Time Banner 是否可见"""
        is_visible = self.page.get_by_test_id("wid-top-promotion-bar").is_visible()
        if is_visible:
            log.info("Time Banner 可见")
        else:
            log.info("Time Banner 不可见")
            raise AssertionError("Time Banner 不可见")
        return is_visible
    def p_click_time_banner(self):
        """点击 Time Banner"""
        self.page.get_by_test_id("wid-top-promotion-bar").click()
        self.page.wait_for_timeout(5000)
        log.info("点击 Time Banner 成功")

    def p_close_time_banner(self):
        """关闭 Time Banner"""
        self.page.get_by_test_id("wid-top-promotion-bar-close-cta-icon").click()
        self.page.wait_for_timeout(10000)  # 等待关闭动画完成
        assert not self.page.get_by_test_id("wid-top-promotion-bar").is_visible()
        log.info("关闭 Time Banner 成功")

    def p_verify_time_banner_closed(self):
        """验证 Time Banner 是否已关闭"""
        assert not self.page.get_by_test_id("wid-top-promotion-bar").is_visible()
        log.info("验证 Time Banner 已关闭成功")
    def p_click_email_signup(self):
        "点击邮箱注册"
        self.page.get_by_test_id("wid-ftu-login-method-dialog-login-email-text").click()
        self.page.wait_for_timeout(5000)
        log.info("点击邮箱输入框成功")
