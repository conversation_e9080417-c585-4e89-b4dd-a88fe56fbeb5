from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.Dweb.EC.dweb_ele.dweb_account.dweb_refer_friends import dweb_refer_friends_ele
from src.config.weee.log_help import log
from src.config.base_config import TEST_URL


class ReferFriendsEntryPage(DWebCommonPage):

	def __init__(self, page: Page, header, browser_context, page_url: str = "category/sale?filter_sub_category=sale"):
		"""
		邀请好友入口页面（页面顶部/mini account 区域）
		:param page: Playwright 页面对象
		:param header: 请求头
		:param browser_context: 浏览器上下文
		:param page_url: 进入的默认页面 URL 路径
		"""
		super().__init__(page, header)
		self.bc = browser_context
		# 进入指定页面（默认为产品详情页），以便在页面顶部执行入口交互
		self.page.goto(TEST_URL + "/" + page_url)
		self.page.wait_for_load_state("load")
		log.info(f"成功进入页面: {TEST_URL}/{page_url}")

	def hover_pc_header_mini_account_entry(self):
		"""
		悬浮 PC 端顶部 mini account 组件入口元素
		用途：触发 mini account 展开层
		"""
		self.page.locator(dweb_refer_friends_ele.ele_pc_top_mini_account_entry).hover(timeout=3000)
		self.page.wait_for_timeout(2000)
		log.info("已悬浮至 PC 顶部 mini account 入口元素")
		# 在悬浮后暂停，方便调试或观察页面状态
		# self.page.pause()

	def click_pc_header_invite_friends(self):
		"""
		点击 PC 端顶部的邀请好友文案元素
		"""
		self.FE.ele(dweb_refer_friends_ele.ele_pc_top_refer_friends_text).click()
		log.info("已点击 PC 顶部邀请好友文案元素")

	def click_mini_account_invite_friends(self):
		"""
		点击 mini account 的邀请好友文案元素
		前置建议：已调用 hover_pc_header_mini_account_entry 展开 mini account
		"""
		self.FE.ele(dweb_refer_friends_ele.ele_pc_top_refer_friends_text).click()
		log.info("已点击 mini account 邀请好友文案元素")


