from playwright.sync_api import Page

from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class DWebOrderListPage(DWebCommonPage):
    """
    这个类主要封装了PC web端对订单列表页面的操作
    """
    def __init__(self, page: Page, header, browser_context, page_url):
        """
        构造方法
        """
        super().__init__(page, header)
        self.bc = browser_context
        self.page.goto(TEST_URL)
        self.page.wait_for_timeout(10000)
        close_advertise_on_home(self.page)