from playwright.sync_api import Page, TimeoutError
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.Dweb.EC.dweb_ele.dweb_rewards import dweb_rewards_ele
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log
from src.config.base_config import TEST_URL


class RewardsTotalSavingPage(DWebCommonPage):
    """Total Saving 页面操作类"""
    
    def __init__(self, page: Page, header, browser_context, page_url: str = "account/my_rewards"):
        """
        初始化 Total Saving 页面
        :param page: Playwright页面对象
        :param header: 请求头
        :param browser_context: 浏览器上下文
        :param page_url: rewards页面URL路径
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入rewards页面
        self.page.goto(TEST_URL + "/" + page_url)
        # 等待页面加载完成
        self.page.wait_for_load_state("load")
        log.info(f"成功进入rewards页面: {TEST_URL}/{page_url}")

    def click_total_saving_banner(self):
        """
        点击 Total saving 绿色横幅
        """
        self.FE.ele(dweb_rewards_ele.ele_rewards_total_saving_banner).click()
        log.info("成功点击 Total saving 绿色横幅")

    def verify_delivery_service_fee_savings_text(self):
        """
        验证 Delivery and service fee savings 文案是否存在
        """
        try:
            savings_text = self.FE.ele(dweb_rewards_ele.ele_rewards_delivery_service_fee_savings)
            assert savings_text.is_visible(), "Delivery and service fee savings 文案未显示"
            log.info("成功验证 Delivery and service fee savings 文案")
            return True
        except Exception as e:
            log.error(f"验证 Delivery and service fee savings 文案失败: {str(e)}")
            return False
