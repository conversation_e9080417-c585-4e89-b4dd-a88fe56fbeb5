# dweb_page_store.py

# ... 其他导入 ...
from playwright.sync_api import Page, Locator
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.config.weee.log_help import log
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL


class DWebStorePage(DWebCommonPage):

    def __init__(self, page: Page, header):
        super().__init__(page, header)
        self.base_url = TEST_URL

        # 定义所有商店的名称和对应的 URL 参数
        self.stores_map = {
            "Chinese": "cn",
            "Japanese": "ja",
            "Korean": "ko",
            "Vietnamese": "vn",
            "Filipino": "ph",
            "Thai": "th",
            "Indian": "in"
        }

    def get_current_store_name(self) -> str | None:
        """
        获取当前选中的商店名称。
        """
        try:
            selected_store_locators = self.page.locator(
                "//div[contains(@data-testid, 'wid-store-list-') and not(contains(@data-testid, 'item'))]").all()
            for locator in selected_store_locators:
                data_testid = locator.get_attribute("data-testid")
                # Assuming the format is 'wid-store-list-StoreName'
                if data_testid:
                    store_name = data_testid.split('-')[-1]
                    if store_name in self.stores_map:
                        log.info(f"当前已选中的商店是: {store_name}")
                        return store_name
        except Exception as e:
            log.error(f"获取当前商店失败: {e}")
        return None


    def get_current_selected_store_locator(self) -> Locator | None:
        """
        获取当前已选中的商店的Playwright Locator。
        """
        try:
            # 找到所有已选中的商店元素
            # 使用 contains 模糊匹配，更健壮
            return self.page.locator(
                "//div[contains(@data-testid, 'wid-store-list-') and not(contains(@data-testid, 'item'))]")
        except Exception as e:
            log.error(f"获取当前选中商店元素失败: {e}")
            return None

    def get_unselected_store_names(self) -> list:
        """
        获取所有未选中的商店名称列表。
        """
        unselected_stores = []
        try:
            # 找到所有未选中的商店选项
            unselected_store_locators = self.page.locator(
                "//div[contains(@data-testid, 'wid-store-list-item-') and contains(@data-testid, '-unselected')]").all()
            for locator in unselected_store_locators:
                # 提取商店名称
                data_testid = locator.get_attribute("data-testid")
                store_name = data_testid.split('-')[-2]
                unselected_stores.append(store_name)
        except Exception as e:
            log.error(f"获取未选中商店列表失败: {e}")
        return unselected_stores

    def get_url_param_for_store(self, store_name: str) -> str:
        """
        根据商店名称获取对应的 URL 参数。
        """
        # 使用 get 方法，如果 store_name 不在 stores_map 中，会返回空字符串
        param = self.stores_map.get(store_name, '')
        if param:
            return f"grocery-store={param}"
        return ""

    def is_store_selected(self, store_name: str) -> bool:
        """
        验证特定商店是否被选中。
        """
        try:
            # 构建定位器，查找已选中的商店元素
            locator_str = f"//div[@data-testid='wid-store-list-{store_name}']"
            # 使用 Playwright 的 wait_for 方法来确保元素可见，而不是立即返回
            # 这能让方法更健壮，应对页面加载延迟
            self.page.locator(locator_str).wait_for(state="visible", timeout=10000)
            return True
        except Exception as e:
            log.error(f"检查 '{store_name}' 商店选中状态失败: {e}")
            return False

    def switch_to_store(self, store_name: str):
        log.info(f"尝试切换到商店: {store_name}")
        try:
            # 1. 找到并点击当前已选中的商店，以展开选项列表
            current_store_locator = self.page.locator(
                "//div[contains(@data-testid, 'wid-store-list-') and not(contains(@data-testid, 'item'))]"
            )
            current_store_locator.click(timeout=10000)  # 增加点击超时，等待元素出现
            self.page.wait_for_timeout(2000)  # 等待下拉列表展开

            # 2. 找到未选中的目标商店选项并点击
            target_store_locator = self.page.locator(
                f"//div[@data-testid='wid-store-list-item-{store_name}-unselected']"
            )
            target_store_locator.click(timeout=30000)  # 增加点击超时

            # 3. 等待页面重定向和加载
            self.page.wait_for_load_state("networkidle")
            close_advertise_on_home(self.page)

            log.info(f"成功切换到商店: {store_name}")

        except Exception as e:
            log.error(f"切换到商店 {store_name} 失败: {e}")
            raise

