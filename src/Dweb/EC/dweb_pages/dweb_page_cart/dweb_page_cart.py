from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_home import dweb_home_ele
from src.Dweb.EC.dweb_ele.dweb_cart import dweb_cart_ele
from src.api.zipcode import switch_zipcode
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage


class DWebCartPage(DWebCommonPage):
    """
    这个类主要封装了PC web端对购物车页面的封装
    """
    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011", page_url: str = None):
        """
        构造方法主要包含以下功能：
        1. 进入购物车页面
        2.如果zipcode不是98011，则切换zipcode
        3.调用close_advertise_on_home(self.page)关掉首页的广告
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入购物车
        self.page.goto(TEST_URL + page_url)
        # 获取顶部语言
        if not self.page.get_by_test_id("wid-language").locator(
                "//span[text()='English'and contains(@class,'Header')]").all():
            # 切换为英文
            pass
        # 获取顶部zipocde
        page_zipcode = self.page.locator(dweb_home_ele.ele_zipcode).text_content()
        if page_zipcode != zipcode:
            switch_zipcode(headers=self.header)
            self.page.reload()
        self.page.wait_for_timeout(10000)
        close_advertise_on_home(self.page)

    def start_shopping(self):
        """
        方法包含以下功能：
        1. 点击空购物车的start_shopping按钮，进入首页可以开始购物
        """
        # 点击空购物车的start_shopping按钮
        self.FE.ele(dweb_cart_ele.ele_cart_start_shopping).click()

    # def check_multi_cart_style_ui(self):
    #     """
    #     # 校验购物车样式-{【109546】 PC购物车-多个购物车样式}
    #     :return:
    #     """
    #     self.page.wait_for_selector(cart_elements.ele_cart_summary)
    #     assert self.FE.ele(cart_elements.ele_cart_summary).is_visible()
    #     assert "title" in self.FE.ele(cart_elements.ele_cart_summary).get_attribute("class")
    #     # 0. 判断第一个购物车是local delivery
    #     assert "Local delivery" == self.FE.ele(cart_elements.ele_cart_summary_local_delivery).text_content()
    #     # 1. 判断subtotal元素存在
    #     assert self.FE.ele(cart_elements.ele_cart_subtatal).is_visible()
    #     sub_total_fee = self.FE.ele(cart_elements.ele_cart_subtatal_fee)
    #     # 2. 判断subtotal值
    #     assert sub_total_fee.is_visible() and "$" in sub_total_fee.text_content()
    #
    #     # 获取所有的items total
    #     items_total = self.FE.eles(cart_elements.ele_cart_items_total)
    #     assert items_total, f"items_total={items_total}"
    #     # 3. 判断items_total中有美元符号存在
    #     for item in items_total:
    #         log.debug("item.text_content===>" + item.text_content())
    #         assert "$" in item.text_content()
    #
    #     # 4. 判断delivery_fee中有美元符号存在或为free
    #     delivery_fee = self.FE.eles(cart_elements.ele_cart_delivery_fee)
    #     for df in delivery_fee:
    #         log.debug("delivery_fee的content===>" + df.text_content())
    #         assert "$" in df.text_content() or 'Free' == df.text_content()
    #
    #     # 5. 判断左侧的购物车
    #     all_cart_div = self.FE.eles(cart_elements.ele_cart_each_cart_div)
    #     assert all_cart_div, f"all_cart_div={all_cart_div}"
    #     for acd in all_cart_div:
    #         all_goods: List[ElementHandle] = acd.query_selector_all("//div[contains(@class, 'GoodsInCart_goods__')]")
    #         assert all_goods, f"all_goods={all_goods}"
    #         for index, ag in enumerate(all_goods):
    #             goods_in_cart_price_action = ag.query_selector(
    #                 u"//div[contains(@class, 'GoodsInCart_goodsActions')]//div[contains(@class, 'GoodsInCart_priceAction')]")
    #             # 校验购物车里"每个商品"的div
    #             assert goods_in_cart_price_action.is_visible()
    #             goods_in_cart_price = ag.query_selector(
    #                 u"//div[contains(@class, 'GoodsInCart_goodsActions')]//div[contains(@class, 'leading-none font-semibold text-center')]")
    #             log.info("each product price in cart===>" + goods_in_cart_price.text_content())
    #             # 校验商品的价格以$开头
    #             assert "$" in goods_in_cart_price.text_content() or "Free" in goods_in_cart_price.text_content()
    #             # 第一个商品有可能是gift商品，没有remove和save_for_later
    #             if index >= 2:
    #                 # 校验remove按钮
    #                 remove = ag.query_selector(u"//div[text()='Remove']")
    #                 # 校验save_for_later
    #                 save_for_later = ag.query_selector(u"//div[text()='Save for later']")
    #                 assert remove.is_visible() and save_for_later.is_visible()
    #             product_name = ag.query_selector(u"//div[contains(@class, 'GoodsInCart_name')]//span").text_content()
    #             log.info("product_name is: " + product_name)
    #             assert len(product_name) > 2
    #
    #     # 7. check底部的recommendations
    #     # 先滚动到Recommendations
    #     while True:
    #         self.page.evaluate('window.scrollBy(0, window.innerHeight)')
    #         self.page.wait_for_timeout(2000)
    #         if self.FE.ele(u"//span[text()='Recommendations']"):
    #             self.FE.ele(u"//span[text()='Recommendations']").scroll_into_view_if_needed()
    #             break
    #
    #     # 7.1 校验标题
    #     assert self.FE.ele(cart_elements.ele_cart_recommendations).text_content() == 'Recommendations'
    #     recommendations_all_goods = self.FE.eles(cart_elements.ele_cart_recommendations_all_goods)
    #     assert recommendations_all_goods, f"购物车推荐商品为0"
    #     # 7.2 校验recommendations下面的商品
    #     for index, i in enumerate(recommendations_all_goods):
    #         # 后面隐藏的商品，继续找加购按钮，找不到，可能因为不可见，需要划动
    #         if index <= 2:
    #             r_add_to_cart_btn = i.query_selector(u"//i[@data-role]")
    #             assert r_add_to_cart_btn.is_enabled()
    #             r_add_to_cart_btn.click()
    #
    #
