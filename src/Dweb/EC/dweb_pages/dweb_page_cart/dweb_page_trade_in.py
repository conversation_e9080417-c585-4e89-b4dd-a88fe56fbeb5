from playwright.sync_api import Page

from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL

from src.Dweb.EC.dweb_ele.dweb_cart import dweb_cart_ele,dweb_trade_in_ele
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.config.weee.log_help import log


class DWebTradeInPage(DWebCommonPage):
    """
    这个类主要封装了PC web端对购物车换购页面的操作
    """
    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011",page_url=None):
        """
        构造方法
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入指定页面
        if page_url:
            self.page.goto(TEST_URL + page_url)
        else:
            self.page.goto(TEST_URL)

        self.page.wait_for_timeout(10000)
        close_advertise_on_home(self.page)

    def get_normal_cart_trade_in_banner_text(self):
        """
        获取换购入口banner文案
        
        Returns:
            str: 换购入口banner文案，如果不存在则返回空字符串
        """
        banner = self.page.get_by_test_id(dweb_cart_ele.ele_cart_trade_in_normal_banner)
        if banner.is_visible():
            return banner.text_content()
        return ""

    def is_normal_cart_trade_in_banner_visible(self):
        """
        检查换购入口banner是否可见
        
        Returns:
            bool: 换购入口banner是否可见
        """
        banner = self.page.get_by_test_id(dweb_cart_ele.ele_cart_trade_in_normal_banner)
        return banner.is_visible()

    def click_normal_cart_trade_in_banner(self):
        """
        点击换购入口banner
        
        Returns:
            bool: 是否成功点击
        """
        banner = self.page.get_by_test_id(dweb_cart_ele.ele_cart_trade_in_normal_banner_icon)
        if not banner.is_visible():
            log.info("换购入口banner不可见")
            return False
        
        banner.click()
        self.page.wait_for_timeout(2000)
        log.info("点击换购入口banner")
        return True

    def is_trade_in_drawer_visible(self):
        """
        检查换购列表是否可见
        
        Returns:
            bool: 换购列表是否可见
        """
        drawer = self.page.get_by_test_id(dweb_trade_in_ele.ele_trade_in_title)
        return drawer.is_visible()

    def get_trade_in_products(self):
        """
        获取换购商品列表
        
        Returns:
            list: 换购商品列表
        """
        return self.page.get_by_test_id(dweb_trade_in_ele.ele_trade_in_products).all()

    def check_trade_in_products_status(self):
        """
        检查换购商品是否可加购
        
        Returns:
            bool: 换购商品是否可加购
        """
        products = self.page.get_by_test_id(dweb_trade_in_ele.ele_trade_in_products).all()

        if not products:
            log.info("换购列表为空")
            return False
        
        # 检查第一个换购商品的加购按钮是否禁用
        first_product_status = products[0].get_by_test_id(dweb_trade_in_ele.ele_trade_in_non_selectable)
        return not first_product_status

    def close_trade_in_drawer(self):
        """
        关闭换购列表
        
        Returns:
            bool: 是否成功关闭
        """
        close_btn = self.page.get_by_test_id(dweb_trade_in_ele.ele_trade_in_close)
        if not close_btn.is_visible():
            log.info("换购列表关闭按钮不可见")
            return False
        
        close_btn.click()
        self.page.wait_for_timeout(1000)
        log.info("关闭换购列表")
        return True

    def add_trade_in_product(self, index=0):
        """
        加购指定索引的换购商品
        
        Args:
            index (int): 换购商品索引
        
        Returns:
            bool: 是否成功加购
        """
        products = self.get_trade_in_products()
        if not products or index >= len(products):
            log.info(f"换购列表为空或索引{index}超出范围")
            return False
        
        add_btn = products[index].get_by_test_id(dweb_trade_in_ele.ele_trade_in_selectable)
        if add_btn.is_disabled():
            log.info(f"第{index+1}个换购商品不可加购")
            return False
        
        add_btn.click()
        self.page.wait_for_timeout(1000)
        log.info(f"加购第{index+1}个换购商品")
        return True

    def add_trade_in_products(self, max_count=5):
        """
        加购换购商品，最多max_count件
        
        Args:
            max_count (int): 最大加购数量
        
        Returns:
            int: 成功加购的数量
        """
        products = self.get_trade_in_products()
        added_count = 0
        
        for i in range(min(max_count, len(products))):
            if self.add_trade_in_product(i):
                added_count += 1
        
        log.info(f"成功加购{added_count}个换购商品")
        return added_count

    def get_trade_in_products_count(self):
        """
        获取购物车中的换购商品数量
        
        Returns:
            int: 换购商品数量
        """
        cart_items = self.page.get_by_test_id(dweb_trade_in_ele.ele_trade_in_products).all()
        trade_in_count = 0
        
        for item in cart_items:
            if "trade-in" in item.get_attribute("class") or item.locator("[data-testid='trade-in-badge']").count() > 0:
                trade_in_count += 1
        
        log.info(f"购物车中有{trade_in_count}个换购商品")
        return trade_in_count

    def remove_first_trade_in_product(self):
        """
        删除第一个换购商品
        
        Returns:
            bool: 是否成功删除
        """
        cart_items = self.page.get_by_test_id("wid-cart-section-normal-goods").all()
        
        for item in cart_items:
            item.get_by_test_id("wid-cart-section-normal-goods-remove-btn").click()
            self.page.wait_for_timeout(2000)
            log.info("删除一个换购商品")
            return True
        
        log.info("未找到换购商品")
        return False

    def remove_all_trade_in_products(self):
        """
        删除所有换购商品
        
        Returns:
            int: 删除的换购商品数量
        """
        removed_count = 0
        while self.remove_first_trade_in_product():
            removed_count += 1
        
        log.info(f"删除了{removed_count}个换购商品")
        return removed_count

    def open_trade_in_page(self):
        """
        打开换购抽屉
        
        Returns:
            bool: 是否成功打开换购抽屉
        """
        # 点击换购banner
        if not self.click_normal_cart_trade_in_banner():
            log.info("点击换购banner失败")
            return False
        
        # 验证换购抽屉弹出
        if not self.is_trade_in_drawer_visible():
            log.info("换购抽屉未弹出")
            return False

        log.info("换购抽屉弹出成功")
        return True

    def get_trade_in_drawer_status(self):
        """
        获取换购页面商品信息

        Returns:
            dict: 包含换购商品信息的字典
        """
        result = {
            "products": [],
            "can_add_to_cart": False
        }

        # 获取换购商品列表
        result["products"] = self.get_trade_in_products()
        log.info(f"找到{len(result['products'])}个换购商品")
        
        # 检查换购商品是否可加购
        result["can_add_to_cart"] = self.check_trade_in_products_status()
        log.info(f"换购商品{'可' if result['can_add_to_cart'] else '不可'}加购")
        
        return result

    def open_trade_in_drawer_and_check(self):
        """
        打开换购抽屉并检查状态

        Returns:
            dict: 包含换购抽屉状态信息的字典
        """
        result = {
            "visible": False,
            "products": [],
            "can_add_to_cart": False
        }

        # 打开换购抽屉
        if not self.open_trade_in_page():
            return result

        result["visible"] = True

        # 获取换购商品信息
        drawer_status = self.get_trade_in_drawer_status()
        result["products"] = drawer_status["products"]
        result["can_add_to_cart"] = drawer_status["can_add_to_cart"]

        return result

    def trade_in_product(self,trade_in_product):
        # 换购商品基础信息验证
        assert trade_in_product.get_by_test_id(dweb_trade_in_ele.ele_trade_in_title).is_visible()
        assert trade_in_product.get_by_test_id(dweb_trade_in_ele.ele_trade_in_card_image).is_visible()
        assert trade_in_product.get_by_test_id(dweb_trade_in_ele.ele_trade_in_price).is_visible()
        assert trade_in_product.get_by_test_id(dweb_trade_in_ele.ele_trade_in_base_price).is_visible()




