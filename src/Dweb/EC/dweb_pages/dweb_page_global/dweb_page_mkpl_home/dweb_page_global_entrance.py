from playwright.sync_api import Page
from src.config.weee.log_help import log
from src.Dweb.EC.dweb_ele.dweb_home.dweb_home_ele import ele_pc_home_global_entrance
from src.Dweb.EC.dweb_ele.dweb_mkpl_global.dweb_mkpl_global_ele import close_popup_on_home


class DWebGlobalEntrancePage:

    def __init__(self, page: Page):
        self.page = page

    def navigate_and_click_global_entrance(self):
        """访问主站并点击Global+入口"""
        # 1. 访问主站
        self.page.goto("https://www.sayweee.com")
        log.info("访问主站完成")
        
        # 2. 等待8秒，检查并关闭弹窗
        self.page.wait_for_timeout(8000)
        
        close_btn = self.page.get_by_test_id(close_popup_on_home)
        if close_btn.count() > 0:
            close_btn.click()
            log.info("点击关闭弹窗按钮成功")
        else:
            log.info("关闭弹窗按钮不存在")
        
        # 3. 检查并点击Global+入口元素
        global_entrance = self.page.get_by_test_id(ele_pc_home_global_entrance)
        if global_entrance.count() > 0 and global_entrance.is_visible():
            global_entrance.click()
            log.info("点击Global+入口成功")
            return True
        else:
            log.info("Global+入口元素不存在或不可见")
            return False


