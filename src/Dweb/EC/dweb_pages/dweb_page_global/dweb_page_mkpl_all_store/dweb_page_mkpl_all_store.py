import re
from typing import List

from playwright.sync_api import Page, ElementHandle
from src.common.commfunc import empty_cart
from src.common.commonui import close_popup_on_home, close_advertise_on_home
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage


class MkplAllStorePage(DWebCommonPage):
    """
    Global+ allstore
    """

    def __init__(self, page: Page, header):
        super().__init__(page, header)
        self.page.goto(TEST_URL + '/mkpl/global?mode=sub_page&hide_activity_pop=1')
        self.page.wait_for_timeout(10000)
        self.page.wait_for_load_state("networkidle", timeout=60000)
        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        self.page.reload()
    def _check_stores_page_loaded(self):
        """
        校验All Stores页面加载完成
        """
        # 等待商家容器加载
        seller_containers = self.FE.eles("//div[@data-testid='wid-seller-container']")
        assert len(seller_containers) > 0, "All Stores页面没有商家容器"

        log.info(f"All Stores页面加载完成，找到{len(seller_containers)}个商家")

    def _click_product_card(self):
        """
        点击商品卡片进入PDP页面
        """
        # 获取第一个商品卡片
        product_images = self.FE.eles("//div[@data-testid='wid-product-card-image']")
        assert len(product_images) > 0, "没有找到商品卡片"

        # 点击第一个商品
        first_product = product_images[0]
        first_product.click()
        self.page.wait_for_timeout(3000)

        log.info("已点击商品卡片，进入PDP页面")