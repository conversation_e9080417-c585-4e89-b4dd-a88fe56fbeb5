"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   dweb_page_order_confirmation.py
@Description    :  
@CreateTime     :  2025/6/8 11:52
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/8 11:52
"""
from playwright.sync_api import Page, expect

from src.Dweb.EC.dweb_ele import dweb_order_confirmation
from src.Dweb.EC.dweb_ele.dweb_home import dweb_home_ele
from src.Dweb.EC.dweb_ele.dweb_order_confirmation import dweb_order_confirmation_ele
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.api.zipcode import switch_zipcode
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class DWebOrderComfirmationPage(DWebCommonPage):
    """订单确认页面类"""

    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011", page_url: str = None):
        """
        构造方法主要包含以下功能：
        1. 进入订单成功页面
        2.如果zipcode不是98011，则切换zipcode
        3.调用close_advertise_on_home(self.page)关掉首页的广告
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入订单成功页
        self.page.goto(TEST_URL + page_url)
        # 获取顶部语言
        if not self.page.get_by_test_id("wid-language").locator(
                "//span[text()='English'and contains(@class,'Header')]").all():
            # 切换为英文
            pass
        # 获取顶部zipocde
        page_zipcode = self.page.locator(dweb_home_ele.ele_zipcode).text_content()
        if page_zipcode != zipcode:
            switch_zipcode(headers=self.header)
            self.page.reload()
        self.page.wait_for_timeout(10000)
        close_advertise_on_home(self.page)

    def verify_order_confirmation_title(self):
        """验证订单确认页标题"""
        title = self.FE.ele(dweb_order_confirmation_ele.ele_order_confirmation_title)
        assert title.is_visible(), "订单确认页标题不可见"
        title_text = title.text_content()
        log.info(f"订单确认页标题: {title_text}")
        return title_text

    def start_earning(self):
        """
        点击开始赚取积分按钮，打开分享弹窗
        """
        # 点击开始赚取积分按钮
        start_button = self.FE.ele(dweb_order_confirmation_ele.start_earning)
        assert start_button.is_visible(), "开始赚取积分按钮不可见"
        start_button.click()
        self.page.wait_for_timeout(2000)
        
        # 验证弹窗已打开
        modal = self.page.locator("//div[contains(@class, 'ant-modal-content')]").first
        assert modal.is_visible(), "分享弹窗未打开"
        log.info("分享弹窗已打开")
        return modal

    def close_modal(self):
        self.page.wait_for_timeout(3000)
        self.page.locator(dweb_order_confirmation_ele.dialog).is_visible()
        """关闭弹窗并验证"""
        close_button = self.page.get_by_test_id("wid-modal-btn-close")
        # 检查按钮是否存在
        count = close_button.count()
        log.info(f"找到 {count} 个匹配 'wid-modal-btn-close' 的元素")
        # 点击关闭按钮
        close_button = self.page.locator(dweb_order_confirmation_ele.dialog_close).first
        print("0000000000000",close_button)
        assert close_button.is_visible(), "关闭按钮不可见"
        close_button.click()
        self.page.wait_for_timeout(2000)
        
        # 验证弹窗已关闭
        modal = self.page.locator(dweb_order_confirmation_ele.dialog).first
        assert not modal.is_visible(), "弹窗未关闭"
        log.info("弹窗已成功关闭")

    def verify_order_summary(self):
        """验证订单摘要信息"""
        # 验证订单摘要区域
        summary_section = self.FE.ele(dweb_order_confirmation_ele.ele_order_summary)
        assert summary_section.is_visible(), "订单摘要区域不可见"
        log.info("订单摘要区域可见")
        
        # 验证小计
        subtotal = self.FE.ele(dweb_order_confirmation_ele.ele_subtotal)
        assert subtotal.is_visible(), "小计信息不可见"
        subtotal_text = subtotal.text_content()
        log.info(f"订单小计: {subtotal_text}")
        
        # 验证配送费
        delivery_fee = self.FE.ele(dweb_order_confirmation_ele.ele_delivery_fee)
        assert delivery_fee.is_visible(), "配送费信息不可见"
        delivery_fee_text = delivery_fee.text_content()
        log.info(f"配送费: {delivery_fee_text}")
        
        # 验证税费
        tax = self.FE.ele(dweb_order_confirmation_ele.ele_tax)
        assert tax.is_visible(), "税费信息不可见"
        tax_text = tax.text_content()
        log.info(f"税费: {tax_text}")
        
        # 验证总计
        total = self.FE.ele(dweb_order_confirmation_ele.ele_total)
        assert total.is_visible(), "总计信息不可见"
        total_text = total.text_content()
        log.info(f"订单总计: {total_text}")
        
        return {
            "subtotal": subtotal_text,
            "delivery_fee": delivery_fee_text,
            "tax": tax_text,
            "total": total_text
        }

    def verify_action_buttons(self):
        """验证操作按钮"""
        # 验证查看订单详情按钮
        view_order_btn = self.FE.ele(dweb_order_confirmation_ele.ele_view_order_details)
        assert view_order_btn.is_visible(), "查看订单详情按钮不可见"
        log.info(f"查看订单详情按钮: {view_order_btn.text_content()}")
        
        # 验证继续购物按钮
        continue_shopping_btn = self.FE.ele(dweb_order_confirmation_ele.ele_continue_shopping)
        assert continue_shopping_btn.is_visible(), "继续购物按钮不可见"
        log.info(f"继续购物按钮: {continue_shopping_btn.text_content()}")
        
        # 验证追踪订单按钮
        track_order_btn = self.FE.ele(dweb_order_confirmation_ele.ele_track_order)
        assert track_order_btn.is_visible(), "追踪订单按钮不可见"
        log.info(f"追踪订单按钮: {track_order_btn.text_content()}")
        
        return {
            "view_order": view_order_btn,
            "continue_shopping": continue_shopping_btn,
            "track_order": track_order_btn
        }
