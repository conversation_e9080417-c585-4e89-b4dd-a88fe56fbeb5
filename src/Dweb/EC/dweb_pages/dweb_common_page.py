from src.common.commonui import FindElement, close_advertise_on_home
from playwright.sync_api import Page

from src.config.weee.log_help import log


class DWebCommonPage:
    def __init__(self, page:Page, header):
        self.page = page
        self.header = header
        self.FE = FindElement(self.page)

    def home_page_switch_zipcode(self, zipcode="98011"):
        """
        该构造方法包含以下功能：
        1. 根据传入的zipcode, 输入zipcode并点击confirm, 用于PC首页页面的切换zipcode
        """
        # current_zipcode = self.page.get_by_test_id("wid-modal-zip-code").locator(u"//div[matches(text(), '^\d{5}\s+\w+')]/text()")
        # print("current_zipcode:"+str(current_zipcode))
        # if str(current_zipcode) !=zipcode:
        self.page.get_by_test_id("wid-modal-zip-code").click()
        self.page.wait_for_timeout(2000)
        zipcode_input = self.page.get_by_test_id("wid-input-zipcode-input")
        zipcode_input.clear()  # 先清空zipcode输入框
        zipcode_input.fill(zipcode)  # 再输入新的zipcode
        # 点击确认按钮
        self.page.get_by_test_id("btn-zipcode-btn").click()
        self.page.wait_for_timeout(2000)
        # 关闭pop
        close_advertise_on_home(self.page)


    def home_page_switch_lang(self, lang="English"):
        """
        该构造方法包含以下功能：
        1. 根据传入的language, 切换语言 用于PC首页页面的切换language
        """
        current_lang = self.page.get_by_test_id("wid-language").locator(
                u"//span[contains(@class,'Header_label')]")
        if current_lang!=lang:
            # 切换为指定语言
            self.page.get_by_test_id("wid-language").click()


    def home_page_common_check(self):
        """
        首页需要校验的公共组件
        """
        pass


    def cart_page_common_check(self):
        pass


    def pdp_dweb_page_common_check(self):
        assert self.page.get_by_test_id("wid-pdp-thumbnail-0").is_visible()
        assert self.page.get_by_test_id("wid-pdp-zoom-img").is_visible()
        assert self.page.get_by_test_id("btn-favorite").all()[0].is_visible()
        assert self.page.get_by_test_id("pdp-detail-main").is_visible()

    def pdp_page_common_check(self):
        assert self.page.get_by_test_id("wid-mweb_page_pdp-thumbnail-0").is_visible()
        assert self.page.get_by_test_id("wid-mweb_page_pdp-zoom-img").is_visible()
        assert self.page.get_by_test_id("btn-favorite").all()[0].is_visible()
        assert self.page.get_by_test_id("mweb_page_pdp-detail-main").is_visible()

    def add_pdp_related_products_to_cart(self, ele_pdp_related_add_to_cart_button, n):
        for index, item in enumerate(ele_pdp_related_add_to_cart_button):
            try:
                item.evaluate('(item) => item.click()')
                # item.click()
            except Exception as e:
                log.info("加购按钮点击失败" + str(e))
            # self.page.reload()
            self.page.wait_for_timeout(1000)
            if index == n:
                break


    def category_page_common_check(self):
        pass


    def checkout_page_common_check(self):
        pass

    def scroll_to_pos(self, test_id: str):
        """
        滚动到指定元素位置
        Args:
            test_id: 元素的test-id
        """
        try:
            element = self.page.get_by_test_id(test_id)
            if element.is_visible():
                element.scroll_into_view_if_needed()
                self.page.wait_for_timeout(1000)
                log.info(f"成功滚动到元素: {test_id}")
            else:
                log.warning(f"元素不可见，无法滚动: {test_id}")
        except Exception as e:
            log.error(f"滚动到元素时发生异常: {test_id}, 错误: {str(e)}")




