"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   page_checkout.py
@Description    :  
@CreateTime     :  2025/3/10 14:17
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/3/10 14:17
"""
import allure
from playwright.sync_api import Page
from playwright.sync_api import expect

from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class CheckoutPage(DWebCommonPage):
    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011"):
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入结算页
        self.page.goto(TEST_URL + "/order/checkout?cart_domain=grocery")
        # # 获取顶部语言
        # if not self.page.get_by_test_id("wid-language").locator(
        #         "//span[text()='English'and contains(@class,'Header')]").all():
        #     # 切换为英文
        #     pass
        # # 获取顶部zipocde
        # page_zipcode = self.page.locator(home_elements.ele_zipcode).text_content()
        # if page_zipcode != zipcode:
        #     switch_zipcode(headers=self.header)
        #     self.page.reload()
        # self.page.wait_for_timeout(10000)
        # close_advertise_on_home(self.page)

    def check_checkout_free_shipping_banner_ui(self):
        # 定位不了这种会消失的元素
        with allure.step("验证凑单banner"):
            try:
                banner = self.page.get_by_test_id('wid-checkout-free-shipping-banner')
                expect(banner).to_be_visible()
                # 验证banner文案包含凑单金额信息
                banner_text = banner.text_content()
                assert "$35" in banner_text, "凑单banner金额不正确"
                log.info("凑单banner验证成功")
            except Exception as e:
                log.error(f"验证凑单banner失败: {str(e)}")
                raise

    @staticmethod
    def submit_order_with_points_on_checkout_page(self, page: Page):
        """在结算页面直接提交订单"""
        try:
            page.wait_for_timeout(3000)

            # 检查积分开关状态
            points_switch = page.get_by_test_id("wid-checkout-payment-panel-points-switch")
            try:
                points_switch.wait_for(state="visible", timeout=10000)
                current_state = page.get_by_test_id("wid-checkout-payment-panel-points-switch-inner").get_attribute("data-state")
            except:
                log.warning("积分开关加载超时，跳过积分支付")
                current_state = None

            log.info(f"积分开关状态: {current_state}")

            if current_state == "unchecked":
                points_switch.click()
                page.wait_for_timeout(2000)
                log.info("已开启积分支付")
            elif current_state == "checked":
                log.info("积分支付已开启")
            elif current_state is None:
                log.info("积分开关不可用，直接提交订单")
            else:
                log.warning(f"未知积分状态: {current_state}")

            # 点击结算按钮
            submit_btn = page.locator("(//button[@data-testid='wid-checkout-btn'])[2]")
            submit_btn.click()
            page.wait_for_timeout(10000)
            log.info("已点击结算按钮")

            # 获取订单号
            current_url = page.url
            log.info(f"当前URL: {current_url}")

            # 从 URL 末尾获取订单号
            order_id = current_url.split("/")[-1].split("?")[0]
            if order_id:
                log.info(f"成功创建订单: {order_id}")

                # 跳转到订单详情页
                page.goto(TEST_URL + f"/order/detail/{order_id}")
                page.wait_for_timeout(3000)

                return order_id
            else:
                log.error("未能获取订单号")

        except Exception as e:
            log.error(f"提交订单失败: {str(e)}")

        return None