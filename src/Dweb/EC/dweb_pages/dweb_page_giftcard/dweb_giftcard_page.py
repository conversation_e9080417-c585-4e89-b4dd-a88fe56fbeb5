from playwright.sync_api import Page, TimeoutError
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.Dweb.EC.dweb_ele.dweb_account.dweb_giftcard.dweb_giftcard_ele import *
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log
from src.config.base_config import TEST_URL


class DWebGiftCardPage(DWebCommonPage):
    """礼品卡页面操作类 - DWeb版本"""
    
    def __init__(self, page: Page, header, browser_context, page_url: str = "product/gift-card/2189607"):
        """
        初始化礼品卡页面
        :param page: Playwright页面对象
        :param header: 请求头
        :param browser_context: 浏览器上下文
        :param page_url: 礼品卡页面URL路径
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入礼品卡页面
        self.page.goto(TEST_URL + "/" + page_url)
        # 等待页面加载完成
        self.page.wait_for_load_state("load")
        log.info(f"成功进入礼品卡页面: {TEST_URL}/{page_url}")

    def close_gift_card_popup(self):
        """
        关闭礼品卡弹窗 - 点击 Got it 按钮
        """
        try:
            self.FE.ele(ele_giftcard_got_it_button).click()
            log.info("成功点击 Got it 按钮，关闭礼品卡弹窗")
        except TimeoutError:
            log.warning("未找到 Got it 按钮，可能弹窗已关闭或不存在")

   