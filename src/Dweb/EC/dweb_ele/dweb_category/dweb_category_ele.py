# PC首页上方的category元素
ele_sale_categ = "wid-direct-link-sale"
ele_trending_categ = "wid-direct-link-trending"
ele_new_categ = "wid-direct-link-new"
ele_global_categ = "wid-direct-link-global"


# 各category搜索结果总div
ele_category_result_div = "div[class*='DiscoveryProducts_listContent']"

# 各具体product div
ele_category_result_product_div = "div[class*='DiscoveryProducts_resultItem']"

# 分类页右侧分类
ele_filter_reset = 'btn-sort-filter-reset'

ele_local_delivery_test_id = "btn-delivery_type-delivery_type_local"
ele_pantry_delivery_test_id = "btn-delivery_type-delivery_type_pantry"
ele_global_delivery_test_id = "btn-delivery_type-delivery_type_global"
ele_mo_delivery_test_id = 'btn-delivery_type-delivery_type_fbw'

# 冻货分类
ele_filter_frozen = 'wid-categories-item-frozen'
# 酒分类
ele_filter_alcohol = 'wid-categories-item-alcohol'


# 右侧filter 模块
ele_filters_container = "wid-filters-container"
# 非只有本地是这个
ele_delivery_type_local = "btn-delivery_type-delivery_type_local"
# MOF 本地是这个
ele_delivery_type_local_mof = "btn-delivery_type-delivery_type_local_mof"
# MO 本地、mof Fulfilled by Weee
ele_delivery_type_fbw = "btn-delivery_type-delivery_type_fbw"
# pantry
ele_delivery_type_pantry = "btn-delivery_type-delivery_type_pantry"
# global
ele_delivery_type_global = "btn-delivery_type-delivery_type_global"

# 99348