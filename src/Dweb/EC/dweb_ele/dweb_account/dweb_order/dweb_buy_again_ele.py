buy_again_reorder_header_ele = u"//div[contains(@class,'buy-again_reorderHeader')]"
buy_again_date_info_ele = u"//div[contains(@class,'buy-again_dateInfo')]"
buy_again_select_all_ele = u"//div[contains(@class,'buy-again_selectAll')]"

# buy_again_select_all_ele = u"//div[contains(@class,'buy-again_selectAll') and contains(@class, 'buy-again_selected')]"
buy_again_unselect_all_ele = u"//div[contains(@class,'buy-again_selectAll') and contains(@class, 'buy-again_disabledSelect')]"
buy_again_list_ele = u"//div[contains(@class,'buy-again_list')]"
buy_again_product_card_ele = u"//div[contains(@class,'buy-again_productCard')]"
buy_again_selected_ele = u"//div[contains(@class, 'buy-again_productCard') and contains(@class, 'buy-again_selected')]"
buy_again_invalid_ele = u"//div[contains(@class, 'buy-again_productCard') and contains(@class, 'buy-again_inavailable')]"
buy_again_add_cart_button_ele = u"//button[contains(@class,'buy-again_addCart')]"

#  以上元素是老pc的，需要重新写
buy_again_x_btn = "//button[@data-testid='wid-drawer-close']"



