# PC端地址相关元素定位

# 首页地址按钮
ele_home_address_button = u"//div[@id='changeZipCode']"

# 新增地址按钮
ele_add_new_address_button = u"//button[text()='Add new address']"

# 地址表单元素
ele_address_street = u"input[placeholder='Street Address']"
ele_address_first_matched = u"#streetAddressList span"
ele_address_first_name = u"input[placeholder='First Name']"
ele_address_last_name = u"input[placeholder='Last Name']"
ele_address_phone = u"input[placeholder='Phone Number']"
ele_address_note = u"textarea[placeholder]"
ele_address_save_button = u"button[type='submit']"

# 地址簿中的地址
ele_address_book_item = u"//div[contains(@class, 'AddressCard_addressCard')]"
ele_address_book_name = u"//div[contains(@class, 'AddressCard_addressCard')]//strong"

# 账户页面元素
ele_account_button = u"//div[@data-testid='wid-account-menu']"
ele_account_addresses = u"//a[contains(@href, '/account/addresses')]"

# 订单详情页元素
ele_order_detail_address_section = u"//div[contains(@class, 'OrderDetail_addressSection')]"
ele_order_detail_change_address = u"//span[text()='Change']"

# Checkout页面元素
ele_checkout_address_section = u"//div[@data-testid='wid-checkout-address-selector']"
ele_checkout_add_address = u"//button[text()='Add new address']"

# 删除地址
ele_address_delete_button = u"//i[contains(@class, 'iconDelete')]"
ele_address_confirm_delete = u"//button[text()='Remove']"

# 测试用地址名称
ele_test_address_name = u"//strong[text()='Test Automation']"
