# 1. add to cart按钮
ele_pdp_add_to_cart = "//div[text()='Add to cart']"
ele_pdp_add_to_cart_attrs = "//div[text()='Add to cart']/attribute::*"
ele_pdp_add_cart_container = 'wid-add-cart-container'
ele_pdp_btn_atc_plus = 'btn-atc-plus'
ele_pdp_btn_add_favorite = 'btn-add-favorite'
# 2. 缩略图
ele_pdp_thumbnail = "div[class^='Header_thumbnail']"
# 3. 主图
ele_pdp_primary_img = "div[class^='Header_atlas']"

# 右侧信息区 Header_content
ele_pdp_header_content = "div[class^='Header_content']"

# related products div
ele_pdp_related = 'div[data-role="related"]'
# pdp 相关商品加购按钮
ele_pdp_related_add_to_cart_button = "div[data-role='related'] i[data-role='addButtonPlusIcon']"
# pdp product group 模块
ele_product_group = u"//div[contains(@class,'Variation_variation')]"
ele_product_group_title = ele_product_group+u"//div[contains(@class,'property_title')]"
ele_product_group_item_list = ele_product_group+u"//div[contains(@class,'property_listItem')]"


# pdp review 模块
ele_mod_review = u"//div[@data-testid='mod-reviews']"
ele_mod_review_card = ele_mod_review+u"//a[@data-testid='wid-review-card']"
# pdp review 片弹出pop
ele_review_pop = u"//div[@data-type='popup']"
ele_review_pop_review_list = ele_review_pop+u"//div[@data-testid='wid-review-list']"

# pdp review pop item list
ele_review_pop_item_list = ele_review_pop + u"//div[@data-testid='wid-review-item']"
# review pop 右上角x按钮
ele_review_pop_close_button = u"//div[@data-type='popup']//div[@data-testid='btn-modal-close']"


# pdp 视频模块
ele_mod_videos = u"//div[@data-testid='mod-videos']"
# 视频卡片
ele_video_card = u"//div[@data-testid='wid-video-card']"
# 视频卡片下方文案
ele_video_card_title = u"//div[@data-testid='text-video-card-title']"
# 视频下方头像
ele_video_card_avatar = u"//div[@data-testid='wid-video-card-avatar']"
# 视频下方点赞按钮
ele_video_card_like = u"//div[@data-testid='wid-set-like']"
# 视频卡片弹出pop
ele_video_pop = u"//div[@data-type='popup']//div[@data-testid='mod-video-modal']"
# 视频卡片弹出pop 右上角x按钮
video_pop_close_button = ele_video_pop + u"//div[@data-testid='btn-modal-close']"
# 视频pop的左侧视频模块
video_pop_video = u"//div[contains(@class,'video_video-box')]"
# 视频pop的左侧视频里的图片
video_pop_img = video_pop_video + u"//div[contains(@class,'video_video-box')]//img[@src]"
# 视频pop的左侧视频里的播放按钮
video_pop_play_icon = u"//div[contains(@class,'video_video-box')]//div[@data-type='play-icon']"
# 视频右侧评论输入框的placeholder
video_pop_comment = u"//div[@data-testid='wid-comments-input']"
# 视频右侧评论输入框 post 按钮
video_pop_post = u"//button[@data-testid='btn-comments-post']"
# 视频右侧评论输入框下方评论 模块
video_pop_comment_item = u"//button[@data-testid='wid-comment-item']"
# 视频右侧商品卡片模块
video_pop_product_card = u"//button[@data-testid='wid-product-card']"

# Global FBW配送信息模块 - 新版本
ele_pdp_global_fbw_info = "//div[@data-testid='wid-product-promotion-fulfilled-by-weee']"
# Global FBW配送信息模块图标
ele_pdp_global_fbw_info_icon = ele_pdp_global_fbw_info + "//img[@data-testid='wid-product-promotion-fulfilled-by-weee-icon']"
# Global FBW配送信息模块标题
ele_pdp_global_fbw_info_title = ele_pdp_global_fbw_info + "//div[@data-testid='wid-product-promotion-fulfilled-by-weee-title']"
# Global FBW配送信息模块副标题
ele_pdp_global_fbw_info_subtitle = ele_pdp_global_fbw_info + "//div[@data-testid='wid-product-promotion-fulfilled-by-weee-sub-title']"
# Global FBW配送信息模块内嵌Weee logo
ele_pdp_global_fbw_info_weee_logo = ele_pdp_global_fbw_info_title + "//img"

# 店铺推荐模块
ele_pdp_same_vendor_card = "//div[@data-testid='wid-pdp-same-vendor-card']"
# 店铺推荐模块标题
ele_pdp_same_vendor_title = ele_pdp_same_vendor_card + "//h3[contains(text(),'店铺推荐') or contains(text(),'판매자 추천') or contains(text(),'販売者のおすすめです') or contains(text(),'Gợi ý từ nhà cung cấp') or contains(text(),'Seller Recommended') or contains(text(),'店鋪推薦')]"
# 店铺推荐商品卡片容器
ele_pdp_same_vendor_product_card = ele_pdp_same_vendor_card + "//a[@data-testid='wid-product-card-container']"
# 店铺推荐商品名称
ele_pdp_same_vendor_product_name = ele_pdp_same_vendor_product_card + "//div[@data-testid='wid-product-card-title']"
# 店铺推荐商品价格
ele_pdp_same_vendor_product_price = ele_pdp_same_vendor_product_card + "//div[@data-testid='wid-product-card-price']"
# 店铺推荐收藏按钮
ele_pdp_same_vendor_favorite_btn = ele_pdp_same_vendor_card + "//button[@data-testid='btn-favorite']"
# 店铺推荐加购按钮
ele_pdp_same_vendor_atc_btn = ele_pdp_same_vendor_card + "//div[@data-testid='btn-atc-plus']"
# 店铺推荐轮播下一页按钮
ele_pdp_same_vendor_next_btn = ele_pdp_same_vendor_card + "//button[contains(@class,'absolute')]//span[text()='Next slide']/.."

# PDP商品Promotion模块
ele_pdp_product_promotions = "//div[@data-testid='wid-pdp-product-promotions']"
# 单个活动项
ele_pdp_product_promotion = ele_pdp_product_promotions + "//div[@data-testid='wid-pdp-product-promotion']"
# 活动图标
ele_pdp_product_promotion_icon = ele_pdp_product_promotion + "//img[@data-testid='wid-pdp-product-promotion-icon']"
# 活动标题
ele_pdp_product_promotion_title = ele_pdp_product_promotion + "//div[@data-testid='wid-pdp-product-promotion-title']"
# 活动规则描述
ele_pdp_product_promotion_rule_desc = ele_pdp_product_promotion + "//p[@data-testid='wid-pdp-product-promotion-rule-desc']"
# 查看更多按钮
ele_pdp_product_promotion_button = "//div[@data-testid='btn-pdp-product-promotion']"
# 查看更多按钮文本
ele_pdp_product_promotion_button_text = ele_pdp_product_promotion_button + "//span[@data-testid='wid-pdp-product-promotion-button-text']"
# 活动弹窗
ele_pdp_promotion_drawer = "//div[@data-testid='wid-promotion-drawer-wrapper']"

# 活动弹窗详细内容元素
# 活动弹窗标题
ele_promotion_drawer_title = "//h3[@data-testid='txt-promotion-drawer-title']"
# 活动弹窗描述
ele_promotion_drawer_description = "//p[@data-testid='txt-promotion-drawer-description']"
# 活动弹窗规则
ele_promotion_drawer_rules = "//p[@data-testid='txt-promotion-drawer-rules']"

# 活动弹窗商品列表
ele_promotion_drawer_product_list = "//div[@data-testid='wid-promotion-drawer-product-list']"
# 价格过滤器容器
ele_promotion_drawer_price_filters = "//div[@data-testid='wid-promotion-drawer-price-filters']"
# 价格过滤器选项（通用）
ele_promotion_drawer_price_filter_item = ele_promotion_drawer_price_filters + "//div[contains(@data-testid,'wid-promotion-drawer-price-filters-')]"

# 商家信息模块
ele_pdp_mkpl_intro = 'wid-pdp-mkpl-intro'
ele_pdp_mkpl_intro_content = 'wid-pdp-mkpl-intro-content'
ele_pdp_mkpl_intro_content_logo = 'wid-pdp-mkpl-intro-content-logo'
ele_pdp_mkpl_intro_content_logo_image = 'wid-pdp-mkpl-intro-content-logo-image'
ele_pdp_mkpl_intro_content_info = 'wid-pdp-mkpl-intro-content-info'
ele_pdp_mkpl_intro_content_info_name = 'wid-pdp-mkpl-intro-content-info-name'
ele_pdp_mkpl_intro_content_info_sales_volume = 'wid-pdp-mkpl-intro-content-info-sales-volume'
ele_pdp_mkpl_intro_content_info_sales_volume_value = 'wid-pdp-mkpl-intro-content-info-sales-volume-value'
ele_pdp_mkpl_intro_content_info_descriptions = 'wid-pdp-mkpl-intro-content-info-descriptions'
ele_btn_pdp_contact_seller = 'btn-pdp-contact-seller'

# 首次加购弹窗元素
ele_pdp_first_add_popup = 'wid-pdp-first-add-popup'
ele_pdp_first_add_popup_tag = 'wid-pdp-first-add-popup-tag'
ele_pdp_first_add_popup_tag_icon = 'wid-pdp-first-add-popup-tag-icon'
ele_pdp_first_add_popup_progress = 'wid-pdp-first-add-popup-progress'
ele_pdp_first_add_popup_progress_icon = 'wid-pdp-first-add-popup-progress-icon'
ele_pdp_first_add_popup_progress_value = 'wid-pdp-first-add-popup-progress-value'
ele_btn_pdp_first_add_popup = 'btn-pdp-first-add-popup'
ele_pdp_first_add_popup_button_text = 'wid-pdp-first-add-popup-button-text'
ele_pdp_first_add_popup_seller_info_sales_volume = 'wid-pdp-first-add-popup-seller-info-sales-volume'

# 再次加购弹窗元素
ele_pdp_rest_add_popup = 'wid-pdp-rest-add-popup'
ele_pdp_rest_add_popup_tag = 'wid-pdp-rest-add-popup-tag'
ele_pdp_rest_add_popup_tag_image = 'wid-pdp-rest-add-popup-tag-image'
ele_pdp_rest_add_popup_tag_content = 'wid-pdp-rest-add-popup-tag-content'
ele_pdp_rest_add_popup_tag_content_title = 'wid-pdp-rest-add-popup-tag-content-title'
ele_btn_pdp_rest_add_popup = 'btn-pdp-rest-add-popup'
ele_pdp_rest_add_popup_tag_content_button_icon = 'wid-pdp-rest-add-popup-tag-content-button-icon'