# topx 奖杯icon
ele_dweb_topx_cup_icon = "//div[@data-testid='wid-top-x-header-icon']"
# topx 榜单title
ele_dweb_topx_ranking_title = "//div[@data-testid='wid-top-x-header-title']"
# topx 副标题描述
ele_dweb_topx_ranking_subtitle = "//div[@data-testid='wid-top-x-header-desc']"
# topx 榜单标签
ele_dweb_topx_ranking_tag = "//div[contains(@data-testid, 'wid-top-x-ranking-')]"
# topx 产品卡片
ele_dweb_topx_prod = "//a[@data-testid='wid-product-card-container']"
# topx 产品卡片加购按钮
add_to_cart_button = "//div[@data-testid='btn-atc-plus']"
# topx加购产品卡片
ele_dweb_topx_prod_add_to_cart = ele_dweb_topx_prod + add_to_cart_button
# topx 每日榜单标题
ele_dweb_topx_ranking_title_daily = "//div[@data-testid='txt-top-x-daily-titlte']"
# topx more 按钮
ele_dweb_topx_more_button = "//a[@data-testid='btn-top-x-daily-see-all']"
# topx chart 页面
ele_dweb_topx_chart = "//div[@data-testid='txt-top-chart-title']"
# topx chart ranking title
ele_dweb_topx_chart_ranking_title = "//div[@data-testid='txt-top-chart-ranking-desc']"
# topx chart 分类icon
ele_dweb_topx_chart_category_icon = "//div[@data-testid='wid-category-list-wrapper']"
# topx chart list 标题：
ele_dweb_topx_chart_list_title = "//div[@data-testid='wid-category-list-wrapper']"
# topx chart top trending 标签#
ele_dweb_top_trending_tag = "//li[@data-testid='wid-sub-category-list-item-top_trending']"
# topx chart 好评榜标签展示：
ele_dweb_top_rated_tag ="//li[@data-testid='wid-sub-category-list-item-most_liked']"
# topx list 查看全部按钮
ele_dweb_topx_chart_list_see_all ="//div[contains(@data-testid, 'btn-top-chart-card-')]"
# ele topx list 商品卖点
ele_dweb_topx_prod_metrics = "//div[@data-testid='wid-product-card-metrics']"
# ele 产品卡片tag 展示
ele_dweb_topx_prod_tag = "//div[contains(@data-testid, 'wid-product-card-tag-link-top_ranking-')]"





