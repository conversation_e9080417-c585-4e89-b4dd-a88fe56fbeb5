"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   dweb_checkout_ele.py
@Description    :  PC结算页元素定位
@CreateTime     :  2025/6/10 14:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/10 14:30
"""

# 结算页标题
ele_checkout_title = u"//h1[@data-testid='wid-checkout-title']"

# 配送地址信息区域
ele_shipping_section = u"//div[@data-testid='wid-checkout-shipping-section']"
ele_address = u"//div[@data-testid='wid-checkout-address']"
ele_delivery_time = u"//div[@data-testid='wid-checkout-delivery-time']"
ele_address_name = u"//div[contains(@class, 'address-name')]"
ele_address_detail = u"//div[contains(@class, 'address-detail')]"
ele_delivery_date = u"//div[contains(@class, 'delivery-date')]"
ele_delivery_window = u"//div[contains(@class, 'delivery-window')]"
ele_checkout_address_info = 'wid-checkout-address-info'
ele_checkout_address_info_title = 'wid-checkout-address-info-title'
ele_checkout_address_card = 'wid-address-card'
ele_checkout_address_card_icon = 'wid-address-card-location-icon'
ele_address_card_name = 'wid-address-card-name'
ele_address_card_address = 'wid-address-card-address'
ele_address_card_city = 'wid-address-card-city'
ele_address_card_edit = 'wid-address-card-edit'
ele_checkout_address_info_address_bt = 'wid-checkout-address-info-add-address-btn'

# 支付信息区域
ele_payment_title = u"//div[contains(text(), 'Payment method')]"
ele_payment_method_point = u"//div[@class='payment-panel_points__PucSg']"
ele_payment_method = u"//div[@class='payment-panel_selectCard__vuyuE']"
ele_payment_card_number = u"//div[contains(@class, 'card-number')]"
ele_payment_card_expiry = u"//div[contains(@class, 'card-expiry')]"

# 订单摘要区域
ele_order_summary = u"//div[@data-testid='wid-checkout-order-summary']"
ele_subtotal = u"//div[@data-testid='wid-checkout-subtotal']"
ele_delivery_fee = u"//div[@data-testid='wid-checkout-delivery-fee']"
ele_tax = u"//div[@data-testid='wid-checkout-tax']"
ele_total = u"//div[@data-testid='wid-checkout-total']"
ele_order_summary_title = u"//div[contains(@class, 'summary-title')]"
ele_order_summary_items = u"//div[contains(@class, 'summary-items')]"
ele_order_summary_divider = u"//div[contains(@class, 'summary-divider')]"
ele_checkout_right_summary_item_amount='wid-checkout-right-purchase-summary-item-amount'

# 商品列表
ele_items = u"//div[@data-testid='wid-checkout-items']"
ele_item = u"//div[@data-testid='wid-checkout-item']"
ele_item_name = u"//div[@data-testid='wid-checkout-item-name']"
ele_item_price = u"//div[@data-testid='wid-checkout-item-price']"
ele_item_quantity = u"//div[@data-testid='wid-checkout-item-quantity']"
ele_item_image = u"//img[contains(@class, 'item-image')]"

# 下单按钮
ele_place_order = u"(//button[@data-testid='wid-checkout-btn'])[2]"


# 凑单banner
ele_free_shipping_banner = u"//div[@data-testid='wid-checkout-free-shipping-banner']"

# 小费区域
ele_tip_section = u"//div[@data-testid='wid-checkout-delivery-tip']"
ele_tip_options = u"//div[contains(@class, 'tip-options')]//button"
ele_tip_custom = u"//input[contains(@class, 'tip-custom')]"

# 优惠券区域
ele_coupon_section = u"//div[contains(@class, 'coupon-section')]"
ele_coupon_input = u"//input[contains(@placeholder, 'coupon') or contains(@placeholder, 'Coupon')]"
ele_coupon_apply = u"//button[contains(text(), 'Apply')]"

# 订单备注区域
ele_order_note = u"//textarea[contains(@placeholder, 'note') or contains(@placeholder, 'Note')]"

# 结算页顶部的weeelogo
ele_checkout_top_logo = u"//div[@role='heading']"

# 结算页顶部的提示文案
ele_checkout_top_text = u"//span[text()='Your information is secured with encryption.']"

# 结算页小费标题
ele_checkout_tip_title = u"//div[@data-testid='wid-checkout-delivery-tip']/div/span[1]"

# 最终总计下面的文案
ele_checkout_final_text = u"//div[@class='mt-1']/div[1]/span[1]"
ele_checkout_final_submit_text = u"//div[@class='mt-1']/div[2]/span[1]"

# 错误提示信息
ele_error_message = u"//div[contains(@class, 'error-message')]"

# 加载状态指示器
ele_loading_indicator = u"//div[contains(@class, 'loading') or contains(@class, 'spinner')]"

# 返回购物车按钮
ele_back_to_cart = u"//a[contains(text(), 'Back to Cart')]"

# 地址选择下拉菜单
ele_address_dropdown = u"//div[contains(@class, 'address-dropdown')]"

# 配送时间选择下拉菜单
ele_delivery_time_dropdown = u"//div[contains(@class, 'delivery-time-dropdown')]"

# 支付方式选择下拉菜单
ele_payment_method_dropdown = u"//div[contains(@class, 'payment-method-dropdown')]"

# 订单确认复选框
ele_order_confirmation_checkbox = u"//input[@type='checkbox' and contains(@id, 'confirmation')]"

# 订单条款和条件链接
ele_terms_conditions_link = u"//a[contains(text(), 'Terms') or contains(text(), 'Conditions')]"

# 隐私政策链接
ele_privacy_policy_link = u"//a[contains(text(), 'Privacy') or contains(text(), 'Policy')]"

# 帮助/支持链接
ele_help_support_link = u"//a[contains(text(), 'Help') or contains(text(), 'Support')]"

# 页面底部版权信息
ele_copyright_info = u"//div[contains(@class, 'copyright') or contains(text(), '©')]"

# 安全支付图标
ele_secure_payment_icons = u"//div[contains(@class, 'secure-payment') or contains(@class, 'payment-icons')]"

# 页面底部导航链接
ele_footer_navigation = u"//div[contains(@class, 'footer-navigation') or contains(@class, 'footer-links')]"

# 页面底部社交媒体链接
ele_social_media_links = u"//div[contains(@class, 'social-media') or contains(@class, 'social-links')]"

# 页面底部语言选择器
ele_language_selector = u"//div[contains(@class, 'language-selector')]"

# 页面底部货币选择器
ele_currency_selector = u"//div[contains(@class, 'currency-selector')]"

