# section 1
# 头部
ele_zipcode = u"//div[@id='changeZipCode']/div[position()=1]"
ele_deals = u"//div[contains(@class,'HeaderWithCategory')]//a[@aria-label='Deals']"
ele_bestsellers = u"//div[contains(@class,'HeaderWithCategory')]//a[@aria-label='Bestsellers']"
ele_new_arrivals = u"//div[contains(@class,'HeaderWithCategory')]//a[@aria-label='New arrivals']"

# 1. banner
ele_home_banner_active = "//span[contains(@class, 'swiper-slide-active')]/a"
ele_home_right_shift_button = "//div[@data-module='cm_main_banner']//i[@data-role='silde-panel-arrow-right']"

ele_home_page_snack = u"//span[text()='Snacks']"


ele_home_editors_pick = u"""//div[text()="Editor's Pick"]"""
# editor's pick下面的加购按钮
ele_home_editors_pick_add_to_cart = u"""//div[text()="Editor's Pick"]/../../../..//i[@data-role="addButtonPlusIcon"]"""
# editor's card div
ele_home_editors_pick_card_div = u"""//div[text()="Editor's Pick"]/../../../..//div[contains(@class, 'items-stretch')]"""

# new arrivals' 加购按钮
ele_home_new_arrival_add_to_cart = u"//div[text()='New Arrivals']/../../../..//i[@data-role='addButtonPlusIcon']"
# bestsellers' 加购按钮
ele_home_bestsellers_add_to_cart = u"//div[text()='Bestsellers']/../../../..//i[@data-role='addButtonPlusIcon']"
# global+ 加购按钮
ele_home_global_plus_add_to_cart = u"//div[contains(text(),'Global+ Top')]/../../../..//i[@data-role='addButtonPlusIcon']"
# fresh daily 加购按钮
ele_home_fresh_daily_add_to_cart = u"//div[text()='Fresh Daily']/../../../..//i[@data-role='addButtonPlusIcon']"
# everyday Deals 加购按钮
ele_home_everyday_deals_add_to_cart = u"//div[text()='Everyday deals']/../../../..//i[@data-role='addButtonPlusIcon']"
# recommended for you
ele_home_recommended_for_you_add_to_cart = u"//div[text()='Recommended For You']/../../../..//i[@data-role='addButtonPlusIcon']"
# 购物车按钮
ele_home_cart = "//span[text()='Cart']"

# 搜索
ele_home_search_span = u"""//span[text()="Search America's largest online Asian supermarket"]"""
ele_home_search_input = u"#searchInput"
ele_home_search_button = u"//span/button[@shape='round']"

# 热词搜索
# 火焰热词
# ele_hot_key_searches = u"div[class^='Search_tagContent'] i~span"
# 所有热词
ele_hot_key_searches = u"//div[text()='Popular searches']/../..//span"

# 左侧category, 其他的使用动态的变量表示，只是text()属性不同
ele_home_fresh_gourmet = u"//span[text()='Fresh Gourmet']"

# zipcode
ele_home_zipcode = "#changeZipCode"
# 整个zipcode弹框
ele_home_zipcode_popup_div = "div[class='ant-modal-content']"
# enter your zipcode
ele_home_enter_your_zipcode = "input[value]"
# confirm button
ele_home_confirm_zipcode = "div[class*='ChangeZipCode_confirm'] button[shape]"

# 首页商品title，不区分合集
ele_home_products_title = "div[class*='List_item'] div[class*='ProductCard_title']>span"

# 切换store
ele_home_store_selection = "div[class^='DefaultSidebar_storeSelectorWrapper']"








