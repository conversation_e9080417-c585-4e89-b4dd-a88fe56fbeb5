# section 1
# 头部
ele_zipcode = u"//div[@id='changeZipCode']/div[position()=1]"
# 更新Deals元素选择器，提供多种备选方案
ele_deals = "wid-direct-link-sale"
ele_bestsellers = "wid-direct-link-trending"
ele_new_arrivals = "wid-direct-link-new"
ele_global = "wid-direct-link-global"

# 1. banner
ele_home_banner_active = "//span[contains(@class, 'swiper-slide-active')]/a"
ele_home_right_shift_button = "//div[@data-module='cm_main_banner']//i[@data-role='silde-panel-arrow-right']"

ele_home_page_snack = u"//span[text()='Snacks']"


ele_home_editors_pick = u"""//div[text()="Editor's Pick"]"""
# editor's pick下面的加购按钮
ele_home_editors_pick_add_to_cart = u"""//div[text()="Editor's Pick"]/../../../..//i[@data-role="addButtonPlusIcon"]"""
# editor's card div
ele_home_editors_pick_card_div = u"""//div[text()="Editor's Pick"]/../../../..//div[contains(@class, 'items-stretch')]"""

# new arrivals' 加购按钮
ele_home_new_arrival_add_to_cart = u"//div[text()='New Arrivals']/../../../..//i[@data-role='addButtonPlusIcon']"

# global+ 加购按钮
ele_home_global_plus_add_to_cart = u"//div[contains(text(),'Global+ Top')]/../../../..//i[@data-role='addButtonPlusIcon']"
# fresh daily 加购按钮
ele_home_fresh_daily_add_to_cart = u"//div[text()='Fresh Daily']/../../../..//i[@data-role='addButtonPlusIcon']"
# everyday Deals 加购按钮
ele_home_everyday_deals_add_to_cart = u"//div[text()='Everyday deals']/../../../..//i[@data-role='addButtonPlusIcon']"
# recommended for you
ele_home_recommended_for_you_add_to_cart = u"//div[text()='Recommended For You']/../../../..//i[@data-role='addButtonPlusIcon']"
# 购物车按钮
ele_home_cart = "//span[text()='Cart']"

# 搜索
ele_home_search_span = u"""//span[text()="Search America's largest online Asian supermarket"]"""
ele_home_search_input = u"#searchInput"
ele_home_search_button = u"//span/button[@shape='round']"

# 热词搜索
# 火焰热词
# ele_hot_key_searches = u"div[class^='Search_tagContent'] i~span"
# 所有热词
ele_hot_key_searches = u"//div[text()='Popular searches']/../..//span"

# 左侧category, 其他的使用动态的变量表示，只是text()属性不同
ele_home_fresh_gourmet = u"//span[text()='Fresh Gourmet']"

# zipcode
ele_home_zipcode = "#changeZipCode"
# 整个zipcode弹框
ele_home_zipcode_popup_div = "div[class='ant-modal-content']"
# enter your zipcode
ele_home_enter_your_zipcode = "input[value]"
# confirm button
ele_home_confirm_zipcode = "div[class*='ChangeZipCode_confirm'] button[shape]"

# 首页商品title，不区分合集
ele_home_products_title = "div[class*='List_item'] div[class*='ProductCard_title']>span"

# 切换store
ele_home_store_selection = "//button[text()='Select store']"

# edlp tags 展示
ele_pc_home_everyday_value = "//div[@data-testid='wid-product-card-tag-list-item-edlp']"
ele_pc_pdp_everyday_value ="//span[text()='Everyday value']"

# bestseller组件展示

ele_pc_home_global_entrance = "wid-direct-link-global"

# 组件展示

ele_pc_home_global_entrance = "wid-direct-link-global"

# 组件展示
ele_pc_bestseller ="//section[@data-testid='mod-item-carousel-Bestsellers']"
ele_pc_bestseller_product = ele_pc_bestseller + "//a[@data-testid='wid-product-card-container']"
# 查看更多按钮：
more_link = "//a[@data-testid='btn-more-link']"
# 加购按钮
add_to_cart = "//div[@data-testid='btn-atc-plus']"
# bestsellers' 加购商品
ele_home_bestsellers_add_to_cart = ele_pc_bestseller + add_to_cart


# 新人专区组件:
ele_pc_basket_start ="//section[@data-testid='mod-item-carousel-新人专区']"
ele_pc_basket_start_zh_off_label ="//span[@data-testid='wid-product-card-label-off']"

#Recommended For You组件
ele_pc_rec ="//section[@data-testid='mod-item-carousel-Recommended-For-You']"
ele_pc_rec_product = ele_pc_rec + "//a[@data-testid='wid-product-card-container']"
# Recommended查看更多按钮：
rec_more_link = "//a[@data-testid='btn-more-link']"
# Recommended加购商品
ele_home_rec_add_to_cart = ele_pc_rec + add_to_cart



# 新品上架组件：
ele_pc_new_arrivals = "//section[@data-testid='mod-item-carousel-New-Arrivals']"
# 新品上架产品卡片展示：
ele_pc_new_arrivals_product = ele_pc_new_arrivals + "//a[@data-testid='wid-product-card-container']"
# 新品上架加购商品：
ele_home_new_arrivals_add_to_cart =ele_pc_new_arrivals + add_to_cart






