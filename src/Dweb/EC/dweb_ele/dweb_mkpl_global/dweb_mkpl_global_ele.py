# PC端全球购商店页面元素定义

# All stores 导航元素
ele_all_stores = "btn-all-store-origin-recommend"

# Japan 导航元素
ele_japan_store = "btn-all-store-origin-japan"

# Korea 导航元素
ele_korea_store = "btn-all-store-origin-korea"

# USA 导航元素
ele_usa_store = "btn-all-store-origin-usa"

# Others 导航元素
ele_others_store = "btn-all-store-origin-others"

# 商家卡片元素
ele_seller_carousel = "mod-mkpl-seller-line"
ele_seller_card_title = "wid-seller-title"
ele_seller_card_shipping_reminder = "wid-seller-shipping-reminder"
ele_seller_card_tags = "wid-seller-tags"

# 商家商品卡片元素
ele_seller_card_container = "wid-seller-container"
ele_seller_product_card = "wid-product-card-container"
ele_seller_product_atc = "btn-atc-plus"
ele_seller_product_atc_role = "//div[@data-testid='btn-atc-plus' and @role='button' and @aria-label='add-to-cart']"
ele_seller_product_minus = "btn-atc-minus"
ele_product_card_image = 'wid-product-card-image'
ele_product_card_price = 'wid-product-card-price'
ele_product_card_base_price = 'wid-product-card-base-price'
ele_product_card_title = 'wid-product-card-title'
ele_product_card_brand = 'wid-product-card-brand'
ele_carousel_next = 'btn-carousel-next'
# 购物车数量元素
ele_data_count = "btn-atc-quantity"







