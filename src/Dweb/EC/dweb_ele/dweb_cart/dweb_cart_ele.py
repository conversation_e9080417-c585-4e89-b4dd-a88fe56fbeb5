# mini空购物车img
# 购物车顶部
from src.Mweb.EC.mweb_ele.mweb_cart.mweb_cart_ele import ele_cart_trade_in

ele_cart_header = 'wid-cart-summary-normal-0'
# ###############################空购物车 模块#########################################
# 空购物车img
ele_cart_content = "wid-cart-empty-cart-content"

ele_cart_img = "wid-cart-empty-cart-image"
# 空购物车下Your cart is empty文案
ele_cart_text = "wid-cart-empty-cart-title"
# 空购物车Start shopping按钮
ele_cart_start_shopping = "wid-cart-empty-start-shopping"


# ###############################购物车模块 模块#########################################
# 生鲜/pantry/seller购物车
ele_cart_normal = 'wid-cart-summary-normal-0'
ele_cart_pantry = 'wid-cart-summary-pantry-1'
ele_cart_seller = 'wid-cart-summary-seller-1'

# 购物车商品卡片
ele_cart_normal_card = "wid-cart-section-normal-goods"
ele_cart_pantry_card = "wid-cart-section-pantry-goods"
ele_cart_seller_card = "wid-cart-section-seller-goods"
# 购物车商品卡片
ele_cart_normal_goods = 'wid-cart-section-normal-goods'
ele_cart_seller_goods='wid-cart-section-seller-goods'
ele_cart_pantry_goods = 'wid-cart-section-pantry-goods'

# 购物车商品卡片标题
ele_cart_normal_goods_name = 'wid-cart-section-normal-goods-name'
ele_cart_seller_goods_name = 'wid-cart-section-seller-goods-name'
ele_cart_pantry_goods_name = 'wid-cart-section-pantry-goods-name'

# 购物车商品价格
ele_cart_normal_goods_price = 'wid-cart-section-normal-goods-price'
# 购物车商品划线价
ele_cart_normal_goods_base_price = 'wid-cart-section-normal-goods-base-price'
ele_cart_seller_goods_price = 'wid-cart-section-seller-goods-price'
# seller购物车划线价
ele_cart_seller_goods_base_price = 'wid-cart-section-seller-goods-base-price'
ele_cart_pantry_goods_price = 'wid-cart-section-pantry-goods-price'
# pantry购物车划线价
ele_cart_pantry_goods_base_price = 'wid-cart-section-pantry-goods-base-price'

# ###############################save for later 模块#########################################
ele_cart_s4l_normal_btn = 'wid-cart-section-normal-goods-save-for-later-btn'
ele_cart_s4l_pantry_btn = 'wid-cart-section-pantry-goods-save-for-later-btn'
ele_cart_s4l_seller_btn = 'wid-cart-section-seller-goods-save-for-later-btn'
ele_cart_s4l_title = 'wid-cart-save-for-later-title'
# 稍后再买商品卡片
ele_cart_s4l_card = 'wid-cart-save-for-later-product-item'
# save for later 按钮
ele_cart_normal_s4l = "wid-cart-section-normal-goods-save-for-later-btn"
ele_cart_pantry_s4l = "wid-cart-section-pantry-goods-save-for-later-btn"
ele_cart_seller_s4l = "wid-cart-section-seller-goods-save-for-later-btn"

# remove 按钮
ele_cart_normal_remove = "wid-cart-section-normal-goods-remove-btn"
ele_cart_pantry_remove = "wid-cart-section-pantry-goods-remove-btn"
ele_cart_seller_remove = "wid-cart-section-seller-goods-remove-btn"

# ###############################左侧购物车各种费用金额#########################################
# 生鲜/pantry/seller购物车name
ele_cart_normal_cart_name = "wid-cart-section-normal-name"
ele_cart_pantry_cart_name = "wid-cart-section-pantry-name"
ele_cart_seller_cart_name = "wid-cart-section-seller-name"
# 生鲜/pantry/seller购物车 img
ele_cart_normal_cart_img = "wid-cart-section-normal-image"
ele_cart_pantry_cart_img = "wid-cart-section-pantry-image"
ele_cart_seller_cart_img = "wid-cart-section-seller-image"
# 生鲜/pantry/seller购物车 副title
ele_grocery_cart_text = "wid-cart-section-normal-desc"
ele_grocery_pantry_text = "wid-cart-section-pantry-desc"
ele_grocery_mkpl_text = "wid-cart-section-seller-desc"

# 生鲜/pantry/seller购物车 items
ele_cart_normal_cart_item_qty_total = 'wid-cart-section-normal-cart-item-qty-total'
ele_cart_pantry_cart_item_qty_total = 'wid-cart-section-pantry-cart-item-qty-total'
ele_cart_seller_cart_item_qty_total = 'wid-cart-section-seller-cart-item-qty-total'

# 生鲜/pantry/seller购物车 items total金额
ele_cart_normal_cart_item_total_price = "wid-cart-section-normal-cart-item-total-price"
ele_cart_pantry_cart_item_total_price = "wid-cart-section-pantry-cart-item-total-price"
ele_cart_seller_cart_item_total_price = "wid-cart-section-seller-cart-item-total-price"

# 生鲜/pantry/seller购物车 Delivery fee金额
ele_cart_normal_delivery_fee = "wid-cart-section-normal-shipping-fee-price"
ele_cart_pantry_delivery_fee = "wid-cart-section-pantry-shipping-fee-price"
ele_cart_seller_delivery_fee = "wid-cart-section-seller-shipping-fee-price"

# 生鲜/pantry/seller购物车 Service fee金额
ele_cart_normal_service_fee = "wid-cart-section-normal-service-fee-price"
ele_cart_pantry_service_fee = "wid-cart-section-pantry-service-fee-price"
ele_cart_seller_service_fee = "wid-cart-section-seller-service-fee-price"

# ###############################右侧summary购物车列表模块#########################################
# 右侧购物车summary模块
ele_cart_summary_total_list = 'wid-cart-summary-main'
# 右侧summary 文案
ele_cart_summary = "wid-cart-summary-title"
ele_cart_summary_item = "wid-cart-summary-item"
# 右侧 summary下面items_total
# total
ele_cart_items_total = "wid-cart-summary-item-items-total"
ele_cart_items_total_text = "wid-cart-summary-item-items-total-text"
ele_cart_items_total_amount = "wid-cart-summary-item-items-total-amount-text"
# 如果为多个购物车，则返回list
# delivery_fee
ele_cart_delivery_fee = "wid-cart-summary-item-delivery-fee"
ele_cart_delivery_fee_text = "wid-cart-summary-item-delivery-fee-text"
ele_cart_delivery_fee_amount = "wid-cart-summary-item-delivery-fee-amount-text"
# service_fee
ele_cart_service_fee = "wid-cart-summary-item-delivery-service-fee"
ele_cart_service_fee_text = "wid-cart-summary-item-delivery-service-fee-text"
ele_cart_service_fee_amount = "wid-cart-summary-item-delivery-service-fee-amount-text"

# subtotal的金额
ele_cart_subtotal = "wid-cart-summary-subtotal"
ele_cart_subtotal_text = "wid-cart-summary-subtotal-text"
ele_cart_subtotal_fee = "wid-cart-summary-subtotal-amount-text"
# 右侧结算按钮
ele_cart_summary_checkout = "wid-cart-summary-checkout"


# ===============================购物车元素======================================================
# 购物车商品加号
ele_cart_atc_normal_plus = 'btn-atc-plus'
# 购物车商品减号
ele_cart_atc_normal_minus = 'btn-atc-minus'
ele_cart_normal_img = 'wid-cart-section-normal-image'
ele_cart_normal_name = 'wid-cart-section-normal-name'
ele_cart_normal_delivery_date = "wid-cart-section-normal-delivery-date"


# 购物车标题
ele_cart_direct_mail_top_title = u"//*[contains(text(), 'Direct mail')]"
ele_cart_pantry_top_title = u"//*[contains(text(), 'Pantry+')]"
ele_mo_cart_text = u"//*[contains(text(), 'Shipping via FedEx, UPS, etc.')]"
# delivery fee 文案
ele_cart_normal_cart_delivery_fee = ''
ele_cart_normal_cart_delivery_fee_original_price = 'wid-cart-section-normal-shipping-fee-original-price'
# deliveryfree的icon
ele_cart_normal_cart_delivery_fee_free = 'wid-cart-section-normal-shipping-fee-free'
# service 整个模块
ele_cart_normal_cart_delivery_fee_delivery_service_fee = 'wid-cart-section-normal-service-fee-delivery_service_fee'
ele_cart_normal_cart_service_fee = 'wid-cart-section-normal-shipping-fee-free'
# service fee的i标签
ele_cart_normal_cart_service_fee_info_outline ='wid-cart-section-normal-service-fee-delivery_service_fee-info-outline'
# service fee划线价
ele_cart_normal_cart_service_fee_original_price = 'wid-cart-section-normal-service-fee-delivery_service_fee-original-price'
# service fee的free的icon
ele_cart_normal_cart_service_fee_free='wid-cart-section-normal-service-fee-delivery_service_fee-free-text'
# mo地区shipping fee
ele_cart_mo_shipping_fee_text = 'wid-cart-section-normal-shipping-fee-free-text'
ele_cart_mo_shipping_fee_price = 'wid-cart-section-normal-shipping-fee-price'

# mof购物车
ele_cart_cold_package_fee_price ='wid-cart-section-normal-service-fee-cold_package_fee-price'
ele_cart_cold_package_fee_text = u"//*[contains(text(), 'Cold Pack Fee')]"
ele_cart_goods_tag = 'wid-cart-section-normal-goods-tag'



# ===========================================MiniCart===========================================================================
ele_mini_cart_button  = "mini-cart-button"
ele_mini_cart_wrapper = "btn-mini-cart-wrapper"
ele_mini_cart_label = "wid-mini-cart-label"

ele_mini_cart_img = u"//div[contains(@class,'MiniCart_emptyCartWrapper')]//img[contains(@src,'cart_empty')]"
# mini空购物车下Your cart is empty文案
ele_mini_cart_text = u"//div[contains(@class,'MiniCart_emptyCartWrapper')]//div[text()='Your cart is empty']"
# mini 购物车
# mini 购物车进度条模块
ele_mini_progress = u"//div[contains(@class,'ProgressTip_progressTip')]"
# mini 购物车进度条文案
ele_mini_progress_tip_copy = u"(//div[contains(@class,'ProgressTip_progressTip')]//div)[1]//div"
# mini 购物车进度条
ele_mini_progress_tip_bar = ele_mini_progress + u"//div[contains(@class,'ProgressTip_bar')]"

ele_mini_items_num = u"//div[contains(@class,'MiniCart_panelHeader')]//span[@class='text-surface-100-fg-default']"
ele_mini_items_list = u"//div[contains(@class,'MiniCart_goodsCart')]"
ele_mini_goto_cart_button = u"//div[contains(@class,'MiniCart_footer')]//button"



# ###############################新人购物车免运费banner模块#########################################
# 元素待补充
ele_cart_banner_normal = "wid-cart-section-normal-shipping-fee-banner"
ele_cart_banner_pantry = "wid-cart-section-pantry-shipping-fee-banner"
ele_cart_banner_pantry_card = ele_cart_banner_pantry
# ###############################购物车免运费/凑单模块#########################################
ele_cart__normal_activity_list = "wid-cart-section-_normal-activity-list"
ele_cart_pantry_activity_list = "wid-cart-section-pantry-activity-list"
ele_cart_seller_activity_list = "wid-cart-section-seller-activity-list"
# item
ele_cart__normal_activity_item = "wid-cart-section-_normal-activity-item"
ele_cart_pantry_activity_item = "wid-cart-section-pantry-activity-item"
ele_cart_seller_activity_item = "wid-cart-section-seller-shipping-fee-banner"
# icon
ele_cart__normal_activity_tag = "wid-cart-section-_normal-activity-tag"
ele_cart_pantry_activity_tag = "wid-cart-section-pantry-activity-tag"
ele_cart_seller_activity_tag = "wid-cart-section-seller-activity-tag-icon"
# content
ele_cart__normal_activity_content = "wid-cart-section-_normal-activity-content"
ele_cart_pantry_activity_content = "wid-cart-section-pantry-activity-content"
ele_cart_seller_activity_content = "wid-cart-section-seller-shipping-fee-banner-content"


# ###############################购物车换购模块模块#########################################
# 购物车换购banner
ele_cart_trade_in_normal_banner = "wid-cart-section-normal-activity-item-wrapper"
ele_cart_trade_in_pantry_banner =  "wid-cart-section-pantry-activity-item-wrapper"
ele_cart_trade_in_normal_banner_icon =  "wid-cart-section-normal-activity-item-icon"

# ###############################购物车活动模块 模块#########################################
# 元素待补充


# 生鲜购物车切换日期按钮  -- PC 这里没有id 找不到
# ele_cart_normal_delivery_date = ele_cart_normal + u"//button[@data-testid='btn-change-delivery-date']"


# 购物车商品列表
ele_cart_products = u"//div[@data-testid='wid-cart-section-goods']"
ele_remove = u"//div[@data-testid='wid-cart-section-remove']"
ele_save_for_later = u"//div[@data-testid='wid-cart-section-save-for-later']"
ele_cart_product_title = u"//div[contains(@class, 'GoodsInCart_name')]//span"


#
# ###############################中间页 模块#########################################
# 选择要结算的购物车
ele_cart_select_carts_to_checkout = 'wid-cart-select-cart-dialog-title'
# 结算所有购物车
ele_cart_select_all_carts = 'btn-select-all-carts'
# 各类型购物车复选框
ele_cart_select_normal = 'btn-select-cart-normal'
ele_cart_select_pantry = 'btn-select-cart-pantry'
ele_cart_select_seller ='btn-select-cart-seller'

# checkout按钮
ele_cart_checkout = 'wid-cart-select-checkout-btn'
ele_cart_select_tips = 'wid-cart-select-tips'
# 底部小计金额
ele_cart_middle_subtotal = u"//div[contains(text(), 'Subtotal')]"
# upsell
ele_cart_upsell_continue_checkout = u"//button[text()='Continue to checkout']"

# 选择地址
ele_cart_select_address = u"(//div[@data-id]//div[contains(text(), 'Bothell')])[1]"
# place order
ele_cart_place_order = u"//span[@class='font-semibold']/following-sibling::button"

# 购物车标题 css selector, 有多个购物车返回列表
ele_cart_each_cart_div = u"//main[@id='cart-main']//div[contains(@class, 'SelectableCart_combineItem')]"
ele_cart_each_title = u"#cart-main div[class^='SelectableCart_combineItem'] h2"
# 每个购物车的商品， 在程序中处理
# ################################猜你喜欢模块######################################
# 猜你喜欢模块
# 购物车中的recommendations
ele_cart_recommendations = u"//span[text()='Recommendations']"
# recommendations下面的所有商品，list
ele_cart_recommendations_all_goods = ele_cart_recommendations + u"/../following-sibling::div//div[contains(@class, 'List_item')]"
# 为你推荐模块
ele_recommend_module = "wid-cart-preference"
# 再次购买模块
ele_buy_again_module = u"//div[contains(@class,'cart-v2_histories')]"
# 为你推荐模块商品卡片
ele_recommend_module_card = ele_recommend_module + u"//div[contains(@class,'List_item')]"
# 再次购买模块商品卡片
ele_buy_again_module_card = ele_buy_again_module + u"//div[contains(@class,'List_item')]"
