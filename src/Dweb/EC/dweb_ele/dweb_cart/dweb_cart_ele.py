# mini空购物车img
# 购物车顶部
ele_cart_header = 'wid-cart-summary-normal-0'
# ###############################购物车模块 模块#########################################
# 生鲜/pantry/seller购物车
ele_cart_normal = 'wid-cart-summary-normal-0'
ele_cart_pantry = 'wid-cart-summary-pantry-1'
ele_cart_seller = 'wid-cart-summary-seller-1'

# ===============================购物车元素======================================================
# 购物车商品加号
ele_cart_atc_normal_plus = 'btn-atc-plus'
# 购物车商品减号
ele_cart_atc_normal_minus = 'btn-atc-minus'
ele_cart_normal_img = 'wid-cart-section-normal-image'
ele_cart_normal_name = 'wid-cart-section-normal-name'
ele_cart_normal_delivery_date = ele_cart_normal + 'wid-cart-section-normal-delivery-date'
ele_cart_normal_cart_item_qty_total = 'wid-cart-section-normal-cart-item-qty-total'
ele_cart_normal_cart_item_total_price = 'wid-cart-section-normal-cart-item-total-price'
ele_grocery_cart_text = u"//*[contains(text(), 'Delivered by Weee! Truck')]"
ele_grocery_mkpl_text = u"//*[contains(text(), 'Shipping via FedEx, UPS, etc.')]"

# 购物车标题
ele_cart_direct_mail_top_title = u"//*[contains(text(), 'Direct mail')]"
ele_cart_pantry_top_title = u"//*[contains(text(), 'Pantry+')]"
ele_mo_cart_text = u"//*[contains(text(), 'Shipping via FedEx, UPS, etc.')]"
# delivery fee 文案
ele_cart_normal_cart_delivery_fee = ''
ele_cart_normal_cart_delivery_fee_original_price = 'wid-cart-section-normal-shipping-fee-original-price'
# deliveryfree的icon
ele_cart_normal_cart_delivery_fee_free = 'wid-cart-section-normal-shipping-fee-free'
# service 整个模块
ele_cart_normal_cart_delivery_fee_delivery_service_fee = 'wid-cart-section-normal-service-fee-delivery_service_fee'
ele_cart_normal_cart_service_fee = 'wid-cart-section-normal-shipping-fee-free'
# service fee的i标签
ele_cart_normal_cart_service_fee_info_outline ='wid-cart-section-normal-service-fee-delivery_service_fee-info-outline'
# service fee划线价
ele_cart_normal_cart_service_fee_original_price = 'wid-cart-section-normal-service-fee-delivery_service_fee-original-price'
# service fee的free的icon
ele_cart_normal_cart_service_fee_free='wid-cart-section-normal-service-fee-delivery_service_fee-free-text'
# mo地区shipping fee
ele_cart_mo_shipping_fee_text = 'wid-cart-section-normal-shipping-fee-free-text'
ele_cart_mo_shipping_fee_price = 'wid-cart-section-normal-shipping-fee-price'

# mof购物车
ele_cart_cold_package_fee_price ='wid-cart-section-normal-service-fee-cold_package_fee-price'
ele_cart_cold_package_fee_text = u"//*[contains(text(), 'Cold Pack Fee')]"
ele_cart_goods_tag = 'wid-cart-section-normal-goods-tag'
# 购物车商品卡片
ele_cart_normal_goods = 'wid-cart-section-normal-goods'
ele_cart_seller_goods='wid-cart-section-seller-goods'
ele_cart_pantry_goods = 'wid-cart-section-pantry-goods'

# 购物车商品卡片标题
ele_cart_normal_goods_name = 'wid-cart-section-normal-goods-name'
ele_cart_seller_goods_name = 'wid-cart-section-seller-goods-name'
ele_cart_pantry_goods_name = 'wid-cart-section-pantry-goods-name'

# 购物车商品价格
ele_cart_normal_goods_price = 'wid-cart-section-normal-goods-price'
# 购物车商品划线价
ele_cart_normal_goods_base_price = 'wid-cart-section-normal-goods-base-price'
ele_cart_seller_goods_price = 'wid-cart-section-seller-goods-price'
# seller购物车划线价
ele_cart_seller_goods_base_price = 'wid-cart-section-seller-goods-base-price'
ele_cart_pantry_goods_price = 'wid-cart-section-pantry-goods-price'
# pantry购物车划线价
ele_cart_pantry_goods_base_price = 'wid-cart-section-pantry-goods-base-price'

# ======================================================================================================================
ele_mini_cart_img = u"//div[contains(@class,'MiniCart_emptyCartWrapper')]//img[contains(@src,'cart_empty')]"
# mini空购物车下Your cart is empty文案
ele_mini_cart_text = u"//div[contains(@class,'MiniCart_emptyCartWrapper')]//div[text()='Your cart is empty']"
# mini 购物车
# mini 购物车进度条模块
ele_mini_progress = u"//div[contains(@class,'ProgressTip_progressTip')]"
# mini 购物车进度条文案
ele_mini_progress_tip_copy = u"(//div[contains(@class,'ProgressTip_progressTip')]//div)[1]//div"
# mini 购物车进度条
ele_mini_progress_tip_bar = ele_mini_progress + u"//div[contains(@class,'ProgressTip_bar')]"

ele_mini_items_num = u"//div[contains(@class,'MiniCart_panelHeader')]//span[@class='text-surface-100-fg-default']"
ele_mini_items_list = u"//div[contains(@class,'MiniCart_goodsCart')]"
ele_mini_goto_cart_button = u"//div[contains(@class,'MiniCart_footer')]//button"
# ###############################空购物车 模块#########################################
# 空购物车img
ele_cart_img = u"//img[contains(@src,'cart_empty')]"
# 空购物车下Your cart is empty文案
ele_cart_text = u"//main[@id='cart-main']//div[text()='Your cart is empty']"
# 空购物车Start shopping按钮
ele_cart_start_shopping = u"//div[contains(@class,'cart-v2_emptyBtnWrapper')]//button[@shape='round']"



# ###############################购物车免运费banner模块#########################################
# 元素待补充
ele_cart_banner_normal = ele_cart_normal+ u"//div[contains(@class,'SelectableCart_cartPanel')]//*[contains(text(), 'free delivery')]"
ele_cart_banner_normal_card = ele_cart_banner_normal
ele_cart_banner_pantry = ele_cart_pantry+u"//div[contains(@class,'SelectableCart_cartPanel')]//*[contains(text(), 'free delivery')]"
ele_cart_banner_pantry_card = ele_cart_banner_pantry


# ###############################购物车换购模块模块#########################################
# 元素待补充
ele_cart_trade_in_normal = u"//div[contains(@class,'CartActivity_activityWrapper')]"
ele_cart_trade_in_normal_card = ele_cart_trade_in_normal
ele_cart_trade_in_pantry = u"//div[contains(@class,'CartActivity_activityWrapper')]"
ele_cart_trade_in_pantry_card = ele_cart_trade_in_normal

# ###############################购物车活动模块 模块#########################################
# 元素待补充
ele_cart_activity_normal = ele_cart_normal+ u"//div[contains(@class,'CartActivity_activityWrapper')]"
ele_cart_activity_pantry = ele_cart_pantry+ u"//div[contains(@class,'CartActivity_activityWrapper')]"

ele_cart_activity_normal_card = ele_cart_activity_normal+ u"//div[contains(@class,'CartActivity_activityWrapper')]"
ele_cart_activity_pantry_card = ele_cart_activity_pantry+ u"//div[contains(@class,'CartActivity_activityWrapper')]"


# 生鲜购物车切换日期按钮  -- PC 这里没有id 找不到
# ele_cart_normal_delivery_date = ele_cart_normal + u"//button[@data-testid='btn-change-delivery-date']"

# 购物车商品卡片
ele_cart_normal_card = ele_cart_normal+'wid-cart-section-goods'

# ele_cart_normal_card = ele_cart_normal+u"//div[@data-testid='wid-cart-section-goods']"
ele_cart_pantry_card = ele_cart_pantry+u"//div[@data-testid='wid-cart-section-goods']"
ele_cart_seller_card = ele_cart_seller+u"//div[@data-testid='wid-cart-section-goods']"
# save for later 按钮
ele_cart_normal_s4l = ele_cart_normal+u"//div[@data-testid='wid-cart-section-save-for-later']"
ele_cart_pantry_s4l = ele_cart_pantry+u"//div[@data-testid='wid-cart-section-save-for-later']"
ele_cart_seller_s4l = ele_cart_seller+u"//div[@data-testid='wid-cart-section-save-for-later']"
# remove 按钮
ele_cart_normal_remove = ele_cart_normal_card+u"//div[@data-testid='wid-cart-section-remove']"
ele_cart_pantry_remove = ele_cart_pantry_card+u"//div[@data-testid='wid-cart-section-remove']"
ele_cart_seller_remove = ele_cart_seller_card+u"//div[@data-testid='wid-cart-section-remove']"

# 购物车商品列表
ele_cart_products = u"//div[@data-testid='wid-cart-section-goods']"
ele_remove = u"//div[@data-testid='wid-cart-section-remove']"
ele_save_for_later = u"//div[@data-testid='wid-cart-section-save-for-later']"
ele_cart_product_title = u"//div[contains(@class, 'GoodsInCart_name')]//span"

# ###############################save for later 模块#########################################
ele_cart_s4l_normal_btn = 'wid-cart-section-normal-goods-save-for-later-btn'
ele_cart_s4l_pantry_btn = 'wid-cart-section-pantry-goods-save-for-later-btn'
ele_cart_s4l_seller_btn = 'wid-cart-section-seller-goods-save-for-later-btn'
ele_cart_s4l_title = 'wid-cart-save-for-later-title'
# 稍后再买商品卡片
ele_cart_s4l_card = 'wid-cart-save-for-later-product-item'
#
# ###############################中间页 模块#########################################
# 选择要结算的购物车
ele_cart_select_carts_to_checkout = 'wid-cart-select-cart-dialog-title'
# 结算所有购物车
ele_cart_select_all_carts = 'btn-select-all-carts'
# 各类型购物车复选框
ele_cart_select_normal = 'btn-select-cart-normal'
ele_cart_select_pantry = 'btn-select-cart-pantry'
ele_cart_select_seller ='btn-select-cart-seller'

# checkout按钮
ele_cart_checkout = 'wid-cart-select-checkout-btn'
ele_cart_select_tips = 'wid-cart-select-tips'
# 底部小计金额
ele_cart_middle_subtotal = u"//div[contains(text(), 'Subtotal')]"
# upsell
ele_cart_upsell_continue_checkout = u"//button[text()='Continue to checkout']"

# 选择地址
ele_cart_select_address = u"(//div[@data-id]//div[contains(text(), 'Bothell')])[1]"
# place order
ele_cart_place_order = u"//span[@class='font-semibold']/following-sibling::button"

# ###############################左侧购物车各种费用金额#########################################
# 生鲜/pantry/seller购物车 各种费用金额模块
ele_cart_normal_cart_info = ele_cart_normal+ u"//div[contains(@class,'SelectableCart_info')]"
ele_cart_pantry_cart_info = ele_cart_pantry+ u"//div[contains(@class,'SelectableCart_info')]"
ele_cart_seller_cart_info = ele_cart_seller+ u"//div[contains(@class,'SelectableCart_info')]"
# 生鲜/pantry/seller购物车 items total金额
ele_cart_normal_item_total = ele_cart_normal_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[1]//span[2]"
ele_cart_pantry_item_total = ele_cart_pantry_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[1]//span[2]"
ele_cart_seller_item_total = ele_cart_seller_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[1]//span[2]"

# 生鲜/pantry/seller购物车 Delivery fee金额
ele_cart_normal_delivery_fee = ele_cart_normal_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[2]//span[2]"
ele_cart_pantry_delivery_fee = ele_cart_pantry_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[2]//span[2]"
ele_cart_seller_delivery_fee = ele_cart_seller_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[2]//span[2]"

# 生鲜/pantry/seller购物车 Service fee金额
ele_cart_normal_service_fee = ele_cart_normal_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[3]//span[2]"
ele_cart_pantry_service_fee = ele_cart_pantry_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[3]//span[2]"
ele_cart_seller_service_fee = ele_cart_seller_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[3]//span[2]"
# ###############################右侧summary购物车列表模块#########################################
# 如果为多个购物车，则返回list
ele_cart_summary_list = u"//main[@id='cart-main']//div[contains(@data-testid,'wid-cart-summary')]"
# 右侧购物车summary模块
ele_cart_summary_total_list = 'wid-cart-summary-main'

# 右侧summary 文案
# ele_cart_summary = u"//div[text()='Summary']"
ele_cart_summary = ele_cart_summary_total_list + u"//div[contains(@class,'PanelCheckout_title')]"

# 右侧 summary下面第一个购物车为local delivery 文案
ele_cart_summary_local_delivery = ele_cart_summary_total_list + u"//div[@data-testid='wid-cart-summary-item-0']//div[1]//span[contains(@class, 'items-center')]"
# 右侧 summary下面items_total
ele_cart_items_total = u"//div[@class='transition-all']//span[text()='Items total']/following-sibling::span"

# ele_cart_items_total = u"//div[@class='relative']//span[text()='Items total']/following-sibling::span"
# 如果为多个购物车，则返回list
ele_cart_delivery_fee = u"//div[@class='relative']//span[text()='Delivery fee']/following-sibling::span"
# subtotal
ele_cart_subtotal = u"//div[@data-testid='wid-cart-summary-subtotal']"

# ele_cart_subtotal = u"//span[text()='Subtotal']"
# subtotal的金额
ele_cart_subtotal_fee = ele_cart_subtotal + u"//following-sibling::span"
# ele_cart_subtotal_fee = u"//span[text()='Subtotal']/following-sibling::span"
# 购物车标题 css selector, 有多个购物车返回列表
ele_cart_each_cart_div = u"//main[@id='cart-main']//div[contains(@class, 'SelectableCart_combineItem')]"
ele_cart_each_title = u"#cart-main div[class^='SelectableCart_combineItem'] h2"
# 每个购物车的商品， 在程序中处理
# ################################猜你喜欢模块######################################
# 猜你喜欢模块
# 购物车中的recommendations
ele_cart_recommendations = u"//span[text()='Recommendations']"
# recommendations下面的所有商品，list
ele_cart_recommendations_all_goods = ele_cart_recommendations + u"/../following-sibling::div//div[contains(@class, 'List_item')]"
# 为你推荐模块
ele_recommend_module = u"//div[contains(@class,'cart-v2_recommends')]"
# 再次购买模块
ele_buy_again_module = u"//div[contains(@class,'cart-v2_histories')]"
# 为你推荐模块商品卡片
ele_recommend_module_card = ele_recommend_module + u"//div[contains(@class,'List_item')]"
# 再次购买模块商品卡片
ele_buy_again_module_card = ele_buy_again_module + u"//div[contains(@class,'List_item')]"
