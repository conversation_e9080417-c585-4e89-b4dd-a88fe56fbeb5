import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.data_track.trace_page.track_mweb_page_home import TrackMWebPageHome
from src.config.weee.log_help import log


@allure.story("mweb首页埋点UI操作")
class TestMWebMyCanceledOrderUIUX:
    pytestmark = [pytest.mark.track, pytest.mark.track_mweb_home]
    @allure.title("mweb首页埋点UI操作")
    # @pytest.mark.repeat(10)
    def test_000_track_mweb_home_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        MWEB 首页UI操作
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 进入home页面
        log.info("步骤1：进入首页")
        hp = TrackMWebPageHome(p, h5_autotest_header, c)

        # # 2. 切换store
        # hp.switch_store()

        # 3. 点击banner
        hp.click_banner_and_return()

        # # 4. 点击分类并返回
        # hp.click_category_and_return()

        # 5. 点击商品并加购
        hp.add_product_to_cart()

        # # 6. 滚动到底部
        # hp.scroll_to_bottom()
        print(1)


