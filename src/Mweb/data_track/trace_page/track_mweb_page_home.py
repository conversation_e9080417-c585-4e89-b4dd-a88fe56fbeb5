from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.common.commfunc import empty_cart
from src.common.commonui import scroll_one_page_until, scroll_n_page
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log

class TrackMWebPageHome(PageH5CommonOperations):

    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        super().__init__(page, header)
        self.bc = browser_context

        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        # 直接进入指定页面
        self.page.goto(TEST_URL + "?joinEnki=true")
        self.page.wait_for_timeout(5000)
        # 关闭首页广告
        self._close_advertisement_in_homepage()

    def switch_store(self):
        log.info("切换store开始")
        self.page.get_by_test_id("wid-modal-store").click()
        self.page.get_by_test_id("wid-store-line-ja").click()
        self.page.wait_for_timeout(2000)
        self._close_advertisement_in_homepage()
        self.page.get_by_test_id("wid-modal-store").click()
        self.page.get_by_test_id("wid-store-line-cn").click()
        self.page.wait_for_timeout(2000)
        self._close_advertisement_in_homepage()
        self.page.get_by_test_id("wid-modal-store").click()
        self.page.get_by_test_id("wid-store-line-ko").click()
        self.page.wait_for_timeout(2000)
        self._close_advertisement_in_homepage()
        self.page.get_by_test_id("wid-modal-store").click()
        self.page.get_by_test_id("wid-store-line-vn").click()
        self.page.wait_for_timeout(2000)
        self._close_advertisement_in_homepage()
        self.page.get_by_test_id("wid-modal-store").click()
        self.page.get_by_test_id("wid-store-line-ph").click()
        self.page.wait_for_timeout(2000)
        self._close_advertisement_in_homepage()
        self.page.get_by_test_id("wid-modal-store").click()
        self.page.get_by_test_id("wid-store-line-th").click()
        self.page.wait_for_timeout(2000)
        self._close_advertisement_in_homepage()
        self.page.get_by_test_id("wid-modal-store").click()
        self.page.get_by_test_id("wid-store-line-us").click()
        self.page.wait_for_timeout(2000)
        self._close_advertisement_in_homepage()
        log.info("切换store结束")

    def click_banner_and_return(self):
        log.info("click banner开始")
        self.page.wait_for_timeout(4000)
        banner = self.page.locator('//div[@data-testid="wid-main-banner-card-0"]//img').all()
        banner[0].click()
        self.page.wait_for_timeout(2000)
        self.page.go_back()
        self.page.wait_for_timeout(2000)
        log.info("click banner结束并返回结束")

    def click_category_and_return(self):
        log.info("click category开始")
        if "tb1" not in TEST_URL:
            self.page.wait_for_timeout(4000)
            self.page.get_by_test_id("wid-categories-item-ice_cream").click()
            self.page.wait_for_timeout(4000)
            self.page.go_back()
            self.page.wait_for_timeout(4000)

        self.page.get_by_test_id("wid-categories-item-global").click()
        self.page.wait_for_timeout(4000)
        self.page.go_back()
        self.page.wait_for_timeout(4000)

        if "tb1" not in TEST_URL:
            self.page.get_by_test_id("wid-categories-item-freshgourmet").click()
            self.page.wait_for_timeout(4000)
            self.page.go_back()
            self.page.wait_for_timeout(2000)
            log.info("click category结束并返回结束")

    def add_product_to_cart(self):
        log.info("add product to cart开始")
        scroll_n_page(self.page, 1)
        self.page.get_by_test_id("btn-atc-plus").all()[0].click()
        log.info("add product to cart结束")


    def scroll_to_bottom(self):
        log.info("scroll to bottom开始")
        scroll_one_page_until(self.page, "//section[@data-testid='wid-home-collection-cm_content_feed']")
        log.info("scroll to bottom结束")


    def _close_advertisement_in_homepage(self):
        try:
            if "tb1" not in TEST_URL:
                self.page.get_by_test_id("btn-close-activity-modal").click(timeout=5000)
        except Exception as e:
            self.page.reload()
            log.info("关闭首页广告发生异常" + str(e))
        # 如果不区分平台，则时间会加长，关闭弹窗需要等待9秒
        try:
            if "tb1" in TEST_URL:
                log.info("tb1关闭首页广告")
                self.page.locator("//img[@alt='Close']").click(timeout=4000)
        except Exception as e:
            self.page.reload()
            log.info("tb1关闭首页广告发生异常" + str(e))


