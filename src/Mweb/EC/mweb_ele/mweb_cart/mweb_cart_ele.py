# -------------------------购物车顶部message-----------------------------------
# 购物车loytal 升级message
ele_cart_message_bar_item = 'wid-cart-message-bar-item'
#购物车add on的message
ele_cart_add_on_entrance = 'wid-add-on-entrance'

# -------------------------购物车顶部元素----------------------------------------
ele_cart_hearder_title='wid-page-nav-header-title'
ele_home_cart = u"//div[aria-label='My cart']"
ele_cart_back_to_home = u"//button[text()='Return home']"
ele_cart_add_to_cart = u"i[data-role='addButtonPlusIcon'][role='button']"
ele_cart_normal_header_title = 'wid-cart-normal-header-title'
ele_cart_seller_header_title = 'wid-cart-seller-header-title'
# pantry购物车标题
ele_cart_pantry_title = 'wid-cart-pantry-title'
# pantry标题后面的i标签
ele_cart_pantry_info_icon = 'wid-cart-pantry-info-icon'
ele_cart_seller_header_title_desc = 'wid-cart-seller-header-desc'
ele_cart_normal_header_title_desc = 'wid-cart-normal-header-desc'
ele_cart_normal_hearder_icon = 'wid-cart-normal-header-icon'
# 购物车上方箭头
ele_cart_normal_expand_tn='wid-cart-normal-expand-btn'


# --------------------------购物车费用元素-------------------------------------------
# service fee
ele_cart_service_fee_title = 'wid-cart-service-fee-title'
ele_cart_service_fee_original_price = 'wid-cart-service-fee-original-price'
# free标签
ele_cart_service_fee_free_price ='wid-cart-service-fee-free-price'
ele_cart_service_fee_price ='wid-cart-service-fee-price'

# cold pack fee
ele_cart_cold_package_fee = 'cold_package_fee'
ele_cart_cold_package_fee_icon = 'wid-cart-service-fee-info-icon'
ele_cart_cold_package_fee_price = 'wid-cart-service-fee-price'


# delivery fee 生鲜购物车跟mo购物车都取这个值
ele_cart_shipping_fee_title = 'wid-cart-shipping-fee-title'
ele_cart_shipping_fee_original_price = 'wid-cart-shipping-fee-original-price'
ele_cart_shipping_fee_price ='wid-cart-shipping-fee-price'
ele_cart_shipping_fee_text = 'wid-cart-shipping-fee'
# free标签
ele_cart_shipping_fee_free_price ='wid-cart-shipping-fee-free-price'


# --------------购物车banner--------------------------------------
# 新人提醒banner
ele_cart_normal_shiping_fee_bannwe = 'wid-cart-section-normal-shipping-fee-banner'
# 购物车活动banner
ele_cart_normal_activity_item = 'wid-cart-section-normal-activity-item'
# 购物车凑单banner
ele_cart_shop_more = 'wid-cart-selection-detail-shop-more-bar'
# 购物车结算按钮元素
ele_cart_checkout_button = 'btn-checkout'

ele_cart_select_all_cart = u"//div[contains(text(), '全选')]/..//div[contains(@class, 'rounded-full')]"
ele_cart_continue = u"//button[text()='继续']"
ele_home_bestsellers = u"//h2[text()='人气热卖']"
ele_checkout_input_your_address = u"//span[text()='请填写您的送货地址及联系方式。']"
ele_checkout_payment = u"#payment i"

# 空购物车
ele_cart_title = u"//div[@data-role='content']//h1"
ele_empty_cart_img = u"//div[@data-role='content']//div[contains(@class,'text-center')]//div[@data-component='CroppedImage']"
ele_empty_cart_text = ele_empty_cart_img + u"//following-sibling::div"
# start_shopping 按钮
ele_empty_cart_start_shopping = ele_empty_cart_img + u"//following-sibling::button"

# 购物车上方的换购模块
ele_cart_trade_in = u"//div[@data-testid='mod-cart-activity-cart_deal']"
# 购物车上方的换购 商品卡片
ele_cart_trade_in_card = ele_cart_trade_in + u"//a[@data-testid='wid-product-card-container']"
# 购物车上方的换购 商品卡片加购按钮
ele_cart_trade_in_card_add = ele_cart_trade_in_card + u"//div[@data-testid='btn-atc-plus']"

# 购物车上方的换购 view more button
ele_cart_trade_in_button = ele_cart_trade_in + u"//button"

# 购物车模块
# 生鲜/pantry/seller购物车
ele_cart_normal = 'mod-cart-normal'
ele_cart_pantry = 'mod-cart-pantry'
ele_cart_seller = 'mod-cart-seller'
# 生鲜购物车切换日期按钮
ele_cart_normal_delivery_date = 'btn-change-delivery-date'
ele_cart_normal_total_quantity = "wid-cart-total-quantity"
# 购物车标题
ele_cart_direct_mail_top_title = u"//*[contains(text(), 'Direct mail')]"
ele_cart_pantry_top_title = u"//*[contains(text(), 'Pantry+')]"
ele_mo_cart_text = u"//*[contains(text(), 'Shipping via FedEx, UPS, etc.')]"

# ele_cart_grocery_top_title = u"//*[contains(text(), 'Local delivery')]"
# ele_grocery_cart_text = u"//*[contains(text(), 'Delivered by Weee! Truck')]"
# ele_grocery_mkpl_text = u"//*[contains(text(), 'Shipping via FedEx, UPS, etc.')]"

# 购物车商品total金额
ele_cart_total_price = "wid-cart-total-price"
# 购物车Shipping fee 金额
ele_cart_shipping_fee =  "wid-cart-shipping-fee-price"
ele_cart_shipping_fee_free =  "wid-cart-shipping-fee-free-price"
# Shipping fee free标签
# ele_cart_shipping_fee_free_price ='wid-cart-shipping-fee-free-pricewid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-descwid-cart-seller-header-desc'

# 获取购物车shipping fee 数据
ele_shipping_fee = u"//div[@data-testid='wid-cart-shipping-fee']"
# 购物车冷链费的文案
ele_cart_cold_pack = 'wid-cart-cold_package_fee'

# 购物车service fee金额
# ele_cart_service_fee_title = "wid-cart-service-fee-title"
# ele_cart_service_fee = "wid-cart-service-fee-price"
# ele_cart_service_fee_original_price = 'wid-cart-service-fee-original-price'
# # service fee free标签
# ele_cart_service_fee_free_price ='wid-cart-service-fee-free-price'
# ele_cart_service_fee_price ='wid-cart-service-fee-price'

# 获取购物车商品总计金额
ele_cart_total = 'wid-cart-total'
# 总金额
sub_total = "wid-cart-subtotal"

# 购物车商品卡片
product_card_container = "wid-product-card-container"
ele_cart_normal_card = ele_cart_normal + u"//div[@data-testid='wid-product-card-container']"
ele_cart_pantry_card = ele_cart_pantry + u"//div[@data-testid='wid-product-card-container']"
ele_cart_seller_card = ele_cart_seller + u"//div[@data-testid='wid-product-card-container']"
# 购物车商品标题
ele_cart_product_card_title = 'wid-product-card-title'

# 购物车商品价格/划线价
ele_cart_normal_price ="wid-cart-section-normal-goods-price"
ele_cart_pantry_price = "wid-cart-section-pantry-goods-price"
ele_cart_seller_price = "wid-cart-section-seller-goods-price"
ele_cart_normal_base_price ="wid-cart-section-normal-goods-base-price"
ele_cart_pantry_base_price = "wid-cart-section-pantry-goods-base-price"
ele_cart_seller_base_price = "wid-cart-section-seller-goods-base-price"



# ===========================================save_for_later 模块========================================
# save_for_later 模块
ele_save_for_later = "wid-cart-save-for-later"
# save for later 按钮
ele_cart_normal_s4l = "wid-cart-section-normal-goods-save-for-later-btn"
ele_cart_pantry_s4l = "wid-cart-section-pantry-goods-save-for-later-btn"
ele_cart_seller_s4l = "wid-cart-section-seller-goods-save-for-later-btn"
ele_cart_seller_s4l_remove = "wid-cart-section-save_for_later-goods-remove-btn"
ele_cart_seller_s4l_move_to_cart = "wid-cart-section-save_for_later-goods-remove-btn"
ele_cart_seller_s4l_load_more = "wid-cart-save-for-later-load-more"

# remove 按钮
ele_cart_normal_remove = "wid-cart-section-normal-goods-remove-btn"
ele_cart_pantry_remove = "wid-cart-section-pantry-goods-remove-btn"
ele_cart_seller_remove = "wid-cart-section-seller-goods-remove-btn"

# 购物车加减元素
btn_atc_minus = "btn-atc-minus"
btn_atc_plus = "btn-atc-plus"
ele_cart_atc_normal_minus = ele_cart_normal_card + u"//div[@data-testid='btn-atc-minus']"
ele_cart_atc_pantry_minus = ele_cart_pantry_card + u"//div[@data-testid='btn-atc-minus']"
ele_cart_atc_seller_minus = ele_cart_seller_card + u"//div[@data-testid='btn-atc-minus']"
ele_cart_atc_normal_plus = ele_cart_normal_card+ u"//div[@data-testid='btn-atc-plus']"
ele_cart_atc_pantry_plus = ele_cart_pantry_card + u"//div[@data-testid='btn-atc-plus']"
ele_cart_atc_seller_plus = ele_cart_seller_card + u"//div[@data-testid='btn-atc-plus']"

# 购物车点击商品数量
ele_cart_quality_click = 'wid-cart-product-quantity'





# ===========================为你推荐模块===========================
# 为你推荐tab
ele_recommend_tab = u"//div[@id='recommendTabFixed']//div[1]"
# 再次购买tab
ele_buy_again_tab = u"//div[@id='recommendTabFixed']//div[2]"
# 为你推荐模块
ele_recommend_module = u"//div[@data-testid='mod-cart-Recommendations']"

# 为你推荐模块
ele_buy_again_module = u"//div[@data-testid='mod-cart-Buy Again']"
# 为你推荐模块商品卡片
ele_recommend_module_card = ele_recommend_module + u"//a[@data-testid='wid-product-card-container']"
# 为你推荐模块商品卡片
ele_buy_again_module_card = ele_buy_again_module + u"//a[@data-testid='wid-product-card-container']"

# 获取enki模式下生鲜地区切换zipcode的组件
ele_click_grocery_zipcode = u"//span[@class='flex gap-x-[4px]']"
# 获取enki模式下MO地区切换zipcode的组件
ele_click_mo_zipcode = u"//span[@class='flex gap-400 items-center'][1]"
# 获取首页是99348的文案
ele_zipcode_99348 = u"//span[contains(text(), '99348')]"
# 获取首页是98011的文案
ele_zipcode_98011 = u"//span[contains(text(), '98011')]"




# =================================================购物车中间页元素================================================
# 中间页关闭按钮
ele_cart_middle_close = 'btn-modal-close'
# 中间页标题
ele_cart_middle_title = u"//h2[text()='Select carts for checkout']"
ele_popup_header = 'wid-popup-header'
# 全选购物车复选框
ele_cart_select_all = 'btn-select-all-carts'
# 全选购物车文本
ele_cart_select_all_text = ele_cart_select_all + u"//div[contains(@class, 'enki-body-base-medium')]"
# 各类型购物车复选框
ele_cart_select_normal = 'btn-select-cart-normal'
ele_cart_select_pantry = 'btn-select-cart-pantry'
ele_cart_select_seller ='btn-select-cart-seller'
# 购物车卡片 ID
ele_cart_normal_id = u"//div[@id='cart-normal']"
ele_cart_pantry_id = u"//div[@id='cart-pantry']"
ele_cart_seller_id = u"//div[starts-with(@id, 'cart-seller_')]"
# 底部提示文本
ele_cart_middle_tip = 'wid-select-cart-multiple-shipping-fee-tip'
# 底部结算按钮
# ele_cart_middle_checkout = u"(//button[@data-testid='btn-checkout'])[2]"
ele_cart_middle_checkout = 'btn-select-cart-checkout'

# 底部小计金额
ele_cart_middle_subtotal = 'wid-select-cart-subtotal'
# 未选择提示
ele_cart_no_selection_tip = u"//div[contains(text(), 'No cart selected')]"
# 购物车列表容器
ele_cart_list_container = u"//div[@id='cartsList']"
