
ele_home_cart = u"//div[aria-label='My cart']"
ele_cart_back_to_home = u"//button[text()='Return home']"
ele_cart_add_to_cart = u"i[data-role='addButtonPlusIcon'][role='button']"
# 购物车结算按钮元素
ele_cart_checkout_button = 'btn-checkout'

ele_cart_select_all_cart = u"//div[contains(text(), '全选')]/..//div[contains(@class, 'rounded-full')]"
ele_cart_continue = u"//button[text()='继续']"
ele_home_bestsellers = u"//h2[text()='人气热卖']"
ele_checkout_input_your_address = u"//span[text()='请填写您的送货地址及联系方式。']"
ele_checkout_payment = u"#payment i"
# 空购物车
ele_cart_title = u"//div[@data-role='content']//h1"
ele_empty_cart_img = u"//div[@data-role='content']//div[contains(@class,'text-center')]//div[@data-component='CroppedImage']"
ele_empty_cart_text = ele_empty_cart_img + u"//following-sibling::div"
# start_shopping 按钮
ele_empty_cart_start_shopping = ele_empty_cart_img + u"//following-sibling::button"

# 购物车上方的换购模块
ele_cart_trade_in = u"//div[@data-testid='mod-cart-activity-cart_deal']"
# 购物车上方的换购 商品卡片
ele_cart_trade_in_card = ele_cart_trade_in + u"//a[@data-testid='wid-product-card-container']"
# 购物车上方的换购 商品卡片加购按钮
ele_cart_trade_in_card_add = ele_cart_trade_in_card + u"//div[@data-testid='btn-atc-plus']"

# 购物车上方的换购 view more button
ele_cart_trade_in_button = ele_cart_trade_in + u"//button"

# 购物车模块
# 生鲜/pantry/seller购物车
ele_cart_normal = u"//div[@data-testid='mod-cart-normal']"
ele_cart_pantry = u"//div[@data-testid='mod-cart-pantry']"
ele_cart_seller = u"//div[@data-testid='mod-cart-seller']"
# 生鲜购物车切换日期按钮
ele_cart_normal_delivery_date = ele_cart_normal + u"//button[@data-testid='btn-change-delivery-date']"
# 购物车total金额
ele_cart_normal_total = ele_cart_normal + u"//div[@data-testid='wid-cart-total']"
ele_cart_pantry_total = ele_cart_pantry + u"//div[@data-testid='wid-cart-total']"
ele_cart_seller_total = ele_cart_seller + u"//div[@data-testid='wid-cart-total']"
# 购物车delivery fee金额
ele_cart_normal_delivery_fee = ele_cart_normal + u"//div[@data-testid='wid-cart-shipping-fee']"
ele_cart_pantry_delivery_fee = ele_cart_pantry + u"//div[@data-testid='wid-cart-shipping-fee']"
ele_cart_seller_delivery_fee = ele_cart_seller + u"//div[@data-testid='wid-cart-shipping-fee']"

# 购物车service fee金额
ele_cart_normal_service_fee = ele_cart_normal + u"//div[@data-testid='wid-cart-delivery_service_fee']"
ele_cart_pantry_service_fee = ele_cart_pantry + u"//div[@data-testid='wid-cart-delivery_service_fee']"
ele_cart_seller_service_fee = ele_cart_seller + u"//div[@data-testid='wid-cart-delivery_service_fee']"
ele_save_for_later = u"//div[@data-testid='wid-cart-save-for-later']"
# 购物车商品卡片
ele_cart_normal_card = ele_cart_normal + u"//div[@data-testid='wid-product-card-container']"
ele_cart_pantry_card = ele_cart_pantry + u"//div[@data-testid='wid-product-card-container']"
ele_cart_seller_card = ele_cart_seller + u"//div[@data-testid='wid-product-card-container']"
ele_cart_s4l_card = ele_save_for_later + u"//div[@data-testid='wid-product-card-container']"


# save for later 按钮
ele_cart_normal_s4l = ele_cart_normal_card + u"//div[@data-testid='btn-save-for-later']"
ele_cart_pantry_s4l = ele_cart_pantry_card + u"//div[@data-testid='btn-save-for-later']"
ele_cart_seller_s4l = ele_cart_seller_card + u"//div[@data-testid='btn-save-for-later']"

# remove 按钮
ele_cart_normal_remove = ele_cart_normal_card + u"//div[@data-testid='btn-remove-product']"
ele_cart_pantry_remove = ele_cart_pantry_card + u"//div[@data-testid='btn-remove-product']"
ele_cart_seller_remove = ele_cart_seller_card + u"//div[@data-testid='btn-remove-product']"

# 购物车加减元素
ele_cart_atc_normal_minus = ele_cart_normal_card + u"//div[@data-testid='btn-atc-minus']"
ele_cart_atc_pantry_minus = ele_cart_pantry_card + u"//div[@data-testid='btn-atc-minus']"
ele_cart_atc_seller_minus = ele_cart_seller_card + u"//div[@data-testid='btn-atc-minus']"
ele_cart_atc_normal_plus = ele_cart_normal_card+ u"//div[@data-testid='btn-atc-plus']"
ele_cart_atc_pantry_plus = ele_cart_pantry_card + u"//div[@data-testid='btn-atc-plus']"
ele_cart_atc_seller_plus = ele_cart_seller_card + u"//div[@data-testid='btn-atc-plus']"

# 购物车商品标题
ele_cart_normal_title = ele_cart_normal_card + u"//div[@data-testid='wid-product-card-title']"
ele_cart_pantry_title = ele_cart_pantry_card + u"//div[@data-testid='wid-product-card-title']"
ele_cart_seller_title = ele_cart_seller_card + u"//div[@data-testid='wid-product-card-title']"

# 购物车点击商品数量
ele_cart_quality_click = u"//div[@class='absolute bottom-0 right-0']"

# 购物车标题
ele_cart_direct_mail_top_title = u"//*[contains(text(), 'Direct mail')]"
ele_cart_pantry_top_title = u"//*[contains(text(), 'Pantry+')]"
ele_mo_cart_text = u"//*[contains(text(), 'Shipping via FedEx, UPS, etc.')]"

ele_cart_grocery_top_title = u"//*[contains(text(), 'Local delivery')]"
ele_grocery_cart_text = u"//*[contains(text(), 'Delivered by Weee! Truck')]"
ele_grocery_mkpl_text = u"//*[contains(text(), 'Shipping via FedEx, UPS, etc.')]"

# 购物车商品价格
ele_cart_normal_price =ele_cart_normal_card + u"//div[@data-testid='wid-product-card-price']"
ele_cart_pantry_price = ele_cart_pantry_card + u"//div[@data-testid='wid-product-card-price']"
ele_cart_seller_price = ele_cart_seller_card + u"//div[@data-testid='wid-product-card-price']"

# 为你推荐模块
# 为你推荐tab
ele_recommend_tab = u"//div[@id='recommendTabFixed']//div[1]"
# 再次购买tab
ele_buy_again_tab = u"//div[@id='recommendTabFixed']//div[2]"
# 为你推荐模块
ele_recommend_module = u"//div[@data-testid='mod-cart-Recommendations']"
# 为你推荐模块
ele_buy_again_module = u"//div[@data-testid='mod-cart-Buy Again']"
# 为你推荐模块商品卡片
ele_recommend_module_card = ele_recommend_module + u"//a[@data-testid='wid-product-card-container']"
# 为你推荐模块商品卡片
ele_buy_again_module_card = ele_buy_again_module + u"//a[@data-testid='wid-product-card-container']"

# 获取enki模式下生鲜地区切换zipcode的组件
ele_click_grocery_zipcode = u"//span[@class='flex gap-x-[4px]']"
# 获取enki模式下MO地区切换zipcode的组件
ele_click_mo_zipcode = u"//span[@class='flex gap-400 items-center'][1]"
# 获取首页是99348的文案
ele_zipcode_99348 = u"//span[contains(text(), '99348')]"
# 获取首页是98011的文案
ele_zipcode_98011 = u"//span[contains(text(), '98011')]"

# 获取购物车商品金额
ele_items_total = u"//div[@data-testid='wid-cart-total']"
# 获取购物车shipping fee 数据
ele_shipping_fee = u"//div[@data-testid='wid-cart-shipping-fee']"


# 购物车中间页元素
# 中间页关闭按钮
ele_cart_middle_close = 'btn-modal-close'
# 中间页标题
ele_cart_middle_title = u"//h2[text()='Select carts for checkout']"
# 全选购物车复选框
ele_cart_select_all = 'btn-select-all-carts'
# 全选购物车文本
ele_cart_select_all_text = ele_cart_select_all + u"//div[contains(@class, 'enki-body-base-medium')]"
# 各类型购物车复选框
ele_cart_select_normal = 'btn-select-cart-normal'
ele_cart_select_pantry = 'btn-select-cart-pantry'
ele_cart_select_seller ='btn-select-cart-seller'
# 购物车卡片 ID
ele_cart_normal_id = u"//div[@id='cart-normal']"
ele_cart_pantry_id = u"//div[@id='cart-pantry']"
ele_cart_seller_id = u"//div[starts-with(@id, 'cart-seller_')]"
# 底部提示文本
ele_cart_middle_tip = u"//div[contains(text(), 'You can select multiple carts for checkout')]"
# 底部结算按钮
# ele_cart_middle_checkout = u"(//button[@data-testid='btn-checkout'])[2]"
ele_cart_middle_checkout = 'btn-select-cart-checkout'

# 底部小计金额
ele_cart_middle_subtotal = u"(//div[contains(text(), 'Subtotal')])[2]"
# 未选择提示
ele_cart_no_selection_tip = u"//div[contains(text(), 'No cart selected')]"
# 购物车列表容器
ele_cart_list_container = u"//div[@id='cartsList']"
# 购物车冷链费的文案
ele_h5_cart_cold_pack = 'wid-cart-cold_package_fee'