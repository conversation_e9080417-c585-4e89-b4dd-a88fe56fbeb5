"""
换购模块相关元素定位
"""
# PDP页面底部提示
ele_pdp_unlock_tip = u"//div[@data-testid='pdp-unlock-tip']"
# 换购进度条
ele_trade_in_progress = u"//div[@data-testid='trade-in-progress']"
# 换购商品已选状态
ele_trade_in_selected = u"//button[@data-testid='btn-selected']"
# 购物车换购商品标签
ele_cart_trade_in_tag = u"//div[@data-testid='tag-deal']"
# 换购商品删除按钮
ele_cart_trade_in_delete = u"//button[@data-testid='btn-delete']"
# 换购商品数量限制弹窗
ele_trade_in_limit_popup = u"//div[@data-testid='popup-limit']"

# 换购页面模块
ele_trade_in = u"//div[@id='trade_in']"
# 换购页面banner模块
ele_trade_in_banner = ele_trade_in + u"//div[@id='disaccountWrap']"
# Enjoy discounts only if you add items from this list to your cart.
ele_trade_in_tip = "txt-trade-in-discount-tip"
ele_trade_in_txt = "txt-trade-in-max-off-price"
# 换购页面商品卡片
ele_trade_in_card = "wid-product-card-container"
ele_trade_in_card_image ="wid-product-card-product-image"
ele_trade_in_card_tag_off = "wid-product-card-tag-off"
ele_trade_in_card_tags = "wid-product-card-tags"
ele_trade_in_card_brand = "wid-product-card-brand"

# 换购页面商品卡片标题
ele_trade_in_card_title = "wid-product-card-title"
# 换购页面商品卡片价格
ele_trade_in_card_price = "wid-product-card-price"
ele_trade_in_card_price_value = "wid-product-card-price-value"
# 换购页面商品卡片价格
ele_trade_in_card_base_price = "wid-product-card-base-price"
ele_trade_in_select = "btn-trade-in-add-to-cart"
# 换购页面商品卡片加购按钮禁用状态
ele_trade_in_card_add_disabled = "btn-trade-in-non-selectable"
# 换购页小于35 的toast
ele_trade_in_toast = "txt-trade-in-diff-price"
# 换购页shop more 按钮
ele_trade_in_shop_more = "btn-trade-in-shop-more"
# 返回购物车按钮
ele_go_to_cart = "btn-go-to-cart"

# 凑单页面模块 -- 元素需要补充datatestid

# 换购页面元素
ele_trade_in_select_btn = ele_trade_in_card + u"//button[@data-testid='btn-select']"
ele_trade_in_select_btn_disabled = ele_trade_in_select_btn + u"[@disabled]"
ele_trade_in_remaining_amount = u"//div[@data-testid='remaining-amount-text']"

"""换购页面元素定位"""
# 换购页面模块
ele_trade_in = u"//div[@id='trade_in']"
# 换购页面banner模块
# 换购页面商品卡片
# 换购页面商品卡片
# 换购页面商品卡片
# 换购页shop more 按钮

# 换购页面select按钮
ele_select_btn = "//button[contains(@class, 'trade-in-select')]"
# 添加更多提示
ele_add_more_tip = "//div[contains(text(), 'Add ${} more to cart')]"
# 解锁折扣提示
ele_unlock_discount_pop = "//div[contains(text(), 'Unlock your special discount')]"
# 商品详情页解锁提示
# 进度条
ele_progress_bar = "//div[contains(@class, 'progress-bar')]"
# 选择限制提示
ele_limit_pop = "//div[contains(text(), 'selection is limited to 5 items')]"
