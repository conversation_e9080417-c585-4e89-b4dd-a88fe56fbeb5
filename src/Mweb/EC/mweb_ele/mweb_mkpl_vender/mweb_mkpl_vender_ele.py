
# ================================
# 商家关注按钮选择器
# ================================

# 关注按钮 - 基于data-testid（最精确）
ele_follow_seller_btn = "btn-follow-seller"

# ================================
# Email输入框选择器
# ================================

# Email输入框 - 基于id（最精确）
ele_email_input = u"//input[@id='email']"

# Email输入框 - 基于type属性
ele_email_input_type = u"//input[@type='email']"

# Email输入框 - 基于autocomplete属性
ele_email_input_autocomplete = u"//input[@autocomplete='email']"

# ================================
# 下一步按钮选择器
# ================================

# 下一步按钮 - 基于文本内容（最精确）
ele_next_step_btn = u"//button[text()='下一步']"


# 下一步按钮 - 基于主要按钮样式
ele_next_step_btn_primary = u"//button[contains(@class, 'bg-btn-primary-bg') and contains(@class, 'text-btn-primary-fg-default') and text()='下一步']"
# ================================
# 密码输入框选择器
# ================================

# 密码输入框 - 基于id（最精确）
ele_password_input = u"//input[@id='password']"

# 密码输入框 - 基于type属性
ele_password_input_type = u"//input[@type='password']"

# 密码输入框 - 基于autocomplete属性
ele_password_input_autocomplete = u"//input[@autocomplete='password']"

# 密码输入框 - 基于CSS类组合
ele_password_input_css = u"//input[contains(@class, 'w-full') and contains(@class, 'h-full') and @type='password']"

# 密码输入框 - 组合选择器（最可靠）
ele_password_input_combined = u"//input[@type='password' and @id='password' and @autocomplete='password']"
# ================================
# 确定按钮选择器
# ================================

# 确定按钮 - 基于data-testid（最精确）
ele_confirm_btn = "btn-unfollow-seller"


# ================================
# 取消按钮选择器
# ================================

# 取消按钮 - 基于data-testid（最精确）
ele_cancel_btn = "btn-cancel-unfollow-seller"


# 取消关注确认弹窗选择器
# ================================

# 取消关注弹窗 - 基于data-testid（最精确）
ele_unfollow_modal = "mod-unfollow-confirm-modal"

# 弹窗关闭按钮 - 基于data-testid
ele_close_unfollow_popup_btn = "btn-colose-unfollow-popup"


# ================================
# 弹窗容器选择器（移动端）
# ================================

# 弹窗容器 - 基于data-testid（最精确）
ele_unfollow_modal_container = "mod-unfollow-confirm-modal"

# 弹窗关闭按钮 - 基于data-testid
ele_unfollow_close_btn = "btn-colose-unfollow-popup"

# 弹窗确定按钮 - 基于data-testid
ele_unfollow_confirm_btn = "btn-unfollow-seller"

# 弹窗取消按钮 - 基于data-testid
ele_unfollow_cancel_btn = "btn-cancel-unfollow-seller"

# ================================
# 商家标题选择器
# ================================

# 商家标题 - 基于data-testid（最精确）
ele_seller_title = "wid-seller-title"

ele_seller_logo = "wid-seller-logo"

# ================================
# 商家标签页选择器
# ================================

# 标签页容器 - 基于data-testid（最精确）
ele_seller_tabs_container = "wid-seller-tabs-container"

# 探索标签页 - 基于data-testid（最精确）
ele_seller_tab_explore = u"//div[@data-testid='wid-seller-tab-explore']"


# 全部商品标签页 - 基于data-testid（最精确）
ele_seller_tab_all = "wid-seller-tab-all"

# 全部商品标签页 - 备用选择器
ele_seller_tab_all_backup = u"//div[contains(@data-testid, 'wid-seller-tab-all')]"

# 晒单标签页 - 基于data-testid（最精确）
ele_seller_tab_reviews = u"//div[@data-testid='&quot;wid-seller-tab-reviews']"


# 一键置顶按钮定义：
ele_seller_back_to_top = "btn-back-to-top"


# ================================
# 搜索按钮选择器
# ================================

# 搜索按钮 - 基于data-testid（最精确）
ele_search_btn = "btn-open-search"

# 商家星级评价：
ele_seller_rating = "wid-seller-rating"

# 商家ETA 信息：
ele_seller_estimate_range = "wid-seller-estimate-range"

# 商家发货及运费信息
ele_seller_reminder_shipping = "wid-seller-reminder-shipping"

# 商家message 入口
ele_send_message_to_seller = "btn-send-message-to-seller"

# 分享商家message 入口
ele_share_seller_info = "btn-share-seller-info"

# ================================
# 访问商家详情按钮选择器
# ================================

# 访问商家详情按钮 - 基于data-testid
ele_visit_seller_detail = "btn-visit-seller-detail"

# 商家详情-评价tab
ele_seller_detail_feedback = "wid-seller-tab-feedback"

# 商家详情-运输tab
ele_seller_shipping_return = "wid-seller-tab-shipping_return"

# 商家详情-关于tab
ele_seller_about_tab = "wid-seller-tab-about"

# 商家详情-整体评分tab
ele_seller_rating_info = "wid-feedback-statistics-overall_rating"

#商家详情-创建好友拼单入口：
ele_create_group_order = "btn-create-group-order"

#商家详情-创建好友拼单弹窗：
ele_group_order_pop_up = "mod-seller-group-order-popup"

#商家详情-创建好友拼单弹窗-关闭按钮：
ele_group_order_pop_up_close = "btn-close-group-order-popup"

#商家详情-拼单弹窗标题：
ele_group_order_pop_up_title = "wid-group-order-title"

# 商家详情-拼单弹窗标题：
ele_group_order_pop_up_subtitle = "wid-group-order-sub-title"

#商家详情-拼单弹窗描述：
ele_group_order_pop_up_desc = "wid-group-order-desc"

#商家详情-拼单弹窗邀请好友按钮：
ele_group_order_pop_up_confirm = "btn-invite-friends"

# 拼单详情页面，分享弹窗：
ele_group_order_share_pop_up ="mod-share-popup"

# 拼单详情页面，分享弹窗关闭：
ele_group_order_share_close = "btn-modal-close"

# 拼单详情页面，分享弹窗图片：
ele_group_order_share_image = "wid-share-image"

# 拼单详情页面，分享弹窗标题：
ele_group_order_share_title = "wid-share-title"

# 拼单详情页面，分享弹窗描述：
ele_group_order_share_desc = "wid-share-desc"

# 拼单详情页面，分享方式：
ele_group_order_share_copy_link = "btn-share-method-copyLink"

# 拼单详情页面，删除按钮：
ele_group_order_delete_btn = "btn-delete-group-order"

# 拼单取消确认弹窗：
ele_group_order_cancel_popup = "mod-cancel-group-order-popup"

# 拼单取消确认弹窗确认按钮：
ele_group_order_cancel_popup_cancel_confirm = "btn-confirm-cancel-group-order"

# 拼单详情页面，点击删除：
ele_group_order_cancel_button = "btn-cancel-group-order"

# 拼单详情页面，取消订单弹窗：
ele_group_order_cancel_popup = "mod-group-order-cancel-modal"

# 拼单详情页面，取消订单弹窗-关闭按钮：
ele_group_order_cancel_popup_cancel_confirm = "btn-cancel-action"

# 拼单详情页面，取消订单弹窗-取消按钮：
ele_group_order_cancel_popup_cancel = "btn-cancel-group-order"

# 拼单详情页面，取消订单弹窗-X按钮：
ele_group_order_cancel_popup_close = "btn-modal-close"

# 商家商品卡片元素
ele_seller_product_card = "wid-product-card-container"
ele_seller_product_atc = "btn-atc-plus"
ele_seller_product_minus = "btn-atc-minus"

#商家悬浮购物车元素
ele_float_cart_container = "mod-seller-floating-cart-container"
ele_view_seller_float_cart = "btn-visit-seller-cart"
ele_group_order_float_cart_eta = "wid-seller-shipping-date"
ele_group_order_float_cart_user_info = "wid-group-order-user-info"
ele_group_order_float_cart_recommend = "mod-seller-cart-recommend-0"
ele_group_order_float_cart_tips = "wid-seller-discount-tips"
ele_group_order_float_cart_add_more = "btn-add-more"
ele_group_order_float_cart_checkout ="btn-go-checkout"
ele_group_order_float_cart_price ="wid-seller-cart-price"
ele_group_order_float_cart_shipping ="wid-seller-cart-shipping-info"
ele_group_order_checkout_back = "btn-back"
ele_group_order_btn_close = "btn-close-seller-cart"

#离开拼单页面
ele_group_order_btn_modal_close = "btn-close"
ele_group_order_modal_close_popup = "mod-group-order-leave-modal"
ele_group_order_modal_close_button = "btn-modal-close"
ele_group_order_modal_close_title = "wid-group-order-leave-title"
ele_group_order_modal_close_desc = "wid-group-order-leave-desc"
ele_group_order_modal_close_cancel = "btn-leave-group-order"
ele_group_order_modal_close_confirm = "btn-cancel-action"

#从拼单页面回到seller主页：
ele_group_order_seller_homepage_banner = "mod-group-order-bar"
ele_group_order_homepage_banner_icon = "wid-group-order-bar-icon"
ele_group_order_homepage_banner_title = "wid-group-order-bar-title"
ele_group_order_homepage_banner_subtitle = "wid-group-order-bar-subtitle"
ele_group_order_homepage_banner_label = "wid-group-order-points-back-label"
ele_group_order_homepage_banner_visit_icon = "btn-visit-group-order-detail"

# 支付分享相关元素
btn_share = "btn-share"
order_payment_share_pop_up = "wid-popup-share-popup"
order_payment_share_image = "wid-share-image"
order_payment_share_title = "wid-share-title"
order_payment_share_desc = "wid-share-desc"
order_payment_share_zh_lang ="btn-share-lang-zh"
order_payment_share_zht_lang ="btn-share-lang-zh-Hant"
order_payment_share_en_lang ="btn-share-lang-en"
order_payment_share_ko_lang ="btn-share-lang-ko"
order_payment_share_ja_lang ="btn-share-lang-ja"
order_payment_share_vi_lang ="btn-share-lang-vi"
order_payment_share_copy_link = "btn-share-method-copyLink"











