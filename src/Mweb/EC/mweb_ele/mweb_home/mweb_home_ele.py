GITele_deals = u"//span[normalize-space(text())='Deals']"
ele_h5_home_zipcode = u"#layout-header i[class*='iconlocation-filled']"
ele_please_input_zipcode = u"input[placeholder]"
ele_add_new_address_div = u"//button[text()='Add new address']"
ele_input_address = u"input[placeholder='Street Address']"
ele_1st_matched_address = u"#streetAddressList span"

ele_fullname = u"input[placeholder='First Name']"
ele_family_name = u"input[placeholder='Last Name']"
ele_phone_number = u"input[placeholder='Phone Number']"
ele_note = u"textarea[placeholder]"
ele_save_address = u"button[type='submit']"

ele_hello_world_address = "//strong[text()='hello world']/../..//i[contains(@class, 'text-surface')]"
ele_delete_address_button = "i[class*='iconDelete']"
ele_confirm_delete_button = "//button[text()='Remove']"

# each collection on home page
ele_h5_home_editors_pick = u"""//h2[text()="Editor's Pick"]"""
ele_h5_home_editors_pick_add_to_cart = u"""//h2[text()="Editor's Pick"]/../..//i[@data-role='addButtonPlusIcon']"""
ele_h5_home_everyday_deals = u"//h2[text()='Everyday deals']"
ele_h5_home_everyday_deals_add_to_cart = ele_h5_home_everyday_deals + u"/../..//i[@data-role='addButtonPlusIcon']"
ele_h5_home_fresh_daily = u"//div[text()='Fresh Daily']"
ele_h5_home_fresh_daily_add_to_cart = ele_h5_home_fresh_daily + u"/../../..//i[@data-role='addButtonPlusIcon']"
ele_h5_home_recommendations = u"//h2[text()='Recommendations']"
ele_h5_home_recommendations_add_to_cart = ele_h5_home_recommendations + u"/../..//i[@data-role='addButtonPlusIcon']"
ele_h5_home_navigation_global = "//div[@id='navigationGlobal']"

# 查看更多按钮：
mweb_see_all ="//div[@data-testid='btn-more-link']"
# 加购按钮
add_to_cart = "//div[@data-testid='btn-atc-plus']"
# edlp标签展示元素定位
ele_h5_everyday_value = "//span[text()='Everyday value']"

#new arrival 组件展示
ele_h5_home_new_arrivals = "//section[@data-testid='mod-item-carousel-New-Arrivals']"
ele_h5_home_new_arrivals_prod = ele_h5_home_new_arrivals + "//a[@data-testid='wid-product-card-container']"
ele_h5_home_new_arrivals_add_to_cart = ele_h5_home_new_arrivals_prod + add_to_cart
ele_h5_home_new_arrivals_see_all = ele_h5_home_new_arrivals + mweb_see_all

# bestseller组件展示：
ele_h5_home_best_sellers = "//section[@data-testid='mod-item-carousel-Bestsellers']"
ele_h5_home_best_sellers_prod = ele_h5_home_best_sellers + "//a[@data-testid='wid-product-card-container']"
ele_h5_home_best_sellers_add_to_cart = ele_h5_home_best_sellers + add_to_cart
ele_h5_home_best_sellers_see_all = ele_h5_home_best_sellers + mweb_see_all


#侧边栏入口：
ele_h5_home_menu_side_bar = "btn-hamburger-menu"
ele_h5_home_menu_global_entrance = "wid-popup-hamburger-menu-mkpl-waterfall-item"



