"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_checkout_ele.py
@Description    :  结算页面元素定位
@CreateTime     :  2025/5/16 15:04
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/5/16 15:30
"""

# 页面标题
ele_checkout_title = u"//div[text()='Checkout']"

# 返回按钮
ele_back_button = 'btn-back'

# 页面头部
ele_header = u"//div[@id='layout-header']"
ele_header_title = u"//div[@data-role='middle']//div[contains(@class, 'text-primary-1')]"
# 顶部会员等级模块
ele_rewards_header = 'wid-checkout-rewards-header'

# 配送信息模块
ele_delivery_info_section = 'mod-checkout-delivery-info'
ele_delivery_info_title = 'mod-checkout-delivery-info-title'
ele_delivery_address = 'wid-checkout-delivery-info-address'
ele_delivery_address_name = 'wid-checkout-delivery-info-name'
ele_delivery_address_phone = 'wid-checkout-delivery-info-phone'
ele_delivery_address_comment = 'wid-checkout-delivery-info-comment'


# 支付方式模块
ele_payment_method_section = 'mod-checkout-payment'
ele_payment_method_title = 'mod-checkout-payment-title'
ele_payment_method_card = u"//div[@id='payment']//div[contains(@class, 'p-5 border')]"
ele_payment_card_number = u"//div[@id='payment']//div[contains(@class, 'enki-body-sm-strong')]"
# ele_payment_method_point=''  -- 还未提供


# review order
ele_review_order = 'mod-checkout-review-order'
ele_review_order_title = 'mod-checkout-review-order-title'
# 小汽车icon
ele_review_order_shipping_icom = 'wid-review-order-card-normal-shipping-icon'
# 生鲜购物车的标题
ele_review_order_shipping_type_desc = 'wid-review-order-card-normal-shipping-type-desc'
# 生鲜购物车的配送描述
ele_review_order_shipping_desc = 'wid-review-order-card-normal-shipping-desc'
# 生鲜购物车的配送时间
ele_review_order_shipping_date= 'wid-review-order-card-normal-shipping-date'
# 生鲜购物车商品数量
ele_review_order_shipping_total = 'wid-review-order-card-normal-item-total'
# 生鲜购物车商品总金额
ele_review_order_shipping_totap_price = 'wid-review-order-card-normal-item-total-price'
# 生鲜购物车的配送费
ele_wid_review_order_card_normal_shipping_fee ='wid-review-order-card-normal-shipping-fee'
# 商品图片
ele_review_order_product_image = 'wid-review-order-card-normal-product-image'
# 商品图片下面的配送文案
ele_review_order_delivery_window_content = 'wid-review-order-card-normal-delivery-window-content'



# 优惠券模块
ele_coupon_section = u"//div[@id='coupon']"
ele_coupon_title = u"//div[@id='coupon']//div[contains(@class, 'enki-body-base-medium')]"
ele_coupon_select_box = u"//div[@id='coupon']//div[contains(@class, 'p-5 border')]"
ele_coupon_value = u"//div[@id='coupon']//div[contains(@class, 'enki-body-sm-strong')]"

# 订单摘要模块
ele_order_summary_section = u"//div[@id='summary']"
ele_order_purchase_summary_title = 'wid-checkout-right-purchase-summary-title'
ele_order_purchase_summary_item_subtotal_title = 'wid-checkout-right-purchase-summary-item-subtotal-title'
ele_order_purchase_summary_item_taxes_title = 'wid-checkout-right-purchase-summary-item-taxes-title'
ele_order_purchase_summary_item_service_fee_title = 'wid-checkout-right-purchase-summary-item-service_fee-title'
# service fee的i标签
ele_order_purchase_summary_item_service_fee_icon='wid-checkout-right-purchase-summary-item-service-icon'
ele_order_purchase_summary_item_delivery_fee_title='wid-checkout-right-purchase-summary-item-delivery_fee-title'
# delivery fee的i标签
ele_order_purchase_summary_item_delivery_fee_icon='wid-checkout-right-purchase-summary-item-service-icon'
ele_order_purchase_summary_item_tip_title = 'wid-checkout-right-purchase-summary-item-tip-title'
ele_order_purchase_summary_item_total_title='wid-checkout-right-purchase-summary-item-total-title'
ele_order_purchase_summary_item_weee_points_deduction_title='wid-checkout-right-purchase-summary-item-weee!_points_deduction-title'
ele_order_purchase_summary_right_tip_option = 'wid-checkout-right-tip-option-4'

# 结算按钮
ele_place_order_button = 'wid-checkout-btn'
ele_place_order_button_text = u"//button[@data-testid='btn-place-order']/span"

# 底部提示信息
ele_checkout_place_order_terms = 'wid-checkout-right-checkout-place-order-terms'

# 错误提示
ele_error_message = u"//div[contains(@class, 'text-error-500')]"

# 加载状态
ele_loading_indicator = u"//div[contains(@class, 'loading')]"

# 支付方式选择
ele_payment_method_selector = 'payment-method-selector'
ele_credit_card_option = 'payment-method-credit-card'
ele_paypal_option = 'payment-method-paypal'

# 配送时间选择
ele_delivery_time_section ='delivery-time-selector'
ele_delivery_time_option = 'delivery-time-option'

# 商品列表
ele_product_list ='checkout-product-list'
ele_product_item = 'checkout-product-item'
ele_product_name = 'checkout-product-name'
ele_product_price = 'checkout-product-price'
ele_product_quantity = 'checkout-product-quantity'

# 提示横幅
ele_promo_banner = u"//div[contains(@style, 'background: rgb(253, 255, 164)')]"
ele_promo_banner_text = u"//div[contains(@style, 'background: rgb(253, 255, 164)')]//span"
