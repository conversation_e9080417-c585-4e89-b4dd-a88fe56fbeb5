# Marketplace banner array item 3
from src.common.commonui import close_popup_on_home

ele_mkpl_banner_arrays_1 = u"//a[@data-testid='wid-collection-cm_banner_array-1']"
ele_mkpl_banner_arrays_3 = u"//a[@data-testid='wid-collection-cm_banner_array-3']"

# 全球购介绍弹窗
ele_global_intro_popup = u"//div[contains(@class, 'global_intro-content')]"
# 全球购介绍弹窗标题
ele_global_intro_title = ele_global_intro_popup + u"//div[contains(@class, 'enki-body-xl-strong')]"
# 全球购介绍弹窗副标题
ele_global_intro_subtitle = ele_global_intro_popup + u"//div[contains(@class, 'enki-heading-xl')]"
# 探索首单优惠按钮
ele_global_intro_explore_button = ele_global_intro_popup + u"//div[contains(@class, 'bg-[#F37519]')]"
# 关闭按钮
ele_global_intro_close_button = ele_global_intro_popup + u"//i[contains(@class, 'iconTimes')]"

# 优惠券列表元素
ele_coupon_list_section = u"//section[@data-testid='wid-home-collection-cm_coupon_list']"
# 优惠券列表项目
ele_coupon_list_item = ele_coupon_list_section + u"//div[contains(@class, 'swiper-slide')]"
# 优惠券标题
ele_coupon_list_title = ele_coupon_list_section + u"//div[contains(@class, 'text-surface-100-fg-default')]"
# 查看全部按钮
ele_coupon_list_view_all = ele_coupon_list_section + u"//div[contains(@class, 'text-primary-100-fg-default')]"
# 优惠券图标
ele_coupon_list_icon = ele_coupon_list_section + u"//div[contains(@class, 'w-11 h-11 rounded-700 mr-2.5 overflow-hidden flex-none')]"


# search 商家快搜
global_search_input = "wid-search-fouced"
search_product_card = "wid-product-card-container"
global_product_free_shipping_label = "wid-product-card-tag-free_shipping"
homepage_pop_up = "wid-activity-image-picture"
close_popup_on_home = "btn-close-activity-modal"
global_cart_entrance = "wid-cart"

#分享按钮
btn_share = "btn-share"
order_payment_share_pop_up = "mod-share-popup"
order_payment_share_title = "wid-share-title"
order_payment_share_desc = "wid-share-desc"
order_payment_share_copy_link = "btn-share-method-copyLink"


# 箭头右图标
ele_arrow_right_icon = "//i[@class='icon iconfont iconArrowRight' and contains(@style, 'font-size:24px')]"

