# 活动页面头部导航元素
# 返回按钮
ele_activity_back_button ='wid-page-nav-header-back-button'
# 分享按钮
ele_activity_share_button = 'btn-share'

# 分享弹窗相关元素
# 分享弹窗主容器
ele_activity_share_popup = 'mod-share-popup'
# 分享弹窗头部
ele_activity_share_popup_header = 'wid-popup-header'
# 分享弹窗标题
ele_activity_share_popup_title = 'wid-share-title'
# 分享弹窗关闭按钮
ele_activity_share_popup_close = 'btn-modal-close'
# 分享弹窗图片
ele_activity_share_image = 'wid-share-image'
# 复制链接选项
ele_activity_share_copy_link = 'btn-share-method-copyLink'

# 活动页面详细内容元素
# 活动标题
ele_promotion_drawer_title = 'txt-promotion-drawer-title'
# 活动规则
ele_promotion_drawer_rules = 'txt-promotion-drawer-rules'

# 价格过滤器相关元素
# 价格过滤器容器
ele_promotion_price_filters = 'wid-promotion-drawer-price-filters'
# 价格过滤器选项列表
ele_promotion_price_filter_list = ele_promotion_price_filters + "//ul"
# 价格过滤器选项（通用）
ele_promotion_price_filter_item = ele_promotion_price_filter_list + "//li[contains(@data-testid,'wid-promotion-drawer-price-filters-')]"
# 特定价格过滤器选项（可以根据索引选择）
ele_promotion_price_filter_all = ele_promotion_price_filter_list + "//li[contains(@class,'show-out-6-0')]"
ele_promotion_price_filter_under_5 = ele_promotion_price_filter_list + "//li[contains(@class,'show-out-6-1')]"
ele_promotion_price_filter_5_10 = ele_promotion_price_filter_list + "//li[contains(@class,'show-out-6-2')]"
ele_promotion_price_filter_10_15 = ele_promotion_price_filter_list + "//li[contains(@class,'show-out-6-3')]"
ele_promotion_price_filter_15_25 = ele_promotion_price_filter_list + "//li[contains(@class,'show-out-6-4')]"
ele_promotion_price_filter_25_above = ele_promotion_price_filter_list + "//li[contains(@class,'show-out-6-5')]"

# 活动进度信息
ele_promotion_processing_info = 'txt-promotion-drawer-processing-info'

# 商品列表相关元素
# 商品列表容器
ele_promotion_product_list = 'wid-promotion-drawer-product-list'
# 商品卡片容器
ele_promotion_product_card =   'wid-product-card-container'
# 商品图片
ele_promotion_product_image =  'wid-product-card-product-image'
# 商品标题
ele_promotion_product_title =  'wid-product-card-title'
# 商品价格
ele_promotion_product_price ='wid-product-card-price-value'
# 商品原价
ele_promotion_product_base_price ='wid-product-card-base-price'
# 商品收藏按钮
ele_promotion_product_favorite ='btn-favorite'
# 商品加购按钮
ele_promotion_product_atc ='btn-atc-plus'
# 商品销量标签
ele_promotion_product_weekly_sold = 'wid-product-card-weekly-sold'