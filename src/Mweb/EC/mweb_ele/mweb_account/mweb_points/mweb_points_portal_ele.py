# 新增：积分portal页面的青铜升级积分按钮元素
# 这个元素用于定位“Load points to upgrade”按钮，通常用于青铜等级用户进行积分升级操作
ele_points_portal_bronze_upgrade_btn = u"//a[contains(text(), 'Load points to upgrade')]"




# gold 购买普通积分的元素  （200元积分）

# 新增：积分portal页面的“Add points”按钮元素
# 这个元素用于定位“Add points”按钮，通常用于用户增加积分操作
ele_points_portal_add_points_btn = u"//a[text()='Add points']"

# 新增：points列表中的“Checkout”按钮元素
# 这个元素用于定位积分列表中的结算按钮，按钮文本为“Checkout”
ele_points_checkout_button = u"//button[.//span[text()='Checkout']]"

# 新增：points结算页面的“Place Order”按钮元素
# 这个元素用于定位结算页面的下单按钮，data-testid属性为'btn-checkout'
ele_points_place_order_button = u"//button[@data-testid='btn-checkout']"

# 新增：支付方式模块按钮元素（复用礼品卡的元素）
# 这个元素用于定位结算页面的支付方式选择模块
ele_points_payment_box = u"[data-testid='wid-payment-box']"

# 新增：PayPal支付方式按钮元素（复用礼品卡的元素）
# 这个元素用于定位PayPal支付方式选择按钮
ele_points_paypal_payment = u"//*[@data-category='P']"

# 新增：确认支付方式按钮元素（复用礼品卡的元素）
# 这个元素用于定位确认支付方式按钮
ele_points_pay_method_confirm = u"[data-testid='btn-pay-method-confirm']"


# points 页面三种积分值的元素 ，200  500  1000 
# 200元积分的元素，直接定位到span，而不是button
ele_points_200 = u"//span[text()='200']"
ele_points_500 = u"//span[text()='500']"
ele_points_1000 = u"//span[text()='1,000']"




