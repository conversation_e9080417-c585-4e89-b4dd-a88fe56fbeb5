# 1. add to cart按钮
ele_pdp_add_to_cart = "//div[text()='Add to cart']"
ele_pdp_add_to_cart_attrs = "//div[text()='Add to cart']/attribute::*"

# 2. 缩略图
ele_pdp_thumbnail = "div[class^='Header_thumbnail']"
# 3. 主图
ele_pdp_primary_img = "div[class^='Header_atlas']"

# 主图下方分享按钮
ele_share = "//div[@data-testid='mod-pdp-main-banner-container']//i[@data-testid='btn-share']"
# 分享pop
ele_share_pop = "//div[@data-type='popup' and @data-popup-visible='true' and @id='share-popup']"
# 分享pop title
ele_share_pop_title = ele_share_pop + "//header[@id='share-popup-header']/h2"
# 分享pop关闭按钮
ele_share_pop_close = ele_share_pop + "//div[@data-testid='btn-modal-close']"
# 分享pop 商品title
# 分享pop 商品图片
ele_share_pop_product_img = ele_share_pop+"//div[@data-component='CroppedImage']"
# 分享pop语言
ele_share_pop_product_lan = ele_share_pop+"//li"
# copy 链接
ele_share_pop_copy_link = ele_share_pop+"//div[@data-method='copyLink']"
# 保存图片
ele_share_pop_img = ele_share_pop+"//div[@data-method='saveImage']"
# pdp 分享img pop
ele_share_img_pop = "//div[@data-type='popup' and @data-popup-visible='true' and @id='share-image']"
ele_share_img_pop_title = ele_share_img_pop+"//div[contains(@class,'shareImage_title')]"


# 右侧信息区 Header_content
ele_pdp_header_content = "div[class^='Header_content']"

# related products div
ele_pdp_related = 'div[data-role="related"]'
# pdp 相关商品加购按钮
ele_pdp_related_add_to_cart_button = "div[data-role='related'] i[data-role='addButtonPlusIcon']"
# pdp product group 模块
ele_product_group = u"//div[@class='relative flex']"
# pdp product group 模块 select文案
ele_product_group_select = ele_product_group + u"//span[contains(@class,'text-surface')]"
# pdp product group 模块 title
ele_product_group_title = ele_product_group + u"//span[@class='w-full flex']"
# pdp product group 模块 img
ele_product_group_item = ele_product_group + u"//div[@class='flex items-center']"
ele_product_group_img = ele_product_group_item + u"//div[@data-component='CroppedImage']"

# pdp product group pop 模块
ele_product_group_pop = u"//div[@data-type='popup' and @data-popup-visible='true']//div[contains(@class,'Popup_slide-up-enter')]"
# pdp product group pop 模块商品list
ele_product_group_item_list = ele_product_group + u"//div[contains(@class,'property_listItem')]"
# pdp product group pop 关闭按钮
ele_product_group_pop_close = ele_product_group_pop + u"//img[@alt='close']"

# pdp review 模块
ele_mod_review = u"#pdp-sticky-review"
ele_mod_review_card = ele_mod_review + u"//a[@data-testid='wid-review-card']"
# pdp review 片弹出pop
ele_review_pop = u"//div[@data-type='popup']"
ele_review_pop_review_list = ele_review_pop + u"//div[@data-testid='wid-review-list']"

# pdp review pop item list
ele_review_pop_item_list = ele_review_pop + u"//div[@data-testid='wid-review-item']"
# review pop 右上角x按钮
ele_review_pop_close_button = u"//div[@data-type='popup']//div[@data-testid='btn-modal-close']"

# pdp 视频模块
ele_mod_videos = u"//div[@data-testid='mod-videos']"
# 视频卡片
ele_video_card = u"//div[@data-testid='wid-video-card']"
# 视频卡片下方文案
ele_video_card_title = u"//div[@data-testid='text-video-card-title']"
# 视频下方头像
ele_video_card_avatar = u"//div[@data-testid='wid-video-card-avatar']"
# 视频下方点赞按钮
ele_video_card_like = u"//div[@data-testid='wid-set-like']"
# 视频卡片弹出pop
ele_video_pop = u"//div[@data-type='popup']//div[@data-testid='mod-video-modal']"
# 视频卡片弹出pop 右上角x按钮
video_pop_close_button = ele_video_pop + u"//div[@data-testid='btn-modal-close']"
# 视频pop的左侧视频模块
video_pop_video = u"//div[contains(@class,'video_video-box')]"
# 视频pop的左侧视频里的图片
video_pop_img = video_pop_video + u"//div[contains(@class,'video_video-box')]//img[@src]"
# 视频pop的左侧视频里的播放按钮
video_pop_play_icon = u"//div[contains(@class,'video_video-box')]//div[@data-type='play-icon']"
# 视频右侧评论输入框的placeholder
video_pop_comment = u"//div[@data-testid='wid-comments-input']"
# 视频右侧评论输入框 post 按钮
video_pop_post = u"//button[@data-testid='btn-comments-post']"
# 视频右侧评论输入框下方评论 模块
video_pop_comment_item = u"//button[@data-testid='wid-comment-item']"
# 视频右侧商品卡片模块
video_pop_product_card = u"//button[@data-testid='wid-product-card']"

# Reviews 栏相关元素
# Reviews 总数显示
ele_reviews_total_count = u"//section[@id='pdp-sticky-review']//span"




# Reviews list 页面相关元素
# Reviews list 页面标题
ele_reviews_list_title = u"//h1[contains(text(), 'Reviews')]"
# Reviews list 页面高亮关键词
ele_reviews_list_highlighted_keyword = u"//div[@data-testid='wid-highlighted-keyword']"
# Reviews list 排序选择器
ele_reviews_list_sort_dropdown = u"//div[@data-testid='wid-sort-dropdown']"
# Reviews list 筛选后的评论
ele_reviews_list_filtered_reviews = u"//div[@data-testid='wid-review-item']"


