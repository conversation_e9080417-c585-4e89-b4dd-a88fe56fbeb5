# top picks 文案展示：
ele_h5_topx_top_picks_title = "//div[@data-testid='txt-top-x-header-title']"
# topx 榜单标题文案展示：
ele_h5_topx_title = "//div[@data-testid='txt-top-x-header-sub-title']"
# topx 榜单描述展示：
ele_h5_topx_desc_title = "//div[@data-testid='txt-top-x-header-desc']"
# topx 产品卡片展示
ele_h5_topx_prod = "//a[@data-testid='wid-product-card-container']"
# topx 产品卡片加购按钮展示
ele_h5_topx_prod_add_to_cart = "//div[@data-testid='btn-atc-plus']"
# topx 产品卡片top 标签展示
ele_h5_topx_prod_rank = "//div[contains(@data-testid, 'wid-top-x-rank-')]"
# topx 产品卡片卖点展示
ele_h5_topx_prod_metrics = "//div[@data-testid='wid-product-card-metrics']"
# topx see more 按钮展示
ele_h5_topx_see_more = "//div[@data-testid='wid-top-x-show-more']"
# topx 相关榜单标题展示
ele_h5_topx_daily_title = "div[@data-testid='txt-top-x-daily-title']"
# topx 相关榜单 see all 按钮展示
ele_h5_topx_see_all = "//a[@data-testid='btn-top-x-daily-see-all']"
# topx 榜单banner 标题展示
ele_h5_topx_banner_titile = "//div[@data-testid='txt-top-x-daily-title']"
# topx 榜单 banner 图片展示
ele_h5_topx_banner_img = "//div[@data-testid='wid-top-x-related-list-item-product-imgs']"
# topx 榜单 banner箭头展示
ele_h5_topx_banner_arrow = "//div[@data-testid='img-top-x-related-list-item-arrow']"

# topx chart页面：
# topx chart title
ele_h5_topx_chart_title = "//div[@data-testid='txt-top-chart-title']"
# topx chart 副标题：
ele_h5_topx_chart_ranking_title= "//div[@data-testid='txt-top-chart-ranking-desc']"
# topx chart 分类icon
ele_h5_topx_chart_icon = "//div[@data-testid='wid-category-list-wrapper']"
# topx chart list 标题展示：
ele_h5_topx_chart_list_title ="//div[contains(@data-testid, 'wid-top-chart-card-')]"
# topx chart list see all 按钮
ele_h5_topx_chart_list_see_all = "//button[contains(@data-testid, 'btn-top-chart-card-')]"
# topx chart 卖点展示
ele_h5_topx_chart_prod_metrics ="//div[@data-testid='wid-product-card-metrics']"
# top chart 飙升榜标签展示
ele_h5_topx_chart_top_trending_tag ="//li[@data-testid='wid-sub-category-list-item-top_trending']"
# top chart 好评榜标签
ele_h5_topx_chart_top_rated_tag ="//li[@data-testid='wid-sub-category-list-item-most_liked']"

#topx 标签 入口展示
# topx 首页入口标签展示
ele_h5_topx_homepage_tag ="//div[contains(@data-testid, 'wid-product-card-tag-link-top_ranking')]"
# topx pdp入口标签文案展示
ele_h5_topx_pdp_tag = "//div[@data-testid='wid-pdp-bar-module-info-item']"
# topx 搜索结果页对应产品卡片tag 入口
ele_h5_topx_search_prod_tag = "//div[contains(@data-testid, 'wid-product-card-tag-link-top_ranking-')]"










