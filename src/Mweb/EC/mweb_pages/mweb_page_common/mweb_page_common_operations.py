from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_common_page import MWebCommonPage


class PageH5CommonOperations(MWebCommonPage):
    ele_common_paypal_email = u"#email"  # css selector
    ele_common_paypal_next = u"#btnNext"  # css selector

    def __init__(self, page: Page, header):
        super().__init__(page, header)

    def close_advertisement_in_homepage(self):
        if self.page.get_by_test_id("btn-close-activity-modal").all():
            self.page.get_by_test_id("btn-close-activity-modal").click()

    def scroll_one_page(self, n: int):
        for i in range(n):
            self.page.evaluate('window.scrollTo(0, document.body.scrollHeight)')
            self.page.wait_for_timeout(1500)

    def scroll_one_page_until(self, timeout, element):
        while True:
            self.page.evaluate('window.scrollBy(0, window.innerHeight)')
            self.page.wait_for_timeout(timeout)
            if self.FE.ele(element):
                self.FE.ele(element).scroll_into_view_if_needed()
                break

    def pay_with_paypal(self, account, password):
        self.FE.ele(self.ele_common_paypal_email).fill(account)
        self.FE.ele(self.ele_common_paypal_next).click()
        self.FE.ele('#password').fill(password)
        self.FE.ele("#btnLogin").click()
        self.FE.ele(u"//button[text()='完成购物']").click()
        self.page.wait_for_timeout(3000)
