from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.config.weee.log_help import log
from src.Mweb.EC.mweb_ele.mweb_mkpl_vender.mweb_mkpl_vender_ele import (
    ele_create_group_order,
    ele_group_order_pop_up,
    ele_group_order_pop_up_close,
    ele_group_order_pop_up_confirm,
    ele_group_order_share_pop_up,
    ele_group_order_share_copy_link,
    ele_group_order_cancel_button,
    ele_seller_product_card,
    ele_group_order_cancel_popup,
    ele_group_order_cancel_popup_cancel_confirm,
    ele_seller_tab_all,
    ele_seller_product_atc,
    ele_view_seller_float_cart,
    ele_float_cart_container,
    ele_group_order_float_cart_add_more,
    ele_group_order_float_cart_checkout,
    ele_group_order_checkout_back,
    ele_group_order_btn_close,
    ele_group_order_btn_modal_close,
    ele_group_order_modal_close_popup,
    ele_group_order_modal_close_button,
    ele_group_order_modal_close_title,
    ele_group_order_modal_close_desc,
    ele_group_order_modal_close_cancel,
    ele_group_order_modal_close_confirm,
    ele_group_order_seller_homepage_banner,
    ele_group_order_homepage_banner_icon,
    ele_group_order_homepage_banner_title,
    ele_group_order_homepage_banner_subtitle,
    ele_group_order_homepage_banner_label,
    ele_group_order_homepage_banner_visit_icon
)


class MWebVendorGroupOrderPage(PageH5CommonOperations):

    def __init__(self, page: Page, header, browser_context):
        super().__init__(page, header)
        self.bc = browser_context

    def navigate_to_vendor_page(self):
        """访问商家页面"""
        self.page.goto("https://www.sayweee.com/zh/mkpl/vendor/6887")
        self.page.wait_for_timeout(5000)
        log.info("访问商家页面完成")

    """
    def check_group_order_button_exists(self):
        #检查好友拼单按钮是否存在#
        group_order_btn = self.page.get_by_test_id(ele_create_group_order)
        if group_order_btn.count() > 0:
            log.info("好友拼单按钮存在")
            return True
        else:
            # 等待10秒后再次检查
            self.page.wait_for_timeout(10000)
            if group_order_btn.count() > 0:
                log.info("等待后好友拼单按钮存在")
                return True
            else:
                log.info("好友拼单按钮不存在")
                return False
    """

    def click_group_order_button(self):
        """点击好友拼单按钮"""
        group_order_btn = self.page.get_by_test_id(ele_create_group_order)
        if group_order_btn.count() > 0:
            group_order_btn.click()
            log.info("点击好友拼单按钮成功")
            return True
        else:
            log.info("好友拼单按钮不存在")
            return False

    def check_popup_exists(self):
        """检查弹窗是否存在"""
        popup = self.page.get_by_test_id(ele_group_order_pop_up)
        exists = popup.count() > 0
        log.info(f"拼单弹窗存在状态: {exists}")
        return exists

    def close_popup(self):
        """关闭弹窗"""
        close_btn = self.page.get_by_test_id(ele_group_order_pop_up_close)
        if close_btn.count() > 0:
            close_btn.click()
            log.info("关闭拼单弹窗成功")
            return True
        else:
            log.info("弹窗关闭按钮不存在")
            return False

    def click_confirm_button(self):
        """点击邀请好友按钮"""
        confirm_btn = self.page.get_by_test_id(ele_group_order_pop_up_confirm)
        if confirm_btn.count() > 0:
            confirm_btn.click()
            log.info("点击邀请好友按钮成功")
            return True
        else:
            log.info("邀请好友按钮不存在")
            return False

    def check_share_popup_exists(self):
        """检查分享弹窗是否存在"""
        share_popup = self.page.get_by_test_id(ele_group_order_share_pop_up)
        exists = share_popup.count() > 0
        log.info(f"分享弹窗存在状态: {exists}")
        return exists

    def click_copy_link_button(self):
        """点击复制链接按钮"""
        copy_link_btn = self.page.get_by_test_id(ele_group_order_share_copy_link)
        if copy_link_btn.count() > 0:
            copy_link_btn.click()
            log.info("点击复制链接按钮成功")
            return True
        else:
            log.info("复制链接按钮不存在")
            return False

    def click_delete_button(self):
        """点击删除按钮"""
        delete_btn = self.page.get_by_test_id(ele_group_order_cancel_button)
        if delete_btn.count() > 0:
            delete_btn.click()
            log.info("点击删除按钮成功")
            return True
        else:
            log.info("删除按钮不存在")
            return False

    def handle_cancel_popup(self):
        """检查取消弹窗并点击确认按钮"""
        cancel_popup = self.page.get_by_test_id(ele_group_order_cancel_popup)
        if cancel_popup.count() > 0:
            log.info("取消弹窗存在")
            cancel_btn = cancel_popup.get_by_test_id(ele_group_order_cancel_popup_cancel_confirm)
            if cancel_btn.count() > 0 and cancel_btn.is_visible():
                cancel_btn.click()
                log.info("点击取消确认按钮成功")
                return True
            else:
                log.info("取消确认按钮不存在或不可见")
                return False
        else:
            log.info("取消弹窗不存在")
            return False

    def click_back_to_group_order(self):
        """点击返回拼单按钮"""
        back_to_group_order = self.page.get_by_test_id(ele_group_order_checkout_back)
        if back_to_group_order.count() > 0:
            back_to_group_order.click()
            log.info("点击返回拼单按钮成功")
            return True
        else:
            log.info("返回拼单按钮不存在")
            return False

    def click_close_group_order_cart_popup(self):
        """点击返回拼单按钮"""
        close_group_order_popup = self.page.get_by_test_id(ele_group_order_btn_close)
        if close_group_order_popup.count() > 0:
            close_group_order_popup.click()
            log.info("点击购物车弹窗关闭按钮成功")
            return True
        else:
            log.info("购物车弹窗关闭按钮不存在")
            return False

    def check_page_url(self, expected_url):
        """检查页面URL"""
        current_url = self.page.url
        log.info(f"当前URL: {current_url}")
        return expected_url in current_url

    def check_and_click_seller_tab_all(self):
        """检查并点击全部商品标签页"""
        tab_all_element = self.page.get_by_test_id(ele_seller_tab_all)
        exist = tab_all_element.count() > 0
        log.info(f"全部商品tab存在状态: {exist}")
        tab_all_element.first.click()
        return exist


    def check_product_card_exists(self):
        """检查商品卡片是否存在"""
        product_card = self.page.get_by_test_id(ele_seller_product_card)
        exists = product_card.count() > 0
        log.info(f"商品卡片存在状态: {exists}")
        return exists


    def check_and_click_atc_button(self):
        """检查并点击加购按钮"""
        atc_button = self.page.get_by_test_id(ele_seller_product_atc)
        if atc_button.count() > 0:
            atc_button.first.click()
            log.info("点击加购按钮成功")
            return True
        else:
            log.info("加购按钮不存在，跳过")
            return False

    def check_homepage_banner_exists(self):
        """检查主页banner是否存在"""
        banner = self.page.get_by_test_id(ele_group_order_seller_homepage_banner)
        exists = banner.count() > 0
        log.info(f"主页banner存在状态: {exists}")
        return exists

    def check_banner_elements(self):
        """检查banner子元素"""
        elements = [
            (ele_group_order_homepage_banner_icon, "banner图标"),
            (ele_group_order_homepage_banner_title, "banner标题"),
            (ele_group_order_homepage_banner_subtitle, "banner副标题"),
            (ele_group_order_homepage_banner_label, "banner标签")
        ]
        for element_id, name in elements:
            if self.page.get_by_test_id(element_id).count() == 0:
                log.info(f"{name}元素缺失")

    def click_banner_visit_button(self):
        """点击banner访问按钮"""
        visit_btn = self.page.get_by_test_id(ele_group_order_homepage_banner_visit_icon)
        if visit_btn.count() > 0:
            visit_btn.click()
            log.info("点击banner访问按钮成功")
            return True
        else:
            log.info("banner访问按钮不存在")
            return False

    def handle_homepage_banner_flow(self):
        """处理主页banner流程"""
        if self.check_homepage_banner_exists():
            self.check_banner_elements()
            self.page.wait_for_timeout(3000)
            self.click_banner_visit_button()
            self.page.wait_for_timeout(5000)
            return True
        return False


    def handle_modal_close_only(self):
        """仅处理模态框关闭操作"""
        close_btn = self.page.get_by_test_id(ele_group_order_btn_modal_close)
        if close_btn.count() > 0:
            close_btn.click()
            log.info("点击关闭按钮成功")

        self.page.wait_for_timeout(9000)

        close_popup = self.page.get_by_test_id(ele_group_order_modal_close_popup)
        if close_popup.count() > 0:
            cancel_btn = self.page.get_by_test_id(ele_group_order_modal_close_cancel)
            if cancel_btn.count() > 0:
                cancel_btn.click()
                log.info("点击取消按钮成功")

        if close_btn.count() > 0:
            close_btn.click()
            log.info("再次点击关闭按钮成功")

        self.page.wait_for_timeout(3000)

        confirm_btn = self.page.get_by_test_id(ele_group_order_modal_close_confirm)
        if confirm_btn.count() > 0:
            confirm_btn.click()
            log.info("点击确认按钮成功")
        
        self.page.wait_for_timeout(3000)

    def handle_modal_close_flow(self):
        """处理模态框关闭流程包含banner"""
        self.handle_modal_close_only()
        return self.handle_homepage_banner_flow()


    def execute_float_cart_flow(self):
        """执行悬浮购物车流程"""
        self.page.wait_for_timeout(5000)
        
        float_cart_btn = self.page.get_by_test_id(ele_view_seller_float_cart)
        if float_cart_btn.count() > 0:
            float_cart_btn.click()
            log.info("点击悬浮购物车成功")
        
        self.page.wait_for_timeout(3000)
        cart_container = self.page.get_by_test_id(ele_float_cart_container)
        if cart_container.count() > 0:
            add_more_btn = self.page.get_by_test_id(ele_group_order_float_cart_add_more)
            if add_more_btn.count() > 0:
                add_more_btn.click()
                log.info("点击继续购买按钮成功")
            
            self.page.wait_for_timeout(3000)
            if float_cart_btn.count() > 0:
                float_cart_btn.click()
                log.info("再次点击悬浮购物车成功")
            
            self.page.wait_for_timeout(3000)
            checkout_btn = self.page.get_by_test_id(ele_group_order_float_cart_checkout)
            if checkout_btn.count() > 0:
                checkout_btn.click()
                log.info("点击结算按钮成功")
                
                self.page.wait_for_timeout(3000)
                self.click_back_to_group_order()
                self.page.wait_for_timeout(5000)
                self.click_close_group_order_cart_popup()
                self.page.wait_for_timeout(3000)
                self.handle_modal_close_only()

                return True
        
        return False

    def execute_group_order_flow(self):
        """执行好友拼单流程"""

        if not self.click_delete_button():
            return False

        self.page.wait_for_timeout(3000)
        if self.handle_cancel_popup():
            self.page.wait_for_timeout(3000)
            return self.check_page_url("https://www.sayweee.com/en/mkpl/vendor/6887")
        else:
            log.info("取消弹窗处理失败或不存在")
            return True