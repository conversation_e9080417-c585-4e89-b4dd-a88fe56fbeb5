from playwright.sync_api import Page
from src.config.weee.log_help import log
from src.Mweb.EC.mweb_ele.mweb_mkpl_home.mweb_global_waterfall import ele_arrow_right_icon
from src.Mweb.EC.mweb_ele.mweb_mkpl_vender.mweb_mkpl_vender_ele import (
    btn_share,
    order_payment_share_pop_up,
    order_payment_share_title,
    order_payment_share_desc,
    order_payment_share_copy_link
)


class MWebVendorGroupOrderPaymentSharePage:

    def __init__(self, page: Page, header):
        self.page = page
        self.header = header

    def navigate_to_order_detail_and_share(self, order_id="74009002"):
        """访问订单详情页面并执行分享操作"""
        # 1. 查看订单详情页面
        domain = self.header.get('domain', 'https://www.sayweee.com')
        self.page.goto(f"{domain}/zh/order/detail/{order_id}")
        log.info(f"访问订单详情页面完成: {order_id}")
        
        # 2. 滚动页面直到箭头图标可见
        arrow_icon = self.page.locator(ele_arrow_right_icon)
        if arrow_icon.count() > 0:
            arrow_icon.first.scroll_into_view_if_needed()
            log.info("滚动到箭头图标位置")
            
            # 3. 点击箭头图标
            arrow_icon.first.click()
            log.info("点击箭头图标成功")
        else:
            log.info("箭头图标不存在")
            return False
        
        # 4. 等待6秒，检查分享按钮
        self.page.wait_for_timeout(6000)
        
        share_btn = self.page.get_by_test_id(btn_share)
        if share_btn.count() > 0:
            # 5. 点击分享按钮
            share_btn.click()
            log.info("点击分享按钮成功")
            
            # 6. 检查分享弹窗并验证内容
            share_popup = self.page.get_by_test_id(order_payment_share_pop_up)
            if share_popup.count() > 0:
                log.info("分享弹窗存在")
                
                # 验证标题和描述不为空
                title_element = self.page.get_by_test_id(order_payment_share_title)
                desc_element = self.page.get_by_test_id(order_payment_share_desc)
                
                title_text = title_element.text_content() if title_element.count() > 0 else ""
                desc_text = desc_element.text_content() if desc_element.count() > 0 else ""
                
                if title_text.strip() and desc_text.strip():
                    log.info(f"分享标题: {title_text}")
                    log.info(f"分享描述: {desc_text}")
                    
                    # 7. 等待3秒后点击复制链接
                    self.page.wait_for_timeout(3000)
                    
                    copy_link_btn = self.page.get_by_test_id(order_payment_share_copy_link)
                    if copy_link_btn.count() > 0:
                        copy_link_btn.click()
                        log.info("点击复制链接成功")
                        return True
                    else:
                        log.info("复制链接按钮不存在")
                        return False
                else:
                    log.info("分享标题或描述为空")
                    return False
            else:
                log.info("分享弹窗不存在")
                return False
        else:
            log.info("分享按钮不存在")
            return False