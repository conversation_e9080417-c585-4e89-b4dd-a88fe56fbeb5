import allure
from playwright.sync_api import expect, Page
from urllib.parse import urlparse, parse_qs

from src.Mweb.EC.mweb_ele.mweb_store.mweb_store_ele import (
    ele_select_store,
    ele_h5_home_store,
    ele_chinese_option,
    ele_thai_option,
    ele_japanese_option,
    ele_korean_option,
    ele_vietnamese_option,
    ele_filipino_option,
    ele_indian_option,
    ele_explore_option
)
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.common.commfunc import empty_cart
from src.config.weee.log_help import log


class MWebStorePageStore(PageH5CommonOperations):
    """
    首页所有商店选项操作
    """

    def __init__(self, page: Page, header, browser_context):
        super().__init__(page, header)
        self.bc = browser_context

        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info(f"清空购物车发生异常: {str(e)}")

        # 访问指定地址
        self.page.goto("https://www.sayweee.com/en")
        self.page.wait_for_timeout(5000)
        # 关闭首页广告
        self.close_advertisement_in_homepage()

    @allure.step("点击 store 按钮")
    def click_store_button(self):
        """
        点击store按钮
        """
        button_element = self.page.locator(ele_h5_home_store)
        if button_element.count() == 0:
            raise AssertionError("未找到button store元素")

        button_element.click()
        log.info("已点击store button")
        self.page.wait_for_timeout(5000)

    @allure.step("处理并验证 store 弹窗")
    def handle_store_popup(self):
        popup = self.page.locator(ele_select_store)
        expect(popup).to_be_visible(timeout=5000)

        # 验证弹窗中的选项数量
        options = popup.locator("xpath=./div")  # 假设所有选项都是直接子div
        expect(options).to_have_count(8)
        log.info("已验证store弹窗，共有8个选项")

    @allure.step("选择日本商店")
    def select_japanese_store(self):
        option_element = self.page.locator(ele_japanese_option)
        if option_element.count() == 0:
            raise AssertionError("未找到日本store元素")

        option_element.click()
        log.info("已点击日本store")
        self.page.wait_for_timeout(5000)

    @allure.step("验证URL更新")
    def verify_url_update(self):
        current_url = self.page.url
        parsed_url = urlparse(current_url)
        query_params = parse_qs(parsed_url.query)

        expected_url = "https://www.sayweee.com/en"
        expected_param = "ja"

        assert parsed_url.scheme + "://" + parsed_url.netloc + parsed_url.path == expected_url, \
            f"URL基础部分不匹配。期望 {expected_url}，实际 {parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"

        assert 'grocery-store' in query_params, "URL中缺少 grocery-store 参数"
        assert query_params['grocery-store'][0] == expected_param, \
            f"grocery-store 参数值不正确。期望 {expected_param}，实际 {query_params['grocery-store'][0]}"

        log.info(f"URL已正确更新为 {current_url}")

    @allure.step("完整的商店选择流程")
    def complete_store_selection_process(self):
        self.click_store_button()
        self.handle_store_popup()
        self.select_japanese_store()
        self.verify_url_update()


