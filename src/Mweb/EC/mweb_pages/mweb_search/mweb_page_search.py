from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_search import mweb_search_ele
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.config.weee.log_help import log

class MWebSearchPage(PageH5CommonOperations):
    def __init__(self, page: Page, header, browser_context):
        super().__init__(page, header)
        self.bc = browser_context

    def search_by_popular_term(self, keyword: str = "tofu"):
        """
        从Popular Searches中选择指定关键词并跳转搜索结果页
        :param keyword: 热门搜索词（默认tofu）
        """
        # 1. 点击搜索框展开下拉面板
        search_input = self.page.locator("//input[@placeholder='Search']")
        search_input.click()
        self.page.wait_for_load_state("networkidle")

        # 2. 定位Popular Searches区域并点击目标关键词
        popular_search_xpath = f"//div[contains(text(), 'Popular searches')]/following-sibling::div//span[contains(text(), '{keyword}')]"
        self.page.locator(popular_search_xpath).click()
        log.info(f"Clicked popular search term: {keyword}")

        # 3. 验证跳转结果页URL
        expected_url = f"https://www.sayweee.com/en/search/?keyword={keyword}&trigger_type='search_popular'"
        self.page.wait_for_url(expected_url, timeout=15000)
        assert self.page.url == expected_url, f"搜索结果页URL不匹配，当前: {self.page.url}"

        # 4. 验证搜索结果页元素
        self._verify_search_results_page(keyword)

    def _verify_search_results_page(self, keyword: str):
        """校验搜索结果页关键元素"""
        self.page.wait_for_selector("//h1[contains(@class, 'search-results-title')]", state="visible")
        assert f"results for '{keyword}'" in self.page.inner_text("//h1"), "搜索结果标题未显示关键词"
        assert self.page.locator("//div[@data-testid='wid-product-card-container']").first.is_visible(), "未找到商品卡片"
