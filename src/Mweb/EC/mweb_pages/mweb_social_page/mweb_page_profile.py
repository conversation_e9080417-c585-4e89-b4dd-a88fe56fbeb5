# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2025/7/28
@Software       :  PyCharm
------------------------------------
"""
from playwright.sync_api import Page
from src.config.weee.log_help import log
class MWebProfilePage:
    def __init__(self, page: Page, header, browser_context=None):
        self.page = page
        self.header = header
        self.browser_context = browser_context
    def m_verify_tier_badge_visibility(self):
        """验证 tier星星标签 是否可见"""
        try:

            is_visible = self.page.get_by_test_id("wid-tier-badge").is_visible()
            if is_visible:
                self.page.get_by_test_id("wid-tier-badge").click()
            else:
                log.info("tier星星标签 不可见")
                raise AssertionError("tier星星标签不可见")
        except Exception as e:
            log.error(f"验证失败：该用户没有星星标签")
            raise  # 重新抛出异常以确保测试终止
    def m_click_share(self):
        "验证分享按钮"
        self.page.get_by_test_id("btn-share").click()
        log.info("分享按钮已点击")
    def m_verify_share_popup_visibility(self):
        "验证 Share Popup 是否可见"
        self.page.get_by_test_id("wid-share-popup")
        log.info("popup弹出")
    def m_click_copylink(self):
        "验证点击copylink"
        self.page.get_by_test_id("wid-share-channel-copyLink").click()
        log.info("分享成功")
    def m_publish_click(self):
        """验证publish的点击"""
        is_visible = self.page.get_by_test_id("btn-create-post").is_visible()
        if is_visible:
            self.page.get_by_test_id("btn-create-post").click()
        else:
            log.info("publish按钮不可见")
            raise AssertionError("publish按钮不可见")
        return is_visible
    def m_video_upload_notice(self):
        """验证video_upload_notice popup"""
        is_visible = self.page.get_by_test_id("mod-video-upload-notice").is_visible()
        if is_visible:
            self.page.get_by_test_id("btn-app-store").click()
        else:
            log.info("video_upload不可点击")
            raise AssertionError("video_upload不可点击")
        return is_visible


