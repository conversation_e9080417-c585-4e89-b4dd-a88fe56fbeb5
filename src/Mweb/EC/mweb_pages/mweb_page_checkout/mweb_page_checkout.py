"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_page_checkout.py
@Description    :  
@CreateTime     :  2025/7/3 18:03
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/7/3 18:03
"""
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_common_page import MWebCommonPage
from src.common.commonui import scroll_one_page_until
from playwright.sync_api import Page
from src.config.weee.log_help import log
import allure
from src.config.weee.config import TEST_URL


class MWebPageCheckout(MWebCommonPage):
    """
    H5结算页面类
    """

    def __init__(self, page: Page, header, browser_context=None, page_url=None):
        """
        初始化H5结算页面

        Args:
            page: Playwright页面对象
            header: 请求头
            browser_context: 浏览器上下文
            page_url: 页面URL，如果提供则直接导航到该URL
        """
        super().__init__(page, header)
        self.browser_context = browser_context

        # 如果当前不在结算页，则导航到结算页
        if page_url:
            self.page.goto(TEST_URL + page_url)
        elif "/order/checkout" not in self.page.url:
            self.page.goto(TEST_URL + "/order/checkout")

        self.page.wait_for_timeout(3000)


    def verify_checkout_page_features(self):
        """
        验证H5结算页面的各种功能和UI元素

        验证点包括:
        1. 点击结算页Review order下面的生鲜切换日期
        2. 账户有优惠券-结算页面新人优惠券默认是选中状态
        3. 结算页底部显示优惠券被使用显示优惠券名称
        4. 点击结算页面的优惠券-页面跳转到优惠券列表页面
        5. 订单不满免运门槛-结账banner显示：还差$xx.xx，可免运费
        6. 结账页有活动的情况并且满足活动门槛-结账banner显示活动信息
        7. 结算页展示商品数量，商品总额，运费金额
        8. 底部展示日期可以点击
        9. 商品的展开及收起
        10. 会员订单总结显示积分信息
        11. Delivery正常展示当前的运费信息
        12. Delivery Tip可以修改小费

        Returns:
            dict: 包含各验证点结果的字典
        """
        p = self.page
        result = {
            "success": True,
            "details": {}
        }

        try:
            # 1. 验证结算页Review order下面的生鲜切换日期
            with allure.step("验证结算页生鲜切换日期功能"):
                try:
                    # 滚动到Review Order部分
                    scroll_one_page_until(p, "wid-checkout-review-order")
                    p.wait_for_timeout(2000)

                    # 查找并点击切换日期按钮
                    date_selector = p.get_by_test_id("wid-checkout-delivery-time-selector")
                    if date_selector.is_visible(timeout=3000):
                        # 记录当前日期文本
                        current_date = date_selector.text_content()
                        log.info(f"当前配送日期: {current_date}")

                        # 点击切换日期
                        date_selector.click()
                        p.wait_for_timeout(2000)

                        # 验证日期选择弹窗出现
                        date_popup = p.get_by_test_id("wid-checkout-delivery-time-popup")
                        if date_popup.is_visible(timeout=3000):
                            # 选择第一个不同于当前日期的选项
                            date_options = p.get_by_test_id("wid-checkout-time-slot").all()
                            if len(date_options) > 1:
                                date_options[1].click()
                                p.wait_for_timeout(2000)

                                # 验证日期已更改
                                new_date = p.get_by_test_id("wid-checkout-delivery-time-selector").text_content()
                                date_changed = new_date != current_date
                                result["details"]["date_change"] = {
                                    "success": date_changed,
                                    "old_date": current_date,
                                    "new_date": new_date
                                }
                                log.info(f"日期切换{'成功' if date_changed else '失败'}, 新日期: {new_date}")
                            else:
                                result["details"]["date_change"] = {
                                    "success": False,
                                    "reason": "没有足够的日期选项可供切换"
                                }
                                log.warning("没有足够的日期选项可供切换")
                        else:
                            result["details"]["date_change"] = {
                                "success": False,
                                "reason": "日期选择弹窗未显示"
                            }
                            log.warning("日期选择弹窗未显示")
                    else:
                        result["details"]["date_change"] = {
                            "success": False,
                            "reason": "未找到日期选择器"
                        }
                        log.warning("未找到日期选择器")
                except Exception as e:
                    result["details"]["date_change"] = {
                        "success": False,
                        "reason": str(e)
                    }
                    log.error(f"验证日期切换功能时出错: {str(e)}")
                    result["success"] = False

            # 2 & 3. 验证优惠券相关功能
            with allure.step("验证优惠券相关功能"):
                try:
                    # 滚动到优惠券部分
                    scroll_one_page_until(p, "wid-checkout-coupon-selector")
                    p.wait_for_timeout(2000)

                    # 检查优惠券选择器
                    coupon_selector = p.get_by_test_id("wid-checkout-coupon-selector")
                    if coupon_selector.is_visible(timeout=3000):
                        # 检查是否有优惠券被选中
                        coupon_text = coupon_selector.text_content()
                        has_coupon_selected = "Select coupon" not in coupon_text and coupon_text.strip() != ""

                        result["details"]["coupon"] = {
                            "has_coupon_selected": has_coupon_selected,
                            "coupon_text": coupon_text
                        }

                        log.info(f"优惠券状态: {'已选中' if has_coupon_selected else '未选中'}, 文本: {coupon_text}")

                        # 点击优惠券选择器，验证跳转到优惠券列表页面
                        coupon_selector.click()
                        p.wait_for_timeout(2000)

                        # 验证是否进入优惠券列表页面
                        is_coupon_list_page = "/coupon/list" in p.url or p.get_by_test_id("wid-coupon-list").is_visible(
                            timeout=3000)
                        result["details"]["coupon"]["coupon_list_navigation"] = is_coupon_list_page

                        log.info(f"优惠券列表页面导航: {'成功' if is_coupon_list_page else '失败'}")

                        # 返回结算页
                        if is_coupon_list_page:
                            p.go_back()
                            p.wait_for_timeout(2000)
                    else:
                        result["details"]["coupon"] = {
                            "success": False,
                            "reason": "未找到优惠券选择器"
                        }
                        log.warning("未找到优惠券选择器")
                except Exception as e:
                    result["details"]["coupon"] = {
                        "success": False,
                        "reason": str(e)
                    }
                    log.error(f"验证优惠券功能时出错: {str(e)}")
                    result["success"] = False

            # 4. 验证结账banner (免运费门槛)
            with allure.step("验证结账banner (免运费门槛)"):
                try:
                    # 滚动到页面顶部
                    p.evaluate("window.scrollTo(0, 0)")
                    p.wait_for_timeout(2000)

                    # 检查免运费banner
                    free_shipping_banner = p.locator("div[class*='ShippingBanner']").first
                    if free_shipping_banner.is_visible(timeout=3000):
                        banner_text = free_shipping_banner.text_content()
                        has_free_shipping_threshold = "$" in banner_text and "free shipping" in banner_text.lower()

                        result["details"]["free_shipping_banner"] = {
                            "visible": True,
                            "text": banner_text,
                            "has_threshold": has_free_shipping_threshold
                        }

                        log.info(f"免运费banner: 可见, 文本: {banner_text}")
                    else:
                        result["details"]["free_shipping_banner"] = {
                            "visible": False
                        }
                        log.info("免运费banner不可见")
                except Exception as e:
                    result["details"]["free_shipping_banner"] = {
                        "success": False,
                        "reason": str(e)
                    }
                    log.error(f"验证免运费banner时出错: {str(e)}")
                    result["success"] = False

            # 5. 验证活动banner
            with allure.step("验证活动banner"):
                try:
                    # 检查活动banner
                    activity_banner = p.locator("div[class*='ActivityBanner']").first
                    if activity_banner.is_visible(timeout=3000):
                        banner_text = activity_banner.text_content()

                        result["details"]["activity_banner"] = {
                            "visible": True,
                            "text": banner_text
                        }

                        log.info(f"活动banner: 可见, 文本: {banner_text}")
                    else:
                        result["details"]["activity_banner"] = {
                            "visible": False
                        }
                        log.info("活动banner不可见")
                except Exception as e:
                    result["details"]["activity_banner"] = {
                        "success": False,
                        "reason": str(e)
                    }
                    log.error(f"验证活动banner时出错: {str(e)}")
                    result["success"] = False

            # 6. 验证结算页商品信息
            with allure.step("验证结算页商品信息"):
                try:
                    # 滚动到商品列表
                    scroll_one_page_until(p, "wid-checkout-product-list")
                    p.wait_for_timeout(2000)

                    # 检查商品列表
                    product_list = p.get_by_test_id("wid-checkout-product-list")
                    if product_list.is_visible(timeout=3000):
                        # 获取商品数量
                        product_items = p.get_by_test_id("wid-checkout-product-item").all()
                        product_count = len(product_items)

                        # 验证商品展开/收起功能
                        expand_button = p.get_by_test_id("wid-checkout-product-expand")
                        if expand_button.is_visible(timeout=2000):
                            # 记录当前状态
                            initial_state = "expanded" if product_count > 0 and product_items[
                                0].is_visible() else "collapsed"

                            # 点击展开/收起按钮
                            expand_button.click()
                            p.wait_for_timeout(1000)

                            # 验证状态是否改变
                            if initial_state == "expanded":
                                # 应该收起
                                state_changed = not (product_count > 0 and product_items[0].is_visible())
                            else:
                                # 应该展开
                                state_changed = product_count > 0 and product_items[0].is_visible()

                            # 再次点击恢复原状
                            expand_button.click()
                            p.wait_for_timeout(1000)

                            result["details"]["product_expand_collapse"] = {
                                "success": state_changed,
                                "initial_state": initial_state
                            }

                            log.info(f"商品展开/收起功能: {'正常' if state_changed else '异常'}")
                        else:
                            result["details"]["product_expand_collapse"] = {
                                "success": False,
                                "reason": "未找到展开/收起按钮"
                            }
                            log.warning("未找到商品展开/收起按钮")

                        result["details"]["product_info"] = {
                            "count": product_count
                        }

                        log.info(f"商品数量: {product_count}")
                    else:
                        result["details"]["product_info"] = {
                            "success": False,
                            "reason": "未找到商品列表"
                        }
                        log.warning("未找到商品列表")
                except Exception as e:
                    result["details"]["product_info"] = {
                        "success": False,
                        "reason": str(e)
                    }
                    log.error(f"验证商品信息时出错: {str(e)}")
                    result["success"] = False

            # 7. 验证订单总结信息
            with allure.step("验证订单总结信息"):
                try:
                    # 滚动到订单总结部分
                    scroll_one_page_until(p, "wid-order-summary")
                    p.wait_for_timeout(2000)

                    # 检查订单总结
                    order_summary = p.get_by_test_id("wid-order-summary")
                    if order_summary.is_visible(timeout=3000):
                        # 获取商品总额
                        subtotal = p.get_by_test_id("wid-order-summary-item-subtotal")
                        subtotal_text = subtotal.text_content() if subtotal.is_visible() else "未找到"

                        # 获取运费
                        delivery_fee = p.get_by_test_id("wid-order-summary-item-delivery_fee")
                        delivery_fee_text = delivery_fee.text_content() if delivery_fee.is_visible() else "未找到"

                        # 检查会员积分信息
                        vip_points = p.locator("div:has-text('As a VIP member')").first
                        has_vip_points = vip_points.is_visible(timeout=2000)
                        vip_points_text = vip_points.text_content() if has_vip_points else "未找到"

                        result["details"]["order_summary"] = {
                            "subtotal": subtotal_text,
                            "delivery_fee": delivery_fee_text,
                            "has_vip_points": has_vip_points,
                            "vip_points_text": vip_points_text if has_vip_points else None
                        }

                        log.info(f"订单总结: 商品总额={subtotal_text}, 运费={delivery_fee_text}, 会员积分={has_vip_points}")
                    else:
                        result["details"]["order_summary"] = {
                            "success": False,
                            "reason": "未找到订单总结"
                        }
                        log.warning("未找到订单总结")
                except Exception as e:
                    result["details"]["order_summary"] = {
                        "success": False,
                        "reason": str(e)
                    }
                    log.error(f"验证订单总结时出错: {str(e)}")
                    result["success"] = False

            # 8. 验证小费功能
            with allure.step("验证小费功能"):
                try:
                    # 滚动到小费部分
                    scroll_one_page_until(p, "wid-checkout-tip-selector")
                    p.wait_for_timeout(2000)

                    # 检查小费选择器
                    tip_selector = p.get_by_test_id("wid-checkout-tip-selector")
                    if tip_selector.is_visible(timeout=3000):
                        # 记录当前小费
                        current_tip = tip_selector.text_content()
                        log.info(f"当前小费: {current_tip}")

                        # 点击修改小费
                        tip_selector.click()
                        p.wait_for_timeout(2000)

                        # 验证小费弹窗
                        tip_popup = p.get_by_test_id("wid-checkout-tip-popup")
                        if tip_popup.is_visible(timeout=3000):
                            # 选择不同的小费选项
                            tip_options = p.get_by_test_id("wid-checkout-tip-option").all()
                            if len(tip_options) > 1:
                                # 选择第二个选项
                                tip_options[1].click()
                                p.wait_for_timeout(2000)

                                # 验证小费已更改
                                new_tip = p.get_by_test_id("wid-checkout-tip-selector").text_content()
                                tip_changed = new_tip != current_tip

                                result["details"]["tip_change"] = {
                                    "success": tip_changed,
                                    "old_tip": current_tip,
                                    "new_tip": new_tip
                                }

                                log.info(f"小费修改: {'成功' if tip_changed else '失败'}, 新小费: {new_tip}")
                            else:
                                result["details"]["tip_change"] = {
                                    "success": False,
                                    "reason": "没有足够的小费选项"
                                }
                                log.warning("没有足够的小费选项")
                        else:
                            result["details"]["tip_change"] = {
                                "success": False,
                                "reason": "小费弹窗未显示"
                            }
                            log.warning("小费弹窗未显示")
                    else:
                        result["details"]["tip_change"] = {
                            "success": False,
                            "reason": "未找到小费选择器"
                        }
                        log.warning("未找到小费选择器")
                except Exception as e:
                    result["details"]["tip_change"] = {
                        "success": False,
                        "reason": str(e)
                    }
                    log.error(f"验证小费功能时出错: {str(e)}")
                    result["success"] = False

        except Exception as e:
            result["success"] = False
            result["error"] = str(e)
            log.error(f"验证结算页面功能时出现未处理的异常: {str(e)}")
            import traceback
            log.error(traceback.format_exc())

        return result