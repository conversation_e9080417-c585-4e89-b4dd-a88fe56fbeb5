"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_page_checkout.py
@Description    :  
@CreateTime     :  2025/7/3 18:03
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/7/3 18:03
"""
from src.Mweb.EC.mweb_ele.mweb_checkout import mweb_checkout_ele
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.common.commonui import scroll_one_page_until
from playwright.sync_api import Page, expect

from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
import allure


class MWebPageCheckout(PageH5CommonOperations):
    """
    H5结算页面类
    """

    def __init__(self, page: Page, header, browser_context=None, page_url=None):
        """
        初始化H5结算页面

        Args:
            page: Playwright页面对象
            header: 请求头
            browser_context: 浏览器上下文
            page_url: 页面URL，如果提供则直接导航到该URL
        """
        super().__init__(page, header)
        self.browser_context = browser_context

        # 如果当前不在结算页，则导航到结算页
        if page_url:
            self.page.goto(TEST_URL + page_url)
        elif "/order/checkout" not in self.page.url:
            self.page.goto(TEST_URL + "/order/checkout")

        self.page.wait_for_timeout(3000)
        # 关闭首页广告
        self.close_advertisement_in_homepage()

    def verify_checkout_title(self):
        """验证结算页标题"""
        with allure.step("验证会员等级显示"):
            try:
                # 获取会员等级信息
                member_level = self.page.get_by_test_id('wid-checkout-rewards-header')
                level_text = member_level.text_content()

                # 根据不同等级验证对应的图标和文案
                if "Bronze" in level_text.upper():
                    # p.get_by_test_id('wid-checkout-rewards-header')
                    expect(self.FE.ele("//div[@data-testid='btn-atc-plus']").locator('img')).to_be_visible()
                    expect(self.page.locator("text=Bronze Rewards member")).to_be_visible()
                elif "Silver" in level_text.upper():
                    expect(self.page.get_by_test_id('wid-checkout-rewards-header').locator('img')).to_be_visible()
                    expect(self.page.locator("text=Silver Rewards member")).to_be_visible()
                elif "Gold" in level_text.upper():
                    expect(self.page.get_by_test_id('wid-checkout-rewards-header').locator('img')).to_be_visible()
                    expect(self.page.locator("text=Gold Rewards member")).to_be_visible()
                log.info(f"会员等级验证成功: {level_text}")
            except Exception as e:
                log.error(f"验证会员等级显示失败: {str(e)}")
                raise


    def verify_shipping_banner(self):
        """验证结账banner (免运费门槛)"""
        p = self.page
        try:
            p.evaluate("window.scrollTo(0, 0)")
            p.wait_for_timeout(2000)
            
            free_shipping_banner = p.get_by_test_id('wid-checkout-reminder-content')
            if free_shipping_banner.is_visible(timeout=3000):
                banner_text = free_shipping_banner.text_content()
                has_free_shipping_threshold = "$" in banner_text and "FREE delivery" in banner_text.lower()
                log.info(f"免运费banner: 可见, 文本: {banner_text}")
                return {"success": True, "visible": True, "text": banner_text, "has_threshold": has_free_shipping_threshold}
            else:
                log.info("免运费banner不可见")
                return {"success": True, "visible": False}
        except Exception as e:
            log.error(f"验证免运费banner时出错: {str(e)}")
            return {"success": False, "reason": str(e)}

    def verify_date_change(self):
        """验证结算页生鲜切换日期功能"""
        p = self.page
        try:
            scroll_one_page_until(p, "[data-testid='wid-checkout-review-order']")
            p.wait_for_timeout(2000)
            
            date_selector = p.get_by_test_id("wid-review-order-card-normal-shipping-date")
            if date_selector.is_visible(timeout=3000):
                date_selector.click()
                p.wait_for_timeout(2000)
                
                date_page_exists = p.get_by_test_id("wid-navbar-title").is_visible(timeout=3000)
                if date_page_exists:
                    p.get_by_test_id("wid-navbar-left").click()
                    p.wait_for_timeout(1000)
                    log.info("日期切换功能验证成功")
                    return {"success": True}
                else:
                    log.warning("日期页面未显示")
                    return {"success": False, "reason": "日期页面未显示"}
            else:
                log.warning("未找到日期选择器")
                return {"success": False, "reason": "未找到日期选择器"}
        except Exception as e:
            log.error(f"验证日期切换功能时出错: {str(e)}")
            return {"success": False, "reason": str(e)}

    def verify_coupon_functionality(self):
        """验证优惠券相关功能"""
        p = self.page
        try:
            scroll_one_page_until(p, "[data-testid='wid-checkout-coupon-wrap']")
            p.wait_for_timeout(2000)
            
            coupon_selector = p.get_by_test_id("wid-checkout-coupon-wrap")
            if coupon_selector.is_visible(timeout=3000):
                coupon_text = coupon_selector.text_content()
                has_coupon_selected = "Please select or enter coupon code" not in coupon_text and coupon_text.strip() != ""
                
                log.info(f"优惠券状态: {'已选中' if has_coupon_selected else '未选中'}, 文本: {coupon_text}")
                
                coupon_selector.click()
                p.wait_for_timeout(2000)
                
                is_coupon_list_page = "/coupon/list" in p.url or p.get_by_test_id("wid-drawer-header-title").is_visible(timeout=3000)
                log.info(f"优惠券列表页面导航: {'成功' if is_coupon_list_page else '失败'}")
                
                if is_coupon_list_page:
                    p.get_by_test_id("btn-drawer-header-back").click()
                    p.wait_for_timeout(2000)
                
                return {
                    "success": True,
                    "has_coupon_selected": has_coupon_selected,
                    "coupon_text": coupon_text,
                    "coupon_list_navigation": is_coupon_list_page
                }
            else:
                log.warning("未找到优惠券选择器")
                return {"success": False, "reason": "未找到优惠券选择器"}
        except Exception as e:
            log.error(f"验证优惠券功能时出错: {str(e)}")
            return {"success": False, "reason": str(e)}

    def verify_product_info_popup(self):
        """验证结算页商品信息弹窗"""
        p = self.page
        try:
            scroll_one_page_until(p, "//div[@data-testid='wid-review-order-card-summary']")
            p.wait_for_timeout(3000)
            
            arrow_button = p.get_by_test_id(mweb_checkout_ele.ele_rebiew_order_arrow_right)
            if arrow_button.is_visible(timeout=3000):
                arrow_button.click()
                p.wait_for_timeout(2000)
                
                popup_selectors = [
                    mweb_checkout_ele.ele_checkout_product_title,
                    "wid-drawer-header-title",
                    "wid-modal-title"
                ]
                
                product_popup = None
                for selector in popup_selectors:
                    element = p.get_by_test_id(selector)
                    if element.is_visible(timeout=2000):
                        product_popup = element
                        break
                
                if product_popup:
                    title_exists = product_popup.is_visible()
                    
                    close_selectors = [
                        mweb_checkout_ele.ele_checkout_product_clos_icon,
                        "btn-drawer-header-back",
                        "btn-modal-close"
                    ]
                    
                    close_button = None
                    for selector in close_selectors:
                        element = p.get_by_test_id(selector)
                        if element.is_visible(timeout=2000):
                            close_button = element
                            break
                    
                    close_button_exists = close_button is not None
                    
                    product_items = p.get_by_test_id(mweb_checkout_ele.ele_checkout_product_drawer_content).all()
                    product_info_valid = len(product_items) > 0
                    
                    if close_button:
                        close_button.click()
                        p.wait_for_timeout(1000)
                    
                    log.info("商品信息验证成功")
                    return {
                        "success": title_exists and close_button_exists and product_info_valid,
                        "title_exists": title_exists,
                        "close_button_exists": close_button_exists,
                        "product_info_valid": product_info_valid
                    }
                else:
                    log.warning("商品列表弹窗未显示")
                    return {"success": False, "reason": "商品列表弹窗未显示"}
            else:
                log.warning("未找到右侧箭头按钮")
                return {"success": False, "reason": "未找到右侧箭头按钮"}
        except Exception as e:
            log.error(f"验证商品信息时出错: {str(e)}")
            return {"success": False, "reason": str(e)}

    def verify_payment_method(self):
        """验证支付方式模块"""
        p = self.page
        try:
            scroll_one_page_until(p, "[data-testid='mod-checkout-payment']")
            p.wait_for_timeout(2000)
            
            # 检查支付方式模块是否存在
            payment_content = p.get_by_test_id(mweb_checkout_ele.ele_payment_method_section)
            if payment_content.is_visible(timeout=3000):
                # 支付方式已存在
                current_payment_text = payment_content.text_content()
                log.info(f"当前支付方式: {current_payment_text}")
                
                payment_content.click()
                p.wait_for_timeout(2000)
                
                # 检查是否进入支付方式列表页面
                is_payment_list_page = "/payment/method" in p.url or p.get_by_test_id("wid-drawer-header-title").is_visible(timeout=3000)
                
                if not is_payment_list_page:
                    return {"success": False, "reason": "未能进入支付方式列表页面"}
                
                # 查找支付方式选项
                payment_options = p.locator("[data-testid*='payment-method']").all()
                if not payment_options:
                    return {"success": False, "reason": "未找到支付方式选项"}
                
                payment_options[0].click()
                p.wait_for_timeout(1000)
                
                # 查找确认按钮
                confirm_button = p.get_by_test_id("btn-confirm")
                if not confirm_button.is_visible(timeout=2000):
                    return {"success": False, "reason": "未找到确认按钮"}
                
                confirm_button.click()
                p.wait_for_timeout(2000)
                
                # 检查是否返回结算页
                new_payment_content = p.get_by_test_id("mod-checkout-payment-content")
                if not new_payment_content.is_visible(timeout=2000):
                    return {"success": False, "reason": "返回结算页后支付方式模块不可见"}
                
                new_payment_text = new_payment_content.text_content()
                log.info(f"支付方式选择完成: {new_payment_text}")
                return {
                    "success": True,
                    "payment_module_exists": True,
                    "payment_list_navigation": True,
                    "payment_selection_success": True,
                    "old_payment": current_payment_text,
                    "new_payment": new_payment_text
                }
            else:
                # 支付方式模块不存在，检查提示文案
                add_payment_text = p.locator("text*='add payment'").first
                if add_payment_text.is_visible(timeout=2000):
                    prompt_text = add_payment_text.text_content()
                    log.info(f"支付方式提示文案: {prompt_text}")
                    return {
                        "success": True,
                        "payment_module_exists": False,
                        "prompt_text": prompt_text
                    }
                else:
                    return {"success": False, "reason": "支付方式模块不存在且无提示文案"}
                    
        except Exception as e:
            log.error(f"验证支付方式时出错: {str(e)}")
            return {"success": False, "reason": str(e)}

    def verify_order_summary(self):
        """验证订单总结信息"""
        p = self.page
        try:
            scroll_one_page_until(p, "[data-testid='mod-checkout-order-summary']")
            p.wait_for_timeout(2000)
            
            order_summary_data = {}
            
            # 验证Order Summary标题
            title = p.get_by_test_id(mweb_checkout_ele.ele_checkout_summary_title)
            order_summary_data["title_visible"] = title.is_visible(timeout=2000)
            if order_summary_data["title_visible"]:
                order_summary_data["title_text"] = title.text_content()
                log.info(f"Order Summary标题: {order_summary_data['title_text']}")
            
            # 验证会员等级文案和图标
            vip_content = p.get_by_test_id(mweb_checkout_ele.ele_checkout_loytal_vip_content)
            vip_icon = p.get_by_test_id(mweb_checkout_ele.ele_checkout_loytal_vip_icon)
            order_summary_data["vip_content_visible"] = vip_content.is_visible(timeout=2000)
            order_summary_data["vip_icon_visible"] = vip_icon.is_visible(timeout=2000)
            
            if order_summary_data["vip_content_visible"]:
                order_summary_data["vip_content_text"] = vip_content.text_content()
                log.info(f"标题下文案: {order_summary_data['vip_content_text']}")
            
            # 验证商品总额
            subtotal = p.get_by_test_id(mweb_checkout_ele.ele_checkout_subtotal_amount)
            order_summary_data["subtotal_visible"] = subtotal.is_visible(timeout=2000)
            order_summary_data["subtotal_text"] = subtotal.text_content() if order_summary_data["subtotal_visible"] else ""
            if order_summary_data["subtotal_visible"]:
                log.info(f"商品总计金额: {order_summary_data['subtotal_text']}")

            # 验证Service Fee和i标签
            service_fee = p.get_by_test_id(mweb_checkout_ele.ele_order_service_fee_amount)
            service_fee_icons = p.get_by_test_id(mweb_checkout_ele.ele_order_service_fee_icon).all()
            order_summary_data["service_fee_visible"] = service_fee.is_visible(timeout=2000)
            order_summary_data["service_fee_icon_visible"] = len(service_fee_icons) > 0 and service_fee_icons[0].is_visible(timeout=2000)
            
            # 点击Service Fee i标签验证弹窗（优化处理）
            if order_summary_data["service_fee_icon_visible"]:
                try:
                    service_fee_icons[0].click()
                    p.wait_for_timeout(500)
                    popup_exists = p.locator("[role='tooltip'], [role='dialog'], .ant-popover").is_visible(timeout=1000)
                    order_summary_data["service_fee_popup_exists"] = popup_exists
                    if popup_exists:
                        p.keyboard.press("Escape")
                        p.wait_for_timeout(300)
                except Exception:
                    order_summary_data["service_fee_popup_exists"] = False
            
            # 验证Delivery Fee和i标签
            delivery_fee = p.get_by_test_id(mweb_checkout_ele.ele_order_delivery_fee_amount)
            delivery_fee_icons = p.get_by_test_id(mweb_checkout_ele.ele_order_delivery_fee_icon).all()
            order_summary_data["delivery_fee_visible"] = delivery_fee.is_visible(timeout=2000)
            order_summary_data["delivery_fee_icon_visible"] = len(delivery_fee_icons) > 0 and delivery_fee_icons[0].is_visible(timeout=2000)
            
            # 点击Delivery Fee i标签验证弹窗（优化处理）
            if order_summary_data["delivery_fee_icon_visible"]:
                try:
                    delivery_fee_icons[0].click()
                    p.wait_for_timeout(500)
                    popup_exists = p.locator("[role='tooltip'], [role='dialog'], .ant-popover").is_visible(timeout=1000)
                    order_summary_data["delivery_fee_popup_exists"] = popup_exists
                    if popup_exists:
                        p.keyboard.press("Escape")
                        p.wait_for_timeout(300)
                except Exception:
                    order_summary_data["delivery_fee_popup_exists"] = False
            
            # 验证小费
            tip_amount = p.get_by_test_id(mweb_checkout_ele.ele_order_summary_tip_amount)
            tip_visible = tip_amount.is_visible(timeout=2000)
            order_summary_data["tip_visible"] = tip_visible
            if tip_visible:
                order_summary_data["tip_text"] = tip_amount.text_content()
            
            # 验证总计
            total_amount = p.get_by_test_id(mweb_checkout_ele.ele_order_summary_total_amount)
            order_summary_data["total_visible"] = total_amount.is_visible(timeout=2000)
            order_summary_data["total_text"] = total_amount.text_content() if order_summary_data["total_visible"] else ""
            
            order_summary_data["success"] = True
            log.info("订单总结信息验证完成")
            return order_summary_data
            
        except Exception as e:
            log.error(f"验证订单总结时出错: {str(e)}")
            return {"success": False, "reason": str(e)}

    def verify_tip_functionality(self):
        """验证小费选择模块存在，正常显示2,3,4跟other几个选项，点击后小费能正常选择"""
        p = self.page
        try:
            scroll_one_page_until(p, "[data-testid='mod-checkout-delivery-tip']")
            p.wait_for_timeout(2000)
            
            # 验证小费模块存在
            tip_module = p.get_by_test_id(mweb_checkout_ele.ele_checkout_tip)
            if not tip_module.is_visible(timeout=3000):
                log.warning("小费模块不存在")
                return {"success": False, "reason": "小费模块不存在"}
            
            # 验证小费选项按钮存在
            tip_options = {
                "tip_2": p.get_by_test_id(mweb_checkout_ele.ele_checkout_tip_2).first,
                "tip_3": p.get_by_test_id(mweb_checkout_ele.ele_checkout_tip_3).first, 
                "tip_4": p.get_by_test_id(mweb_checkout_ele.ele_checkout_tip_4).first,
                "tip_0": p.get_by_test_id(mweb_checkout_ele.ele_checkout_tip_0).first  # other选项
            }
            
            visible_options = {}
            for option_name, option_element in tip_options.items():
                is_visible = option_element.is_visible(timeout=2000)
                visible_options[option_name] = is_visible
                if is_visible:
                    log.info(f"小费选项 {option_name} 可见")
            
            # 验证至少有3个选项可见
            visible_count = sum(visible_options.values())
            if visible_count < 3:
                log.warning(f"小费选项数量不足，只有{visible_count}个可见")
                return {"success": False, "reason": f"小费选项数量不足，只有{visible_count}个可见"}
            
            # 测试点击功能 - 点击$3选项
            if visible_options["tip_3"]:
                tip_3_button = tip_options["tip_3"]
                tip_3_button.click()
                p.wait_for_timeout(1000)
                
                log.info("$3小费选项点击测试完成")
                
                return {
                    "success": True,
                    "visible_options": visible_options,
                    "visible_count": visible_count,
                    "click_test_passed": True
                }
            else:
                log.info("$3选项不可见，跳过点击测试")
                return {
                    "success": True,
                    "visible_options": visible_options,
                    "visible_count": visible_count,
                    "click_test_passed": False,
                    "reason": "$3选项不可见"
                }
                
        except Exception as e:
            log.error(f"验证小费功能时出错: {str(e)}")
            return {"success": False, "reason": str(e)}

    def verify_checkout_page_features(self):
        """验证H5结算页面的各种功能和UI元素 - 统一入口"""
        result = {"success": True, "details": {}}
        
        try:
            # 验证商品信息弹窗
            with allure.step("验证结算页商品信息"):
                result["details"]["product_info"] = self.verify_product_info_popup()
                if not result["details"]["product_info"]["success"]:
                    result["success"] = False
            
            # 验证订单总结信息
            with allure.step("验证订单总结信息"):
                result["details"]["order_summary"] = self.verify_order_summary()
                if not result["details"]["order_summary"]["success"]:
                    result["success"] = False
                    
        except Exception as e:
            result["success"] = False
            result["error"] = str(e)
            log.error(f"验证结算页面功能时出现未处理的异常: {str(e)}")
            import traceback
            log.error(traceback.format_exc())
        
        return result
    
    def submit_order_with_points_on_checkout_page(self) -> str:
        """在结算页面使用积分支付提交订单"""
        try:
            # 等待页面完全加载
            self.page.wait_for_timeout(5000)
            
            # 等待结算按钮可见
            checkout_btn = self.page.get_by_test_id("btn-checkout")
            checkout_btn.wait_for(state="visible", timeout=15000)
            
            # 检查积分是否已使用
            points_deduction = self.page.get_by_test_id(mweb_checkout_ele.ele_order_summary_point_deduction_amount)
            if points_deduction.is_visible():
                log.info("积分已使用，可直接结算")
            else:
                # 点击积分支付区域
                points_content = self.page.get_by_test_id(mweb_checkout_ele.ele_payment_method_point)
                if points_content.is_visible(timeout=8000):
                    points_content.click()
                    self.page.wait_for_timeout(2000)
                    log.info("已点击积分支付区域")
                else:
                    log.warning("积分支付不可用，跳过积分支付")
            
            # 检查是否需要选择地址
            warning_icon = self.page.get_by_test_id("mod-checkout-delivery-info-warning-icon")
            if warning_icon.is_visible():
                address_content = self.page.get_by_test_id("mod-checkout-delivery-info-content")
                address_content.click()
                self.page.wait_for_timeout(2000)
                
                # 选择第一个地址
                first_address = self.page.locator("[data-testid*='address-item']").first
                if first_address.is_visible():
                    first_address.click()
                    self.page.wait_for_timeout(2000)
                    log.info("已选择地址")
            
            # 点击结算按钮
            submit_btn = self.page.get_by_test_id("btn-checkout")
            submit_btn.click()
            self.page.wait_for_timeout(10000)
            
            # 获取订单号
            current_url = self.page.url
            order_id = current_url.split("/")[-1].split("?")[0]
            
            # 跳转到订单详情页
            self.page.goto(TEST_URL + f"/order/detail/{order_id}")
            self.page.wait_for_timeout(3000)
            
            log.info(f"成功提交订单: {order_id}")
            return order_id
                
        except Exception as e:
            log.error(f"提交订单失败: {str(e)}")
            return None