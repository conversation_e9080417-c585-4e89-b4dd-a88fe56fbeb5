from typing import Literal

from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.common.commfunc import empty_cart
from src.common.commonui import home_init_h5, scroll_one_page_until
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class MWebMkplAllStorePage(PageH5CommonOperations):

    def __init__(self, page: Page, header, bc):
        super().__init__(page, header)
        self.bc = bc

        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        self.page.goto(TEST_URL + "/mkpl/global?mode=sub_page&hide_activity_pop=1?joinEnki=true")
        self.page.wait_for_timeout(5000)
