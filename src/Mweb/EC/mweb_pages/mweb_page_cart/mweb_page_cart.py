from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.common.commfunc import empty_cart
from src.common.commonui import home_init, home_init_h5
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from src.Mweb.EC.mweb_pages.mweb_common_page import MWebCommonPage
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations


class MWebCartPage(MWebCommonPage):
    ele_home_cart = u"div[aria-label='My cart']"
    ele_cart_back_to_home = u"//button[text()='Return home']"
    ele_cart_add_to_cart = u"i[data-role='addButtonPlusIcon'][role='button']"
    ele_cart_checkout_button = u"//button[text()='Checkout']"
    ele_cart_select_all_cart = u"//div[contains(text(), '全选')]/..//div[contains(@class, 'rounded-full')]"
    ele_cart_continue = u"//button[text()='继续']"
    ele_home_bestsellers = u"//h2[text()='人气热卖']"
    ele_checkout_input_your_address = u"//span[text()='请填写您的送货地址及联系方式。']"
    ele_checkout_payment = u"#payment i"
    # 人气热卖
    ele_home_hot_selling_add_to_cart = u"//h2[text()='人气热卖']/ancestor::section//i[@data-role='addButtonPlusIcon']"  # xpath

    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入购物车
        self.page.goto(TEST_URL + page_url + "?joinEnki=true")
        self.page.wait_for_timeout(2000)

    def cart_page_operations(self):
        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        home_init_h5(self.page)
        self.page.wait_for_timeout(3000)
        self._empty_cart_verification()

    def mweb_start_shopping(self):
        """
        点击空购物车的start_shopping按钮
        """
        # 点击空购物车的start_shopping按钮
        self.FE.ele(mweb_cart_ele.ele_empty_cart_start_shopping).click()

    def checkout_with_paypal(self):
        home_init(self.page)
        self.page.wait_for_timeout(3000)

        if "tb1" in TEST_URL:
            # self.FE.ele(self.ele_home_bestsellers).scroll_into_view_if_needed()
            # self.page.locator(self.ele_home_bestsellers).scroll_into_view_if_needed()
            # self.page.evaluate('window.scrollTo(0, document.body.scrollHeight)')
            # self.page.locator(self.ele_home_bestsellers).scroll_into_view_if_needed()
            self.FE.ele(self.ele_home_bestsellers).hover()
            best_sellers_add_to_cart = self.FE.eles(self.ele_home_hot_selling_add_to_cart)
            for index, item in enumerate(best_sellers_add_to_cart):
                item.click()
                self.page.wait_for_timeout(1000)
                if index == 2:
                    break
        elif "www" in TEST_URL:
            editor_pick_add_to_cart = self.FE.eles(
                u"//h2[text()='特价精选']/../following-sibling::div//i[@data-role='addButtonPlusIcon']")
            for index, item in enumerate(editor_pick_add_to_cart):
                item.click()
                self.page.wait_for_timeout(1000)
                if index == 2:
                    break

        # 进入购物车
        self.FE.ele(self.ele_home_cart).click()
        self.page.wait_for_timeout(5000)

        self.FE.ele(self.ele_cart_checkout_button).click()

        # 如果购物车有多个，合并购物车
        if self.FE.ele(self.ele_cart_select_all_cart):
            self.FE.ele(self.ele_cart_select_all_cart).click()
            self.page.wait_for_timeout(3000)
            self.FE.ele(
                u"//div[contains(text(), '您可以选择多个购物车进行结算')]//following-sibling::div/button").click()

        # upsell处理
        if self.FE.ele(self.ele_cart_continue):
            self.FE.ele(self.ele_cart_continue).click()

        # 如果没填地址，需要填写地址
        if self.FE.ele(self.ele_checkout_input_your_address):
            self.FE.ele(u"//span[text()='请填写您的送货地址及联系方式。']/../i").click()
            self.page.wait_for_timeout(2000)
            self.FE.eles(u"//div[@data-id]")[0].click()

        # 如果积分支付打开，则把积分支付关掉
        if self.FE.ele(u"i[class*='iconchecked']"):
            self.FE.ele(u"i[class*='iconchecked']").click()

        self.FE.ele(self.ele_checkout_payment).click()
        self.FE.ele(u"//dl[@data-category='P']").click()
        self.page.wait_for_timeout(2000)
        self.FE.ele(self.ele_cart_checkout_button).click()
        self.page.wait_for_timeout(3000)
        if self.FE.ele(u"//div[text()='请确认该国际订单的收件人姓名和地址信息']"):
            self.FE.ele(u"//div[contains(text(), '我确认所提供的姓名是我的真实姓名')]/preceding-sibling::div").click()
            self.FE.ele(u"//div[text()='确认并下单']").click()

        # 校验paypal页面
        if 'tb1.sayweee.net' in TEST_URL:
            PageH5CommonOperations(self.page, self.header).pay_with_paypal(
                account='<EMAIL>',
                password='********'
            )

    def _empty_cart_verification(self):
        self.FE.ele(self.ele_home_cart).click()
        self.page.wait_for_timeout(5000)
        assert self.FE.ele("div[class^='text-center'] img").is_visible()
        assert self.FE.ele(u"//div[text()='Your cart is hungry']").is_enabled()
        assert self.FE.ele(u"//div[contains(text(),'You need to feed your cart with delicious food')]").is_enabled()
        assert len(self.FE.eles("#recommendTabFixed div")) == 1
        self.page.evaluate('window.scrollTo(0, document.body.scrollHeight/2)')

    def save_for_later_operations(self, product_index: int = 0):
        """
        购物车商品稍后再买操作，跳过带有free、gift标签的商品
        Args:
            product_index: 要操作的商品索引,默认为第一个商品
        Returns:
            bool: 操作是否成功
        """
        try:
            # 获取普通购物车商品
            log.info("开始获取购物车商品...")
            normal_cards = self.FE.eles(mweb_cart_ele.ele_cart_normal_card)
            log.info(f"找到购物车商品数量: {len(normal_cards) if normal_cards else 0}")

            if not normal_cards or len(normal_cards) <= product_index:
                log.info("没有找到可操作的商品")
                return False

            # 查找非free/gift商品
            valid_product_index = -1
            for i, card in enumerate(normal_cards):
                # 检查是否有free/gift标签
                has_free_label = False
                
                # 尝试多种可能的free标签选择器
                free_selectors = [
                    "//span[contains(text(), 'Free')]",
                    "//div[contains(text(), 'Free')]",
                    "//div[@data-testid='gift-label']",
                    "//div[contains(@class, 'gift-label')]"
                ]
                
                for selector in free_selectors:
                    try:
                        element = card.query_selector(selector)
                        if element and element.is_visible():
                            has_free_label = True
                            log.info(f"商品 {i} 有free标签，跳过")
                            break
                    except Exception as e:
                        log.debug(f"查找free标签时出错: {str(e)}")
                
                # 检查价格文本是否包含"Free"
                try:
                    price_element = card.query_selector("//div[@data-testid='wid-product-card-price']")
                    if price_element and "Free" in price_element.text_content():
                        has_free_label = True
                        log.info(f"商品 {i} 价格包含Free，跳过")
                except Exception as e:
                    log.debug(f"检查价格文本时出错: {str(e)}")
                
                # 如果不是free/gift商品，记录索引
                if not has_free_label:
                    valid_product_index = i
                    log.info(f"找到非free/gift商品，索引: {valid_product_index}")
                    if i >= product_index:  # 如果已经找到指定索引之后的非free商品，直接使用
                        break
            
            # 如果没有找到有效的非free商品，返回失败
            if valid_product_index == -1:
                log.info("没有找到非free/gift商品")
                return False
            
            # 使用找到的有效商品索引
            product_index = valid_product_index
            log.info(f"将对索引为 {product_index} 的商品执行稍后再买操作")

            # 确保商品卡片可见
            normal_cards[product_index].scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)

            # 使用data-testid定位稍后再买按钮
            save_later_btn = normal_cards[product_index].query_selector(mweb_cart_ele.ele_cart_normal_s4l)
            if not save_later_btn:
                log.info("尝试使用备用定位器...")
                save_later_btn = normal_cards[product_index].query_selector("//div[contains(text(), '稍后再买') or contains(text(), 'Save for later')]")

            if not save_later_btn:
                log.info("未找到稍后再买按钮")
                return False

            # 确保按钮可见和可点击
            if not save_later_btn.is_visible():
                log.info("稍后再买按钮不可见")
                return False

            # 点击按钮
            save_later_btn.click()
            self.page.wait_for_timeout(2000)

            # 验证商品是否已移动到稍后再买区域
            current_normal_cards = self.FE.eles(mweb_cart_ele.ele_cart_normal_card)
            if len(current_normal_cards) < len(normal_cards):
                log.info("商品已成功移动到稍后再买区域")
                return True

            log.info("商品数量未减少，稍后再买操作可能失败")
            return False

        except Exception as e:
            log.error(f"稍后再买操作发生异常: {str(e)}")
            import traceback
            log.error(traceback.format_exc())
            return False

    def move_to_cart_from_save_later(self, product_index: int = 0):
        """
        将稍后再买商品移回购物车
        Args:
            product_index: 要操作的商品索引,默认为第一个商品
        Returns:
            bool: 操作是否成功
        """
        try:
            # 获取稍后再买区域的商品
            save_later_cards = self.FE.eles("[data-testid='save-later-card']")
            if not save_later_cards or len(save_later_cards) <= product_index:
                log.info("稍后再买区域没有找到可操作的商品")
                return False

            # 点击移回购物车按钮
            move_to_cart_btn = save_later_cards[product_index].query_selector("[data-testid='btn-move-to-cart']")
            if not move_to_cart_btn:
                log.info("未找到移回购物车按钮")
                return False
            move_to_cart_btn.click()
            self.page.wait_for_timeout(1000)

            # 验证商品是否出现在普通购物车区域
            normal_cards = self.FE.eles("[data-testid='cart-normal-card']")
            if not normal_cards:
                log.info("普通购物车区域未显示商品")
                return False

            return True
        except Exception as e:
            log.error(f"移回购物车操作发生异常: {str(e)}")
            return False


def get_cart_products_prices(page: Page, cart_page):
    """获取购物车所有商品的价格"""
    try:
        # 确保页面加载完成
        page.wait_for_timeout(2000)

        # 等待购物车商品加载
        page.wait_for_selector(mweb_cart_ele.ele_cart_normal_card, timeout=5000)

        # 使用 Locator 获取所有商品价格元素
        price_elements = page.locator(mweb_cart_ele.ele_cart_normal_price).all()

        prices = []
        for index, price_ele in enumerate(price_elements):
            try:
                price_text = price_ele.text_content().strip()
                log.info(f"商品 {index + 1} 价格: {price_text}")
                prices.append(price_text)  # 修复：将价格添加到列表中
            except Exception as e:
                log.error(f"获取第 {index + 1} 个商品价格失败: {str(e)}")
                continue

        if not prices:
            log.warning("没有找到任何商品价格，尝试重新获取...")
            # 再次尝试使用不同的方式获取价格
            price_elements = page.query_selector_all(mweb_cart_ele.ele_cart_normal_price)
            for price_ele in price_elements:
                price_text = price_ele.text_content().strip()
                prices.append(price_text)

        log.info(f"获取到的所有价格: {prices}")
        return prices
    except Exception as e:
        log.error(f"获取购物车商品价格失败: {str(e)}")
        return []