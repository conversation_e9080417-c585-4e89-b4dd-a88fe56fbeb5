from playwright.sync_api import Page
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_recommendations_ele
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log

class MWebRecommendationPage(PageH5CommonOperations):
    """凑单页面类"""

    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        super().__init__(page, header)
        self.page = page
        self.header = header
        self.browser_context = browser_context
        # 直接进入指定页面
        self.page.goto(TEST_URL + page_url + "?joinEnki=true")
        self.page.wait_for_timeout(2000)
        # 关闭首页广告
        self.close_advertisement_in_homepage()

    def verify_recommendations_page(self):
        """
        验证凑单页面功能
        """
        # 验证凑单页面元素
        assert self.page.get_by_test_id("recommend-page").is_visible()
        assert self.page.get_by_test_id("primary-tab-list").is_visible()
        log.info("验证凑单页面元素成功")

    def operate_recommendations_page(self):
        """
        操作凑单页面
        """
        primary_tabs = self.page.get_by_test_id("primary-tab-list").get_by_role("button").all()
        for tab in primary_tabs:
            tab.click()
            self.page.wait_for_timeout(1000)

            secondary_tabs = self.page.get_by_test_id("secondary-tab-list").get_by_role("button").all()
            for sub_tab in secondary_tabs:
                sub_tab.click()
                self.page.wait_for_timeout(1000)

                # 收藏商品
                self.page.get_by_test_id("favorite-btn").first.click()

                # 加购商品直到达到目标金额
                self.add_items_until_target_amount()
        log.info("完成凑单页面操作")

    def add_items_until_target_amount(self, target_amount: float = 68):
        """
        加购商品直到达到目标金额
        """
        while True:
            self.page.get_by_test_id("add-to-cart-btn").first.click()
            self.page.wait_for_timeout(1000)

            total_amount = float(self.page.get_by_test_id("cart-total-amount").text_content().replace("$", ""))
            if total_amount >= target_amount:
                break

            # 金额小于目标金额时验证view detail按钮
            if self.page.get_by_test_id("view-detail-btn").is_visible():
                self.page.get_by_test_id("view-detail-btn").click()
                self.page.wait_for_url("**/promotion/trade-in")
                self.page.go_back()
        log.info(f"商品加购完成，当前金额: ${total_amount}")
