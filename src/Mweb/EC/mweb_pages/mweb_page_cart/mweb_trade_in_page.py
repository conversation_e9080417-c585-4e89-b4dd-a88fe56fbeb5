from playwright.sync_api import Page
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_trade_in_ele
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.config.weee.log_help import log

class MWebTradeInPage(PageH5CommonOperations):
    """换购页面类"""

    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        super().__init__(page, header)
        self.header = header
        self.browser_context = browser_context
        # 关闭首页广告
        self.close_advertisement_in_homepage()

    def verify_trade_in_status(self, cart_amount: float):
        """验证换购select状态"""
        select_button = self.page.get_by_test_id(mweb_trade_in_ele.ele_trade_in_select)
        select_status = select_button.get_attribute("data-status")
        if cart_amount < 68:
            # -- 待补充
            # 验证按钮不可点击和提示信息
            assert select_button.is_visible() and select_status == "btn-trade-in-add-to-cart-disabled"
            # assert self.page.get_by_test_id(mweb_trade_in_ele.ele_trade_in_shop_more).is_visible()
            # assert self.page.get_by_test_id(mweb_trade_in_ele.ele_trade_in_toast.format(68 - cart_amount)).is_visible()
            log.info("验证换购页面小于$68状态成功")
        else:
            # 验证select按钮可点击
            assert select_button.is_visible() and select_status == "btn-trade-in-add-to-cart-enabled"
            log.info("验证换购页面大于$68状态成功")

    def trade_in_card_assert(self,trade_in_cards,cart_amount):
        for item in trade_in_cards:
            assert item.get_by_test_id(mweb_trade_in_ele.ele_trade_in_card_image).is_visible()
            assert item.get_by_test_id(mweb_trade_in_ele.ele_trade_in_card_tag_off).is_visible()
            assert item.get_by_test_id(mweb_trade_in_ele.ele_trade_in_card_tags).is_visible()
            assert item.get_by_test_id(mweb_trade_in_ele.ele_trade_in_card_title).is_visible()
            assert item.get_by_test_id(mweb_trade_in_ele.ele_trade_in_card_price).is_visible()
            assert item.get_by_test_id(mweb_trade_in_ele.ele_trade_in_card_price_value).is_visible()
            assert item.get_by_test_id(mweb_trade_in_ele.ele_trade_in_card_base_price).is_visible()
            # 如果有fresh daiyl tag
            if item.get_by_test_id("wid-product-card-tag-freshly").is_visible():
                assert item.get_by_test_id("wid-product-card-brand").is_visible()
            self.verify_trade_in_status(cart_amount)

    def verify_select_limit(self):
        """验证换购商品选择限制"""
        # 选择5个换购商品
        select_btns = self.page.locator(mweb_trade_in_ele.ele_select_btn).all()
        for i in range(6):
            if i < 5:
                select_btns[i].click()
                self.page.wait_for_timeout(1000)
                # 验证进度条
                assert self.page.locator(mweb_trade_in_ele.ele_progress_bar).is_visible()
            else:
                # 验证第6个商品点击限制提示
                select_btns[i].click()
                assert self.page.locator(mweb_trade_in_ele.ele_limit_pop).is_visible()
        log.info("验证换购商品选择限制成功")
    def complete_trade_in_flow(self):
        """
        完成换购流程并返回购物车
        """
        # 验证最终状态
        assert self.page.get_by_test_id("shop-deals-btn").is_visible()
        self.page.get_by_test_id("shop-deals-btn").click()

        # 验证换购页面select按钮可用
        assert self.page.get_by_test_id("select-btn").is_enabled()

        # 返回购物车
        self.page.get_by_test_id("go-to-cart-btn").click()

        # 验证购物车换购模块状态
        assert self.page.get_by_test_id("trade-in-card-add").is_enabled()
        log.info("完成换购流程并返回购物车成功")

    def go_to_cart(self):
        """返回购物车"""
        self.page.locator(mweb_trade_in_ele.ele_go_to_cart).click()
        self.page.wait_for_timeout(2000)
        log.info("返回购物车成功")
    def go_to_recommendations_page(self):
        """
        从换购页面进入凑单页面
        """
        self.page.get_by_test_id("shop-more-btn").click()
        self.page.wait_for_url("**/promotion/recommend")
        log.info("成功进入凑单页面")