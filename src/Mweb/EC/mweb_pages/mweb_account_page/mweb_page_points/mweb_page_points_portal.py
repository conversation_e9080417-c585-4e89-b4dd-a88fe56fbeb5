from playwright.sync_api import Page, TimeoutError
from src.Mweb.EC.mweb_ele.mweb_account.mweb_points.mweb_points_portal_ele import *
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log
from src.config.base_config import TEST_URL

class MWebPointsPortalPage(PageH5CommonOperations):
    """
    积分Portal页面操作类 - MWeb版本
    这个类用于操作/account/perks?ws=me_page 页面上的相关功能。
    """

    def __init__(self, page: Page, header, browser_context, page_url: str = "/account/perks?ws=me_page"):
        """
        初始化积分Portal页面
        :param page: Playwright页面对象
        :param header: 请求头
        :param browser_context: 浏览器上下文
        :param page_url: portal页面URL路径，默认为/account/perks?ws=me_page
        """
        # 调用父类的初始化方法，设置页面和请求头
        super().__init__(page, header)
        self.bc = browser_context  # 保存浏览器上下文，便于后续操作
        # 直接进入积分portal页面
        self.page.goto(TEST_URL + "/" + page_url)
        # 等待页面加载完成，确保后续操作不会因为页面未加载而失败
        self.page.wait_for_load_state("load")
        log.info(f"成功进入积分portal页面: {TEST_URL}/{page_url}")

    def click_upgrade_points(self):
        """
        点击积分portal页面的升级积分按钮
        """
        try:
            # 尝试点击升级按钮
            self.FE.ele(ele_points_portal_bronze_upgrade_btn).click()
            log.info("成功点击积分portal页面的升级积分按钮")
        except TimeoutError:
            # 如果找不到按钮，记录错误日志并抛出异常
            log.error("未找到积分portal页面的升级积分按钮")
            raise


    def click_add_points(self):
        """
        点击积分portal页面的“Add points”按钮
        """
        try:
                 # 尝试点击“Add points”按钮
            self.FE.ele(ele_points_portal_add_points_btn).click()
            log.info("成功点击积分portal页面的Add points按钮")
        except TimeoutError:
            # 如果找不到按钮，记录错误日志并抛出异常
            log.error("未找到积分portal页面的Add points按钮")
            raise


  
