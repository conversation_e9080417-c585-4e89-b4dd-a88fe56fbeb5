from playwright.sync_api import Page, TimeoutError
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_common_page import MWebCommonPage
from src.Mweb.EC.mweb_ele.mweb_account.mweb_points.mweb_points_portal_ele import (
    ele_points_portal_add_points_btn,
    ele_points_checkout_button,
    ele_points_place_order_button,
    ele_points_payment_box,
    ele_points_paypal_payment,
    ele_points_pay_method_confirm,
)
from src.config.weee.log_help import log
from src.config.base_config import TEST_URL

class MWebGoldPointsBuyPage(MWebCommonPage):
    """
    普通积分购买页面操作类 - MWeb版本
    这个类用于操作普通积分购买流程，包括点击Add points、Checkout和Place Order等按钮。
    """

    def __init__(self, page: Page, header, browser_context, page_url: str = "/account/perks?ws=me_page"):
        """
        初始化普通积分购买页面
        :param page: Playwright页面对象
        :param header: 请求头
        :param browser_context: 浏览器上下文
        :param page_url: portal页面URL路径，默认为/account/perks?ws=me_page
        """
        # 调用父类的初始化方法，设置页面和请求头
        super().__init__(page, header)
        self.bc = browser_context  # 保存浏览器上下文，便于后续操作
        # 直接进入积分portal页面
        self.page.goto(TEST_URL + "/" + page_url)
        # 等待页面加载完成，确保后续操作不会因为页面未加载而失败
        self.page.wait_for_load_state("load")
        log.info(f"成功进入积分portal页面: {TEST_URL}/{page_url}")

    def click_add_points(self):
        """
        点击积分portal页面的"Add points"按钮
        """
        try:
            # 尝试点击"Add points"按钮
            self.FE.ele(ele_points_portal_add_points_btn).click()
            log.info("成功点击积分portal页面的Add points按钮")
        except TimeoutError:
            # 如果找不到按钮，记录错误日志并抛出异常
            log.error("未找到积分portal页面的Add points按钮")
            raise

    def click_points_list_checkout(self):
        """
        点击积分列表页面的Checkout按钮
        """
        try:
            # 尝试点击Checkout按钮
            self.FE.ele(ele_points_checkout_button).click()
            log.info("成功点击积分列表页面的Checkout按钮")
        except TimeoutError:
            log.error("未找到积分列表页面的Checkout按钮")
            raise

    def click_points_checkout_place_order(self):
        """
        点击积分结算页面的Place Order按钮
        """
        try:
            # 尝试点击Place Order按钮
            self.FE.ele(ele_points_place_order_button).click()
            log.info("成功点击积分结算页面的Place Order按钮")
        except TimeoutError:
            log.error("未找到积分结算页面的Place Order按钮")
            raise

    def select_payment_method(self):
        """
        在结算页面点击选择支付模块按钮（复用礼品卡的方法）
        """
        try:
            self.FE.ele(ele_points_payment_box).click()
            log.info("成功点击支付模块按钮")
        except TimeoutError:
            log.error("未找到支付模块按钮")
            raise

    def select_paypal_payment(self):
        """
        选择 PayPal 支付方式（复用礼品卡的方法）
        """
        try:
            self.FE.ele(ele_points_paypal_payment).click()
            log.info("成功选择 PayPal 支付方式")
        except TimeoutError:
            log.error("未找到 PayPal 支付方式按钮")
            raise

    def confirm_payment_method(self):
        """
        点击确认支付方式按钮（复用礼品卡的方法）
        """
        try:
            self.FE.ele(ele_points_pay_method_confirm).click()
            log.info("成功点击确认支付方式按钮")
        except TimeoutError:
            log.error("未找到确认支付方式按钮")
            raise
