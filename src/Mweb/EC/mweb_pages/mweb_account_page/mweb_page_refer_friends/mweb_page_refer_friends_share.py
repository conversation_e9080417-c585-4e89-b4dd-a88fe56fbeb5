from playwright.sync_api import Page, TimeoutError
from src.Mweb.EC.mweb_ele.mweb_account.mweb_refer_friends.mweb_refer_friends_portal_ele import *
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log
from src.config.base_config import TEST_URL


class MWebReferFriendsSharePage(PageH5CommonOperations):
    """推荐朋友Portal页面操作类 - MWeb版本"""
    
    def __init__(self, page: Page, header, browser_context, page_url: str = "account/referral?ws=me_page&fullscreen=1"):
        """
        初始化推荐朋友Portal页面
        :param page: Playwright页面对象
        :param header: 请求头
        :param browser_context: 浏览器上下文
        :param page_url: portal页面URL路径
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入refer friends portal页面
        self.page.goto(TEST_URL + "/" + page_url)
        # 等待页面加载完成
        self.page.wait_for_load_state("load")
        log.info(f"成功进入refer friends portal页面: {TEST_URL}/{page_url}")

    def click_share_the_link(self):
        """
        点击Share the link按钮
        """
        try:
            self.FE.ele(ele_share_the_link_button).click()
            log.info("成功点击Share the link按钮")
        except TimeoutError:
            log.error("未找到Share the link按钮")
            raise

    def click_copy_link(self):
        """
        在share弹窗中点击Copy link按钮
        """
        try:
            self.FE.ele(ele_copy_link_button).click()
            log.info("成功点击Copy link按钮")
        except TimeoutError:
            log.error("未找到Copy link按钮")
            raise

    def click_get_image(self):
        """
        在share弹窗中点击Get Image按钮
        """
        try:
            self.FE.ele(ele_get_image_button).click()
            log.info("成功点击Get Image按钮")
        except TimeoutError:
            log.error("未找到Get Image按钮")
            raise

    def click_qr_code_entrance(self):
        """
        点击邀请好友portal主页面的二维码入口
        """
        try:
            self.FE.ele(ele_qr_code_entrance).click()
            log.info("成功点击二维码入口")
        except TimeoutError:
            log.error("未找到二维码入口")
            raise

    def click_qr_code_modal_close(self):
        """
        点击二维码弹窗内的关闭按钮(X)
        """
        try:
            self.FE.ele(ele_qr_code_modal_close_button).click()
            log.info("成功点击二维码弹窗关闭按钮")
        except TimeoutError:
            log.error("未找到二维码弹窗关闭按钮")
            raise


