from playwright.sync_api import Page, TimeoutError
from src.Mweb.EC.mweb_ele.mweb_account.mweb_refer_friends.mweb_refer_friends_signup_ele import *
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log
from src.config.base_config import TEST_URL
from faker import Faker


class MWebReferFriendsSignupPage(PageH5CommonOperations):
    """推荐朋友注册页面操作类 - MWeb版本"""
    
    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        """
        初始化推荐朋友注册页面
        :param page: Playwright页面对象
        :param header: 请求头
        :param browser_context: 浏览器上下文
        :param page_url: 注册页面URL路径（可选，如果不提供则不自动跳转）
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 如果提供了page_url，则自动跳转；否则等待手动跳转
        if page_url:
            self.page.goto(TEST_URL + "/" + page_url)
            # 等待页面加载完成
            self.page.wait_for_load_state("load")
            log.info(f"成功进入页面: {TEST_URL}/{page_url}")
        else:
            log.info("页面对象已初始化，等待手动跳转")

    def init_refer_friends_landing_page(self, landing_url: str):
        """
        初始化邀请好友落地页
        :param landing_url: 落地页URL路径
        """
        try:
            self.page.goto(f"{TEST_URL}/{landing_url}")
            self.page.wait_for_load_state("load")
            log.info(f"成功进入邀请好友落地页: {landing_url}")
        except Exception as e:
            log.error(f"进入邀请好友落地页失败: {str(e)}")
            raise

    def click_account_logout(self):
        """
        点击退出账号按钮
        """
        try:
            self.FE.ele(ele_account_logout_button).click()
            log.info("成功点击退出账号按钮")
        except TimeoutError:
            log.error("未找到退出账号按钮")
            raise

    def fill_zipcode_95008(self):
        """
        找到邮编输入框，清空里面的值，然后输入95008
        """
        try:
            # 获取邮编输入框元素
            zipcode_input = self.FE.ele(ele_zipcode_input)
            # 先清空输入框内容
            zipcode_input.fill("")
            log.info("已清空邮编输入框内容")
            # 输入95008
            zipcode_input.fill("95008")
            log.info("成功输入邮编: 95008")
        except TimeoutError:
            log.error("未找到邮编输入框")
            raise

    def click_redeem_offer_button(self):
        """
        点击获取优惠券按钮
        """
        try:
            self.FE.ele(ele_redeem_offer_button).click()
            log.info("成功点击获取优惠券按钮")
        except TimeoutError:
            log.error("未找到获取优惠券按钮")
            raise

    def input_dynamic_email(self):
        """
        找到邮箱输入框，然后输入一个动态生成的邮箱。
        邮箱格式为：时间戳 + 随机字符串 + '@sayweee.com'
        例如：<EMAIL>
        """
        import datetime  # 导入datetime模块用于获取当前时间
        import random
        import string

        try:
            log.info("开始生成动态邮箱...")
            
            # 获取当前时间，并格式化为指定的字符串
            log.info("正在获取当前时间...")
            now = datetime.datetime.now()
            log.info(f"当前时间: {now}")
            
            # %Y: 年, %m: 月, %d: 日, %H: 小时, %M: 分钟
            log.info("正在格式化时间字符串...")
            email_prefix = now.strftime("%Y%m%d%H%M")
            log.info(f"时间前缀: {email_prefix}")
            
            # 生成随机字符串（参考项目中的最佳实践）
            log.info("正在生成随机字符串...")
            random_string = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
            log.info(f"随机字符串: {random_string}")
            
            # 拼接邮箱（时间戳 + 随机字符串 + 域名）
            log.info("正在拼接完整邮箱...")
            dynamic_email = f"{email_prefix}_{random_string}@sayweee.com"
            log.info(f"生成的完整邮箱: {dynamic_email}")
            
            # 验证邮箱格式
            log.info("正在验证邮箱格式...")
            if "@" in dynamic_email and len(dynamic_email) > 15:
                log.info("✅ 邮箱格式验证通过")
            else:
                log.warning(f"⚠️ 邮箱格式可能有问题: {dynamic_email}")
            
            # 获取邮箱输入框元素
            log.info("正在定位邮箱输入框元素...")
            log.info(f"使用的选择器: {ele_email_input}")
            
            email_input = self.FE.ele(ele_email_input)
            log.info(f"邮箱输入框元素定位成功: {email_input}")
            
            # 检查元素状态
            log.info("正在检查邮箱输入框元素状态...")
            try:
                is_visible = email_input.is_visible()
                log.info(f"邮箱输入框是否可见: {is_visible}")
                
                is_enabled = email_input.is_enabled()
                log.info(f"邮箱输入框是否启用: {is_enabled}")
                
                current_value = email_input.input_value()
                log.info(f"邮箱输入框当前值: {current_value}")
                
            except Exception as check_e:
                log.warning(f"检查元素状态时出现警告: {str(check_e)}")
            
            # 先清空输入框内容
            log.info("正在清空邮箱输入框内容...")
            email_input.fill("")
            log.info("邮箱输入框内容已清空")
            
            # 填入动态邮箱
            log.info(f"正在输入动态邮箱: {dynamic_email}")
            email_input.fill(dynamic_email)
            log.info("动态邮箱输入完成")
            
            # 验证输入是否成功
            log.info("正在验证输入是否成功...")
            try:
                actual_value = email_input.input_value()
                log.info(f"邮箱输入框实际值: {actual_value}")
                
                if actual_value == dynamic_email:
                    log.info("✅ 动态邮箱输入验证成功！")
                else:
                    log.error(f"❌ 动态邮箱输入验证失败！期望: {dynamic_email}, 实际: {actual_value}")
                    
            except Exception as verify_e:
                log.error(f"验证输入时出错: {str(verify_e)}")
            
            log.info(f"成功输入动态邮箱: {dynamic_email}")
            return dynamic_email  # 返回生成的邮箱，方便后续使用
            
        except TimeoutError as te:
            log.error(f"❌ 未找到邮箱输入框 (TimeoutError): {str(te)}")
            log.error(f"使用的选择器: {ele_email_input}")
            raise
        except Exception as e:
            log.error(f"❌ 生成或输入动态邮箱时出现未知错误: {str(e)}")
            log.error(f"错误类型: {type(e).__name__}")
            raise

    def click_next_button(self):
        """
        点击Next按钮
        """
        try:
            self.FE.ele(ele_next_button).click()
            log.info("成功点击Next按钮")
        except TimeoutError:
            log.error("未找到Next按钮")
            raise

    def get_coupon_title(self):
        """
        获取优惠券标题文本
        :return: 优惠券标题文本
        """
        try:
            coupon_title_element = self.FE.ele(ele_coupon_title)
            title_text = coupon_title_element.text_content()
            log.info(f"成功获取优惠券标题: {title_text}")
            return title_text
        except TimeoutError:
            log.error("未找到优惠券标题")
            raise
