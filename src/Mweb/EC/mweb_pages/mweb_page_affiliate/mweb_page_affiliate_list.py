from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from src.Mweb.EC.mweb_ele.mweb_affiliate_list.mweb_affiliate_list_ele import ele_affiliate_add_all_to_cart, ele_affiliate_explore_more


class PageH5AffiliateListOperations(PageH5CommonOperations):
    """
    返利联盟分享清单页面操作类
    包含：
    1. 进入分享链接
    2. 一键加购
    3. 探索更多
    4. URL校验
    """
    
    def __init__(self, page: Page, header):
        super().__init__(page, header)
        self.share_url = "https://www.sayweee.com/en/share/affiliate-list/4281?type=shareBook&sharer_id=7216895&share_id=4281&lang=en&referral_id=7216895&utm_source=copyLink"
        self.target_url = "https://www.sayweee.com/en/category/sale"

    def enter_affiliate_list(self):
        """
        进入返利联盟分享清单页面
        """
        try:
            self.page.goto(self.share_url)
            self.page.wait_for_load_state("networkidle")
            log.info("成功进入返利联盟分享清单页面")
        except Exception as e:
            log.error(f"进入返利联盟分享清单页面失败: {str(e)}")
            raise

    def add_all_to_cart(self):
        """
        点击一键加购按钮
        """
        try:
            self.FE.ele(ele_affiliate_add_all_to_cart).click()
            self.page.wait_for_timeout(2000)  # 等待加购操作完成
            log.info("成功点击一键加购按钮")
        except Exception as e:
            log.error(f"点击一键加购按钮失败: {str(e)}")
            raise

    def scroll_and_explore_more(self):
        """
        下滑页面并点击探索更多按钮
        """
        try:
            # 下滑页面
            self.page.evaluate('window.scrollTo(0, document.body.scrollHeight)')
            self.page.wait_for_timeout(20000)  # 增加等待时间
            
            # 确保按钮在可视区域内
            explore_more_button = self.FE.ele(ele_affiliate_explore_more)
            explore_more_button.scroll_into_view_if_needed()
            self.page.wait_for_selector(ele_affiliate_explore_more, state="visible", timeout=10000)  # 等待滚动完成
            
            # 点击探索更多按钮
            explore_more_button.click()
            # 增加页面跳转等待时间
            self.page.wait_for_load_state("load")
            self.page.wait_for_timeout(5000)  # 额外等待5秒确保页面跳转完成
            
            log.info("成功点击探索更多按钮")
        except Exception as e:
            log.error(f"点击探索更多按钮失败: {str(e)}")
            raise

    def verify_target_url(self):
        """
        校验跳转后的URL是否正确
        使用更灵活的方式验证URL，只验证基础URL部分
        """
        try:
            current_url = self.page.url
            # 移除查询参数，只比较基础URL
            current_base_url = current_url.split('?')[0]
            target_base_url = self.target_url.split('?')[0]
            
            assert current_base_url == target_base_url, f"URL校验失败，期望基础URL: {target_base_url}, 实际基础URL: {current_base_url}"
            log.info(f"URL验证成功，当前URL: {current_url}")
        except Exception as e:
            log.error(f"URL验证失败: {str(e)}")
            raise

    def affiliate_list_operations(self):
        """
        执行完整的返利联盟分享清单页面操作流程
        """
        try:
            # 1. 进入分享链接
            self.enter_affiliate_list()
            
            # 2. 点击一键加购
            self.add_all_to_cart()
            
            # 3. 下滑页面并点击探索更多
            self.scroll_and_explore_more()
            
            # 4. 校验跳转后的URL
            self.verify_target_url()
            
            log.info("返利联盟分享清单页面操作流程执行完成")
        except Exception as e:
            log.error(f"返利联盟分享清单页面操作流程执行失败: {str(e)}")
            raise 