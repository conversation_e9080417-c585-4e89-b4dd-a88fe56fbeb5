"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_page_address.py
@Description    :  H5地址页面相关操作
@CreateTime     :  2025/1/20 14:00
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/1/20 14:00
"""
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class MWebAddressPage(PageH5CommonOperations):
    """H5地址页面类"""
    
    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        super().__init__(page, header)
        self.bc = browser_context
        if page_url:
            self.page.goto(TEST_URL + page_url)
            self.page.wait_for_timeout(3000)

    def verify_fresh_order_change_address_entry(self, order_id: str) -> bool:
        """验证生鲜订单显示修改地址入口"""
        try:
            # 跳转到订单详情页
            self.page.goto(TEST_URL + f"/order/detail/{order_id}")
            self.page.wait_for_timeout(3000)
            
            # 检查修改地址按钮是否存在
            change_address_btn = self.page.get_by_test_id("btn-order-detail-delivery-address-edit")
            if change_address_btn.is_visible():
                log.info("生鲜订单修改地址入口验证成功")
                return True
            else:
                log.error("生鲜订单未显示修改地址入口")
                return False
                
        except Exception as e:
            log.error(f"验证生鲜订单修改地址入口失败: {str(e)}")
            return False

    def verify_cross_region_address_change_error(self, order_id: str) -> bool:
        """验证跨区域修改地址提示错误"""
        try:
            # 跳转到订单详情页
            self.page.goto(TEST_URL + f"/order/detail/{order_id}")
            self.page.wait_for_timeout(3000)
            
            # 点击修改地址按钮
            change_address_btn = self.page.get_by_test_id("btn-change-address")
            if not change_address_btn.is_visible():
                log.error("修改地址按钮不可见")
                return False
            
            change_address_btn.click()
            self.page.wait_for_timeout(2000)
            
            # 跳转到地址修改页面
            self.page.goto(TEST_URL + f"/account/address/order/{order_id}")
            self.page.wait_for_timeout(3000)
            
            # 选择一个跨区域地址
            cross_region_address = self.page.locator("[data-testid*='address-item']").first
            if cross_region_address.is_visible():
                cross_region_address.click()
                self.page.wait_for_timeout(2000)
                
                # 检查是否显示错误提示
                error_message = self.page.get_by_test_id("wid-order-address-content-address-list-item-error")
                if error_message.is_visible():
                    log.info("跨区域修改地址错误提示验证成功")
                    return True
                else:
                    log.error("未显示跨区域修改地址错误提示")
                    return False
            else:
                log.error("未找到可选择的地址")
                return False
                
        except Exception as e:
            log.error(f"验证跨区域修改地址错误失败: {str(e)}")
            return False