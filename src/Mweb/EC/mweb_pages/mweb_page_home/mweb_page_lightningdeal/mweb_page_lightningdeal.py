# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON><PERSON><PERSON> <PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  mweb_page_lightningdeal.py
@Description    :  移动端秒杀页面操作类
@CreateTime     :  2025/7/14 03:13
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/7/14 03:13
"""

from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.config.base_config import TEST_URL
from src.common.commfunc import empty_cart
from src.config.weee.log_help import log


class MWebLightningDealPage(PageH5CommonOperations):
    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入秒杀页面
        self.page.goto(TEST_URL + page_url + "?joinEnki=true")
        self.page.wait_for_timeout(5000)
        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        # 关闭首页广告
        self.close_advertisement_in_homepage()

    def close_advertisement_in_homepage(self):
        """关闭首页广告弹窗"""
        if self.page.locator("//img[contains(@aria-label, 'close button')]").all():
            self.page.locator("//img[contains(@aria-label, 'close button')]").click()

    def check_lightning_deal_page(self):
        """校验秒杀页面基本元素"""
        from src.Mweb.EC.mweb_ele.mweb_home import mweb_lightningdeal_ele

        # 等待页面加载
        self.page.wait_for_load_state("networkidle", timeout=60000)

        # 校验页面标题（可选，因为页面结构可能变化）
        title_element = self.FE.ele(mweb_lightningdeal_ele.ele_lightning_deal_title)
        if title_element and title_element.is_visible():
            log.info("秒杀页面标题可见")
        else:
            log.warning("秒杀页面标题不可见，可能页面结构发生变化")

        # 校验分享按钮（这是关键元素）
        share_button = self.FE.ele(mweb_lightningdeal_ele.ele_share)
        if share_button and share_button.is_visible():
            log.info("分享按钮可见")
            return True
        else:
            log.warning("分享按钮不可见，可能当前没有秒杀活动")
            return False

    def has_share_button(self):
        """检查是否有分享按钮"""
        from src.Mweb.EC.mweb_ele.mweb_home import mweb_lightningdeal_ele

        share_button = self.FE.ele(mweb_lightningdeal_ele.ele_share)
        return share_button and share_button.is_visible()
