from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.common.commfunc import empty_cart
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from src.Mweb.EC.mweb_ele.mweb_home.mweb_porder.mweb_address import mweb_address_ele


class MWebPageAddress(PageH5CommonOperations):
    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        super().__init__(page, header)
        self.bc = browser_context

        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        # 直接进入指定页面
        self.page.goto(TEST_URL + page_url + "?joinEnki=true")
        self.page.wait_for_timeout(5000)
        # 关闭首页广告
        self.close_advertisement_in_homepage()

    def close_advertisement_in_homepage(self):
        if self.page.locator(mweb_address_ele.close_advertisement).all():
            self.page.locator(mweb_address_ele.close_advertisement).click()

    def fill_delivery_address_form(self, address,first_name: str = "Test",
                                   last_name: str = "Automation",
                                   phone: str = "3456785432"):
        """
        填写配送地址表单的公共方法
        Args:
            first_name: 名字，默认"Test"
            last_name: 姓氏，默认"Automation"
            phone: 电话号码，默认"3456785432"
        Returns:
            bool: 填写是否成功
        """
        try:

            log.info("开始填写配送地址表单")

            # 输入名字
            first_name_input = self.page.get_by_test_id(mweb_address_ele.address_first_name)
            if not first_name_input.is_visible():
                log.error("名字输入框不可见")
                return False
            first_name_input.clear()  # 先清空输入框
            first_name_input.fill(first_name)
            log.info(f"已输入名字: {first_name}")

            # 输入姓氏
            last_name_input = self.page.get_by_test_id(mweb_address_ele.address_last_name)
            if not last_name_input.is_visible():
                log.error("姓氏输入框不可见")
                return False
            last_name_input.clear()  # 先清空输入框
            last_name_input.fill(last_name)
            log.info(f"已输入姓氏: {last_name}")

            # 输入电话
            phone_input = self.page.get_by_test_id(mweb_address_ele.address_phone)
            if not phone_input.is_visible():
                log.error("电话输入框不可见")
                return False
            phone_input.clear()  # 先清空输入框
            phone_input.fill(phone)
            log.info(f"已输入电话: {phone}")

            # 验证街道、城市、zipcode 已经默认填上了
            log.info("验证街道、城市、zipcode已默认填写")
            street_field = self.page.get_by_test_id(mweb_address_ele.address_street)
            city_field = self.page.get_by_test_id(mweb_address_ele.address_city)
            zipcode_field = self.page.get_by_test_id(mweb_address_ele.address_zipcode)

            # 如果街道地址没有被填上值，点击街道字段重新进入地址输入流程
            if not street_field.input_value():
                log.warning("街道地址未自动填写，点击街道字段重新输入地址")
                street_field.click()
                self.page.wait_for_timeout(2000)  # 等待页面跳转到delivery address pop

                # 重新输入地址并选择联想地址
                if not self.input_address_and_select_suggestion(address=address):
                    log.error("重新输入地址失败")
                    return False

                # 重新验证街道地址是否填写
                street_field = self.page.get_by_test_id(mweb_address_ele.address_street)
                if not street_field.input_value():
                    log.error("重新输入地址后，街道地址仍未自动填写")
                    return False
                log.info("重新输入地址成功，街道地址已自动填写")

            if not city_field.input_value():
                log.error("城市未自动填写")
                return False
            if not zipcode_field.input_value():
                log.error("邮编未自动填写")
                return False

            log.info("街道、城市、邮编已自动填写完成")

            # 填写备注
            note_input = self.page.get_by_test_id(mweb_address_ele.address_note)
            if not note_input.is_visible():
                log.error("备注输入框不可见")
                return False
            note_input.clear()  # 先清空输入框
            note_input.fill("h5 UI自动化")
            log.info("配送地址表单填写成功")
            return True

        except Exception as e:
            log.error(f"填写配送地址表单时发生异常: {str(e)}")
            return False

    def input_address_and_select_suggestion(self, address: str ):
        """
        在delivery address pop里输入地址并选择第一个联想地址
        Args:
            address: 要输入的地址，默认"18607 Bothell Way NE, Bothell, WA 98011"
        Returns:
            bool: 操作是否成功
        """
        try:
            log.info("开始输入地址并选择联想地址")

            # 在delivery address pop里输入地址
            log.info(f"在delivery address pop里输入地址: {address}")
            self.page.get_by_test_id(mweb_address_ele.delivery_address_input).click()
            street_input = self.page.get_by_test_id(mweb_address_ele.delivery_address_input)
            if not street_input.is_visible():
                log.error("街道地址输入框不可见")
                return False

            street_input.clear()  # 先清空输入框
            street_input.fill(address)
            self.page.wait_for_timeout(2000)  # 等待地址联想
            log.info(f"已输入地址: {address}")

            # 点击第一个联想地址
            log.info("点击第一个联想地址")

            matched_address = self.page.get_by_test_id(mweb_address_ele.address_first_matched).all()
            # first_matched_address = self.page.locator(u"//div[@id='streetAddressList']//div[1]//i")
            first_matched_address = matched_address[0]
            if not first_matched_address.is_visible():
                log.error("第一个联想地址不可见")
                return False

            first_matched_address.click()
            self.page.wait_for_timeout(2000)  # 等待页面跳转
            log.info("成功点击第一个联想地址，进入Delivery Info页面")

            return True

        except Exception as e:
            log.error(f"输入地址并选择联想地址时发生异常: {str(e)}")
            return False

    def add_address_from_home(self,address):
        """
        从首页添加新地址
        """
        try:
            log.info("开始从首页添加新地址")

            # 1. 点击首页地址位置
            zipcode_btn = self.page.get_by_test_id(mweb_address_ele.home_zipcode_button)
            if not zipcode_btn.is_visible():
                log.error("首页地址按钮不可见")
                return False
            zipcode_btn.click()
            self.page.wait_for_timeout(2000)
            # 如果没有原始地址，可以直接编辑新增地址
            if self.page.get_by_test_id(mweb_address_ele.edit_address_button).is_visible():
                self.page.get_by_test_id(mweb_address_ele.edit_address_button).click()
                self.page.wait_for_timeout(2000)
                # 输入地址，选择第一个联想地址
                if not self.input_address_and_select_suggestion(address):
                    log.error("输入地址并选择联想地址失败")
                    return False


            else:
                # 2. 点击新增地址按钮
                add_address_btn = self.page.get_by_test_id(mweb_address_ele.add_address_button)
                if not add_address_btn.is_visible():
                    log.error("新增地址按钮不可见")
                    return False
                add_address_btn.click()
                self.page.wait_for_timeout(2000)

            # 3. 填写地址表单
            if not self.fill_delivery_address_form(address):
                log.error("填写配送地址表单失败")
                return False
            # 滚动到save 按钮位置
            self.page.get_by_test_id(mweb_address_ele.address_save_button).scroll_into_view_if_needed()

            # 4. 点击保存，回到首页
            save_btn = self.page.get_by_test_id(mweb_address_ele.address_save_button)
            if not save_btn.is_visible():
                log.error("保存按钮不可见")
                return False
            save_btn.click()
            self.page.wait_for_timeout(2000)

            log.info("成功从首页添加新地址")
            return True
        except Exception as e:
            log.error(f"从首页添加新地址时发生异常: {str(e)}")
            return False


    def delete_address_from_home(self):
        """
        删除所有地址
        Returns:
            bool: 操作是否成功
        """
        try:
            log.info("开始删除所有地址")
            
            while True:
                # 1. 点击首页地址位置
                zipcode_btn = self.page.get_by_test_id(mweb_address_ele.home_zipcode_button)
                if not zipcode_btn.is_visible():
                    log.error("首页地址按钮不可见")
                    return False
                zipcode_btn.click()
                self.page.wait_for_timeout(2000)
                
                # 2. 检查是否有地址卡片
                address_cards = self.page.get_by_test_id(mweb_address_ele.address_card).all()
                if not address_cards:
                    log.info("没有地址卡片，删除完成")
                    # 关闭pop，回到首页
                    self.page.get_by_test_id(mweb_address_ele.close_delivery_dialog).click()
                    return True
                
                # 3. 点击第一个地址卡片的编辑按钮
                first_card = address_cards[0]
                edit_btn = first_card.locator(mweb_address_ele.address_card_edit_icon)
                if not edit_btn.is_visible():
                    log.error("编辑按钮不可见")
                    return False
                edit_btn.click()
                self.page.wait_for_timeout(2000)
                
                # 4. 点击删除按钮
                delete_btn = self.page.get_by_test_id("btn-delete-address")
                if not delete_btn.is_visible():
                    log.error("删除按钮不可见")
                    return False
                delete_btn.click()
                self.page.wait_for_timeout(2000)
                
                # 5. 确认删除弹窗并点击确认
                if not self.page.locator("//div[contains(@class,'RemoveAddress_removeWrapper')]").is_visible():
                    log.error("删除确认弹窗未出现")
                    return False
                
                confirm_btn = self.page.get_by_test_id("btn-remove-address")
                if not confirm_btn.is_visible():
                    log.error("确认删除按钮不可见")
                    return False
                confirm_btn.click()
                self.page.wait_for_timeout(3000)
                
                log.info("成功删除一个地址")
                
        except Exception as e:
            log.error(f"删除地址时发生异常: {str(e)}")
            return False