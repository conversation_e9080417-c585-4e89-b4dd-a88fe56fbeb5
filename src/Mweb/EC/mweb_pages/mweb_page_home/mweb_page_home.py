from typing import Literal

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_home import mweb_home_ele
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.common.commfunc import empty_cart
from src.common.commonui import home_init_h5, scroll_one_page_until, scroll_n_page
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log

class MWebPageHome(PageH5CommonOperations):
    ele_home_div_input_search_item = u"div[aria-label='What are you looking for?']"
    ele_home_input_item = u"input[class^='box-border']"
    ele_add_to_cart_in_search_page = u"i[data-role='addButtonPlusIcon']"
    ele_return_to_home = u"i[class='icon iconfont iconBack']"
    ele_not_found_keyword = u"//p[contains(text(),'我们会根据用户建议每周上架新品，敬请关注哦')]"

    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        super().__init__(page, header)
        self.bc = browser_context

        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        # 直接进入指定页面
        self.page.goto(TEST_URL + "?joinEnki=true")
        self.page.wait_for_timeout(5000)
        # 关闭首页广告
        self.close_advertisement_in_homepage()


    def click_category(self, category_name: str):
        if category_name not in  ["Alcohol", "Fruits", "Ching Ming Festival"]:
            # 不知道为什么，alcohol的结构与别的category不一样
            self.page.locator(f'//span[text()="{category_name}"]').all()[0].click()
        else:
            self.page.locator(f'//span[text()="{category_name}"]').all()[1].click()
        self.page.wait_for_timeout(5000)

    def go_to_special_category_from_home(self, special_category):
        """
        从首页点击特定分类进入分类页

        Args:
            special_category: 特定分类的元素选择器
        """
        # 点击特定分类进入分类页
        self.FE.ele(special_category).click()
        # 等待页面加载完成
        self.page.wait_for_timeout(2000)
    def m_close_time_banner(self):
        """
        close time banner on home page
        """
        if self.page.get_by_test_id("btn-close-promo-bar").all():
            self.page.get_by_test_id("btn-close-promo-bar").click()
            self.page.wait_for_timeout(2000)

    def m_home_switch_specific_store(self, store_name: Literal["japanese", "chinese", "korean", "vietnamese", "filipino", "explorer"]):
        """
        switch stores by specific store name
        """
        self.page.get_by_test_id("mod-store-list-container").locator("//span").click()
        self.page.get_by_test_id(f"wid-store-line-{store_name}").click()
        self.page.wait_for_timeout(5000)



    def m_download_app(self, platform_name: Literal["ios", "android"]):
        self.page.wait_for_timeout(5000)
        self.page.goto("https://www.sayweee.com/company/home")
        self.page.wait_for_timeout(5000)
        scroll_one_page_until(self.page, "div[data-framer-name='App store button']")
        if platform_name == "ios":
            with self.bc.expect_page() as download_page:
                self.page.locator("div[data-framer-name='App store button']").click()
                self.page.wait_for_timeout(3000)
            new_page: Page = download_page.value
            assert new_page.locator("div[data-framer-name='app_store_btn'][name='app_store_btn']").all()
            self.page.locator("div[data-framer-name='App store button']").click()
        else:
            log.info("输入的platform名称不对")

    def m_change_zipcode(self):
        """
        输入zipcode并点击confirm
        """
        self.page.get_by_test_id("btn-change-delivery").click()
        self.page.wait_for_timeout(5000)
        self.page.get_by_test_id("wid-address-card").all()[0].click()
        self.page.wait_for_load_state("networkidle", timeout=60000)




    ############################ 以上内容为添加test_id之后的公共方法 #########################

    def search_on_home_page_operations(self):
        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        home_init_h5(self.page)
        self.page.wait_for_timeout(5000)
        self._search_in_home_page()


    def _search_in_home_page(self, keyword="tofu"):
        self.page.get_by_placeholder("Search").click()
        self.page.wait_for_timeout(2000)
        self.page.get_by_placeholder("What are you looking for?").fill(keyword)
        self.page.keyboard.press('Enter')
        self.page.wait_for_timeout(5000)
        all_add_to_cart = self.page.query_selector_all(self.ele_add_to_cart_in_search_page)
        assert all_add_to_cart, f"没有搜索到关键词={keyword}的结果"
        if all_add_to_cart and not self.FE.ele(self.ele_not_found_keyword):
            for index, item in enumerate(all_add_to_cart):
                item.click()
                self.page.wait_for_timeout(1000)
                if index == 3:
                    break

        # 返回首页
        self.page.go_back()


    def save_address_in_home_page(self):
        home_init_h5(self.page)
        self.FE.ele(mweb_home_ele.ele_h5_home_zipcode).click()
        assert self.FE.ele(u"//div[text()='Deliver to']").is_visible()
        self.FE.ele(mweb_home_ele.ele_add_new_address_div).click()
        self.FE.ele(mweb_home_ele.ele_input_address).click()
        self.page.get_by_placeholder("Street Address").fill("15006 104th Ave NE,Bothell,WA,98011")
        self.FE.ele(mweb_home_ele.ele_1st_matched_address).click()
        self.page.get_by_placeholder("First Name").fill("hello")
        self.page.get_by_placeholder("Last Name").fill("world")
        self.page.get_by_placeholder("Phone Number").fill("5555555555")
        self.FE.ele(mweb_home_ele.ele_note).fill("h5 UI自动化")
        self.FE.ele(mweb_home_ele.ele_save_address).click()

        self.page.goto(TEST_URL)
        self.FE.ele(mweb_home_ele.ele_h5_home_zipcode).click()
        if self.FE.ele(mweb_home_ele.ele_hello_world_address):
            self.FE.ele(mweb_home_ele.ele_hello_world_address).click()
            self.FE.ele(mweb_home_ele.ele_delete_address_button).click()
            self.FE.ele(mweb_home_ele.ele_confirm_delete_button).click()

    def home_page_click_global_bar(self):
        home_init_h5(self.page)
        self.FE.ele(mweb_home_ele.ele_h5_home_navigation_global).click()


    def home_page_scroll_and_add_to_cart(self):
        home_init_h5(self.page)
        self.page.wait_for_timeout(3000)
        scroll_one_page_until(self.page, element="//h2[text()='Recommendations']")

        self.add_each_collections_product_to_cart_for_h5(mweb_home_ele.ele_h5_home_editors_pick_add_to_cart, index=1, c_selector=mweb_home_ele.ele_h5_home_editors_pick)
        self.add_each_collections_product_to_cart_for_h5(mweb_home_ele.ele_h5_home_everyday_deals_add_to_cart, index=2, c_selector=mweb_home_ele.ele_h5_home_everyday_deals)
        self.add_each_collections_product_to_cart_for_h5(mweb_home_ele.ele_h5_home_fresh_daily_add_to_cart, index=3, c_selector=mweb_home_ele.ele_h5_home_fresh_daily)
        self.add_each_collections_product_to_cart_for_h5(mweb_home_ele.ele_h5_home_best_sellers_add_to_cart, index=4, c_selector=mweb_home_ele.ele_h5_home_best_sellers)
        self.add_each_collections_product_to_cart_for_h5(mweb_home_ele.ele_h5_home_recommendations_add_to_cart, index=5, c_selector=mweb_home_ele.ele_h5_home_recommendations)


    def add_each_collections_product_to_cart_for_h5(self, collections, index, c_selector):
        self.page.wait_for_timeout(2000)
        # 必须用query_selector来scroll_into_view,page.locator就无效
        self.FE.ele(c_selector).scroll_into_view_if_needed()

        each_collection = self.page.query_selector_all(collections)
        assert each_collection, f"首页没有可以加购的商品，each_collection={collections}"
        log.info("each_collection===>" + str(each_collection))
        for index, item in enumerate(each_collection):
            try:
                item.evaluate('(item) => item.click()')
                # item.click()
            except Exception as e:
                log.info("加购按钮点击失败" + str(e))
            # self.page.reload()
            self.page.wait_for_timeout(1000)
            if index == 2:
                break

    def add_products_from_home(self, count: int = 2):
        """
        在首页加购指定数量的商品

        Args:
            count: 要加购的商品数量，默认2个

        Returns:
            int: 实际加购的商品数量
        """
        try:
            # 等待页面加载完成
            self.page.wait_for_timeout(3000)

            # 查找首页所有加购按钮
            home_add_buttons = self.page.get_by_test_id("btn-atc-plus").all()
            log.info(f"首页找到 {len(home_add_buttons)} 个加购按钮")

            added_count = 0
            for index, add_btn in enumerate(home_add_buttons):
                try:
                    if add_btn.is_visible() and add_btn.is_enabled():
                        add_btn.click()
                        self.page.wait_for_timeout(1500)  # 等待加购完成
                        added_count += 1
                        log.info(f"首页成功加购第 {added_count} 个商品")

                        # 达到指定数量后停止
                        if added_count >= count:
                            break
                except Exception as e:
                    log.warning(f"首页第 {index + 1} 个加购按钮点击失败: {str(e)}")
                    continue

            log.info(f"首页成功加购 {added_count} 个商品")
            return added_count
        except Exception as e:
            log.error(f"首页加购商品失败: {str(e)}")
            return 0

    def navigate_to_global_plus(self):
        """
        点击Global+按钮进入Global+页面

        Returns:
            bool: 是否成功进入Global+页面
        """
        try:
            # 点击Global+按钮
            global_plus_btn = self.page.get_by_test_id("wid-categories-item-3")
            global_plus_btn.click()
            self.page.wait_for_timeout(8000)
            # 解决 global+ 页面弹窗
            if self.page.locator("//div[text()='Not now']").all():
                self.page.locator("//div[text()='Not now']").click()
                self.page.wait_for_timeout(2000)

            log.info("成功进入Global+页面")
            scroll_n_page(self.page, 1)
            return True
        except Exception as e:
            log.error(f"进入Global+页面失败: {str(e)}")
            return False

    def add_products_from_global_plus(self, count: int = 2):
        """
        在Global+页面加购指定数量的商品

        Args:
            count: 要加购的商品数量，默认2个

        Returns:
            int: 实际加购的商品数量
        """
        try:
            # 查找Global+页面所有加购按钮
            global_add_buttons = self.page.get_by_test_id("btn-atc-plus").all()
            log.info(f"Global+页面找到 {len(global_add_buttons)} 个加购按钮")

            added_count = 0
            for index, add_btn in enumerate(global_add_buttons):
                try:
                    if add_btn.is_visible() and add_btn.is_enabled():
                        add_btn.click()
                        self.page.wait_for_timeout(1500)  # 等待加购完成
                        added_count += 1
                        log.info(f"Global+页面成功加购第 {added_count} 个商品")

                        # 达到指定数量后停止
                        if added_count >= count:
                            break
                except Exception as e:
                    log.warning(f"Global+页面第 {index + 1} 个加购按钮点击失败: {str(e)}")
                    continue

            log.info(f"Global+页面成功加购 {added_count} 个商品")
            return added_count
        except Exception as e:
            log.error(f"Global+页面加购商品失败: {str(e)}")
            return 0

    def create_multi_cart_by_adding_products(self):
        """
        通过在首页和Global+页面加购商品来创建多种类型购物车

        Returns:
            dict: 包含加购结果的字典
        """
        result = {
            'home_added': 0,
            'global_added': 0,
            'total_added': 0,
            'success': False
        }

        try:
            # 在首页加购2个商品
            home_added = self.add_products_from_home(2)
            result['home_added'] = home_added

            # 进入Global+页面
            if self.navigate_to_global_plus():
                # 在Global+页面加购2个商品
                global_added = self.add_products_from_global_plus(2)
                result['global_added'] = global_added

            result['total_added'] = result['home_added'] + result['global_added']
            result['success'] = result['total_added'] >= 4

            log.info(f"多购物车创建结果: {result}")
            return result
        except Exception as e:
            log.error(f"创建多购物车失败: {str(e)}")
            return result

    def click_cart_button(self):
        """
        点击首页Cart按钮进入购物车页面

        Returns:
            bool: 是否成功点击Cart按钮
        """
        try:
            # 点击Cart按钮
            self.page.locator("//img[@alt='Go to cart']").click()
            # 等待购物车页面加载完成
            self.page.wait_for_timeout(3000)
            log.info("成功点击Cart按钮，进入购物车页面")
            return True
        except Exception as e:
            log.error(f"点击Cart按钮失败: {str(e)}")
            return False
    
    def check_banner_type(self, banner_element):
        """检查banner类型"""
        if not banner_element.is_visible():
            return None
        
        banner_text = banner_element.text_content()
        if "Add more items" in banner_text and "free shipping" in banner_text:
            return "shipping_reminder"
        elif "Add fresh and frozen groceries" in banner_text and "delivery for tomorrow" in banner_text:
            return "addon_banner"
        elif "Order by" in banner_text and "delivery tomorrow" in banner_text:
            return "order_reminder"
        return "unknown"
    
    def switch_to_second_date(self):
        """切换到第二个日期选项"""
        date_selector = self.page.get_by_test_id("wid-modal-zip-code-and-eta")
        if date_selector.is_visible():
            date_selector.click()
            self.page.wait_for_timeout(2000)
            
            # 选择第二个日期选项
            date_options = self.page.locator("[data-testid*='date']").all()
            if len(date_options) > 1:
                date_options[1].click()
                self.page.wait_for_timeout(2000)
                log.info("已选择第二个日期")
            
            # 关闭日期选择器
            close_buttons = ["btn-close-delivery-info-dialog", "btn-modal-close"]
            for btn_id in close_buttons:
                close_button = self.page.get_by_test_id(btn_id)
                if close_button.is_visible():
                    close_button.click()
                    self.page.wait_for_timeout(2000)
                    break
    
    def place_order_with_points(self):
        """使用积分支付下单"""
        try:
            # 添加商品到购物车
            add_btn = self.page.get_by_test_id("btn-atc-plus").first
            if add_btn.is_visible():
                add_btn.click()
                self.page.wait_for_timeout(2000)
                log.info("成功添加商品到购物车")
            
            # 进入购物车
            self.page.locator("//div[@data-testid='wid-cart']").click()
            self.page.wait_for_timeout(3000)
            
            # 点击结算
            self.page.get_by_test_id("btn-checkout").click()
            self.page.wait_for_timeout(5000)
            
            # 处理upsell弹窗
            upsell_button = self.page.get_by_test_id("btn-continue")
            if upsell_button.is_visible(timeout=3000):
                upsell_button.click()
                self.page.wait_for_timeout(3000)
            
            # 检查是否需要选择地址
            warning_icon = self.page.get_by_test_id("mod-checkout-delivery-info-warning-icon")
            if warning_icon.is_visible():
                address_content = self.page.get_by_test_id("mod-checkout-delivery-info-content")
                address_content.click()
                self.page.wait_for_timeout(2000)
                
                first_address = self.page.locator("[data-testid*='address-item']").first
                if first_address.is_visible():
                    first_address.click()
                    self.page.wait_for_timeout(2000)
                    log.info("已选择地址")
            
            # 使用积分支付
            not_checked_icon = self.page.get_by_test_id("wid-checkout-payment-points-icon-not-checked")
            if not_checked_icon.is_visible():
                points_switch = self.page.get_by_test_id("wid-checkout-payment-points-wrapper-content")
                points_switch.click()
                self.page.wait_for_timeout(2000)
                log.info("已勾选积分支付")
            
            # 提交订单
            submit_btn = self.page.get_by_test_id("btn-checkout")
            submit_btn.click()
            self.page.wait_for_timeout(10000)
            
            return True
        except Exception as e:
            log.error(f"下单失败: {str(e)}")
            return False
    
    def handle_shipping_reminder_flow(self):
        """处理合单提醒完整流程"""
        from src.Mweb.EC.mweb_ele.mweb_home.mweb_topmessage import mweb_topmessage_ele
        from src.config.base_config import TEST_URL
        
        self.page.wait_for_timeout(3000)
        banner = self.page.get_by_test_id(mweb_topmessage_ele.ele_home_cutoff)
        banner_type = self.check_banner_type(banner)
        
        # 如果add-on banner存在，直接返回
        if banner_type == "addon_banner":
            log.info("add on banner存在，跳过流程")
            return True
        
        # 如果是截单提醒，切换日期
        if banner_type == "order_reminder":
            log.info("截单提醒存在，切换日期")
            self.switch_to_second_date()
            self.page.goto(TEST_URL + "/")
            self.page.wait_for_timeout(3000)
            
            # 重新检查banner
            banner = self.page.get_by_test_id(mweb_topmessage_ele.ele_home_cutoff)
            banner_type = self.check_banner_type(banner)
            
            if banner_type == "addon_banner":
                log.info("切换日期后add on banner出现")
                return True
        
        # 如果不是合单提醒，执行下单流程
        if banner_type != "shipping_reminder":
            log.info("合单提醒不存在，开始下单")
            if self.place_order_with_points():
                self.close_all_popups()
                self.page.goto(TEST_URL + "/")
                self.page.wait_for_timeout(3000)
                
                # 检查下单后的banner
                banner_after = self.page.get_by_test_id(mweb_topmessage_ele.ele_home_cutoff)
                if self.check_banner_type(banner_after) == "order_reminder":
                    self.switch_to_second_date()
        
        # 最终验证合单提醒
        banner = self.page.get_by_test_id(mweb_topmessage_ele.ele_home_cutoff)
        banner_type = self.check_banner_type(banner)
        
        if banner_type == "addon_banner":
            log.info("add on banner存在，跳过校验")
            return True
        
        if banner_type != "shipping_reminder":
            log.error(f"最终不是合单提醒: {banner.text_content() if banner.is_visible() else 'banner不可见'}")
            return False
        
        # 点击合单提醒并验证跳转
        banner.click()
        self.page.wait_for_timeout(3000)
        self.close_all_popups()
        
        expected_url = "/category/sale?filter_sub_category=sale&sort=&filters=&out_filters="
        if expected_url not in self.page.url:
            log.error(f"跳转失败: {self.page.url}")
            return False
        
        log.info("合单提醒流程验证成功")
        return True
    
    def close_all_popups(self):
        """关闭所有可能出现的弹窗"""
        try:
            close_btns = ["btn-modal-close", "wid-modal-close", "btn-close", "wid-close-btn"]
            for btn_id in close_btns:
                btn = self.page.get_by_test_id(btn_id)
                if btn.is_visible(timeout=1000):
                    btn.click()
                    self.page.wait_for_timeout(500)
        except Exception as e:
            log.warning(f"关闭弹窗失败: {str(e)}")

