from typing import Literal

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_home import mweb_home_ele
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.common.commfunc import empty_cart
from src.common.commonui import home_init_h5, scroll_one_page_until, scroll_n_page
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log

class MWebPageHome(PageH5CommonOperations):
    ele_home_div_input_search_item = u"div[aria-label='What are you looking for?']"
    ele_home_input_item = u"input[class^='box-border']"
    ele_add_to_cart_in_search_page = u"i[data-role='addButtonPlusIcon']"
    ele_return_to_home = u"i[class='icon iconfont iconBack']"
    ele_not_found_keyword = u"//p[contains(text(),'我们会根据用户建议每周上架新品，敬请关注哦')]"

    def __init__(self, page: Page, header, bc):
        super().__init__(page, header)
        self.bc = bc

        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        self.page.goto(TEST_URL + "?joinEnki=true")
        self.page.wait_for_timeout(5000)
        # 关闭首页广告
        self.close_advertisement_in_homepage()


    def click_category(self, category_name: str):
        if category_name not in  ["Alcohol", "Fruits", "Ching Ming Festival"]:
            # 不知道为什么，alcohol的结构与别的category不一样
            self.page.locator(f'//span[text()="{category_name}"]').all()[0].click()
        else:
            self.page.locator(f'//span[text()="{category_name}"]').all()[1].click()
        self.page.wait_for_timeout(5000)

    def go_to_special_category_from_home(self, special_category):
        """
        从首页点击特定分类进入分类页

        Args:
            special_category: 特定分类的元素选择器
        """
        # 点击特定分类进入分类页
        self.FE.ele(special_category).click()
        # 等待页面加载完成
        self.page.wait_for_timeout(2000)
    def m_close_time_banner(self):
        """
        close time banner on home page
        """
        if self.page.get_by_test_id("btn-close-promo-bar").all():
            self.page.get_by_test_id("btn-close-promo-bar").click()
            self.page.wait_for_timeout(2000)

    def m_home_switch_specific_store(self, store_name: Literal["japanese", "chinese", "korean", "vietnamese", "filipino", "explorer"]):
        """
        switch stores by specific store name
        """
        self.page.get_by_test_id("mod-store-list-container").locator("//span").click()
        self.page.get_by_test_id(f"wid-store-line-{store_name}").click()
        self.page.wait_for_timeout(5000)



    def m_download_app(self, platform_name: Literal["ios", "android"]):
        self.page.wait_for_timeout(5000)
        self.page.goto("https://www.sayweee.com/company/home")
        self.page.wait_for_timeout(5000)
        scroll_one_page_until(self.page, "div[data-framer-name='App store button']")
        if platform_name == "ios":
            with self.bc.expect_page() as download_page:
                self.page.locator("div[data-framer-name='App store button']").click()
                self.page.wait_for_timeout(3000)
            new_page: Page = download_page.value
            assert new_page.locator("div[data-framer-name='app_store_btn'][name='app_store_btn']").all()
            self.page.locator("div[data-framer-name='App store button']").click()
        else:
            log.info("输入的platform名称不对")

    def m_change_zipcode(self):
        """
        输入zipcode并点击confirm
        """
        self.page.get_by_test_id("btn-change-delivery").click()
        self.page.wait_for_timeout(5000)
        self.page.get_by_test_id("wid-address-card").all()[0].click()
        self.page.wait_for_load_state("networkidle", timeout=60000)




    ############################ 以上内容为添加test_id之后的公共方法 #########################

    def search_on_home_page_operations(self):
        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        home_init_h5(self.page)
        self.page.wait_for_timeout(5000)
        self._search_in_home_page()


    def _search_in_home_page(self, keyword="tofu"):
        self.page.get_by_placeholder("Search").click()
        self.page.wait_for_timeout(2000)
        self.page.get_by_placeholder("What are you looking for?").fill(keyword)
        self.page.keyboard.press('Enter')
        self.page.wait_for_timeout(5000)
        all_add_to_cart = self.page.query_selector_all(self.ele_add_to_cart_in_search_page)
        assert all_add_to_cart, f"没有搜索到关键词={keyword}的结果"
        if all_add_to_cart and not self.FE.ele(self.ele_not_found_keyword):
            for index, item in enumerate(all_add_to_cart):
                item.click()
                self.page.wait_for_timeout(1000)
                if index == 3:
                    break

        # 返回首页
        self.page.go_back()


    def save_address_in_home_page(self):
        home_init_h5(self.page)
        self.FE.ele(mweb_home_ele.ele_h5_home_zipcode).click()
        assert self.FE.ele(u"//div[text()='Deliver to']").is_visible()
        self.FE.ele(mweb_home_ele.ele_add_new_address_div).click()
        self.FE.ele(mweb_home_ele.ele_input_address).click()
        self.page.get_by_placeholder("Street Address").fill("15006 104th Ave NE,Bothell,WA,98011")
        self.FE.ele(mweb_home_ele.ele_1st_matched_address).click()
        self.page.get_by_placeholder("First Name").fill("hello")
        self.page.get_by_placeholder("Last Name").fill("world")
        self.page.get_by_placeholder("Phone Number").fill("5555555555")
        self.FE.ele(mweb_home_ele.ele_note).fill("h5 UI自动化")
        self.FE.ele(mweb_home_ele.ele_save_address).click()

        self.page.goto(TEST_URL)
        self.FE.ele(mweb_home_ele.ele_h5_home_zipcode).click()
        if self.FE.ele(mweb_home_ele.ele_hello_world_address):
            self.FE.ele(mweb_home_ele.ele_hello_world_address).click()
            self.FE.ele(mweb_home_ele.ele_delete_address_button).click()
            self.FE.ele(mweb_home_ele.ele_confirm_delete_button).click()

    def home_page_click_global_bar(self):
        home_init_h5(self.page)
        self.FE.ele(mweb_home_ele.ele_h5_home_navigation_global).click()


    def home_page_scroll_and_add_to_cart(self):
        home_init_h5(self.page)
        self.page.wait_for_timeout(3000)
        scroll_one_page_until(self.page, element="//h2[text()='Recommendations']")

        self.add_each_collections_product_to_cart_for_h5(mweb_home_ele.ele_h5_home_editors_pick_add_to_cart, index=1, c_selector=mweb_home_ele.ele_h5_home_editors_pick)
        self.add_each_collections_product_to_cart_for_h5(mweb_home_ele.ele_h5_home_everyday_deals_add_to_cart, index=2, c_selector=mweb_home_ele.ele_h5_home_everyday_deals)
        self.add_each_collections_product_to_cart_for_h5(mweb_home_ele.ele_h5_home_fresh_daily_add_to_cart, index=3, c_selector=mweb_home_ele.ele_h5_home_fresh_daily)
        self.add_each_collections_product_to_cart_for_h5(mweb_home_ele.ele_h5_home_best_sellers_add_to_cart, index=4, c_selector=mweb_home_ele.ele_h5_home_best_sellers)
        self.add_each_collections_product_to_cart_for_h5(mweb_home_ele.ele_h5_home_recommendations_add_to_cart, index=5, c_selector=mweb_home_ele.ele_h5_home_recommendations)


    def add_each_collections_product_to_cart_for_h5(self, collections, index, c_selector):
        self.page.wait_for_timeout(2000)
        # 必须用query_selector来scroll_into_view,page.locator就无效
        self.FE.ele(c_selector).scroll_into_view_if_needed()

        each_collection = self.page.query_selector_all(collections)
        assert each_collection, f"首页没有可以加购的商品，each_collection={collections}"
        log.info("each_collection===>" + str(each_collection))
        for index, item in enumerate(each_collection):
            try:
                item.evaluate('(item) => item.click()')
                # item.click()
            except Exception as e:
                log.info("加购按钮点击失败" + str(e))
            # self.page.reload()
            self.page.wait_for_timeout(1000)
            if index == 2:
                break

    def add_products_from_home(self, count: int = 2):
        """
        在首页加购指定数量的商品

        Args:
            count: 要加购的商品数量，默认2个

        Returns:
            int: 实际加购的商品数量
        """
        try:
            # 等待页面加载完成
            self.page.wait_for_timeout(3000)

            # 查找首页所有加购按钮
            home_add_buttons = self.page.get_by_test_id("btn-atc-plus").all()
            log.info(f"首页找到 {len(home_add_buttons)} 个加购按钮")

            added_count = 0
            for index, add_btn in enumerate(home_add_buttons):
                try:
                    if add_btn.is_visible() and add_btn.is_enabled():
                        add_btn.click()
                        self.page.wait_for_timeout(1500)  # 等待加购完成
                        added_count += 1
                        log.info(f"首页成功加购第 {added_count} 个商品")

                        # 达到指定数量后停止
                        if added_count >= count:
                            break
                except Exception as e:
                    log.warning(f"首页第 {index + 1} 个加购按钮点击失败: {str(e)}")
                    continue

            log.info(f"首页成功加购 {added_count} 个商品")
            return added_count
        except Exception as e:
            log.error(f"首页加购商品失败: {str(e)}")
            return 0

    def navigate_to_global_plus(self):
        """
        点击Global+按钮进入Global+页面

        Returns:
            bool: 是否成功进入Global+页面
        """
        try:
            # 点击Global+按钮
            global_plus_btn = self.page.get_by_test_id("wid-categories-item-3")
            global_plus_btn.click()
            self.page.wait_for_timeout(8000)
            # 解决 global+ 页面弹窗
            if self.page.locator("//div[text()='Not now']").all():
                self.page.locator("//div[text()='Not now']").click()
                self.page.wait_for_timeout(2000)

            log.info("成功进入Global+页面")
            scroll_n_page(self.page, 1)
            return True
        except Exception as e:
            log.error(f"进入Global+页面失败: {str(e)}")
            return False

    def add_products_from_global_plus(self, count: int = 2):
        """
        在Global+页面加购指定数量的商品

        Args:
            count: 要加购的商品数量，默认2个

        Returns:
            int: 实际加购的商品数量
        """
        try:
            # 查找Global+页面所有加购按钮
            global_add_buttons = self.page.get_by_test_id("btn-atc-plus").all()
            log.info(f"Global+页面找到 {len(global_add_buttons)} 个加购按钮")

            added_count = 0
            for index, add_btn in enumerate(global_add_buttons):
                try:
                    if add_btn.is_visible() and add_btn.is_enabled():
                        add_btn.click()
                        self.page.wait_for_timeout(1500)  # 等待加购完成
                        added_count += 1
                        log.info(f"Global+页面成功加购第 {added_count} 个商品")

                        # 达到指定数量后停止
                        if added_count >= count:
                            break
                except Exception as e:
                    log.warning(f"Global+页面第 {index + 1} 个加购按钮点击失败: {str(e)}")
                    continue

            log.info(f"Global+页面成功加购 {added_count} 个商品")
            return added_count
        except Exception as e:
            log.error(f"Global+页面加购商品失败: {str(e)}")
            return 0

    def create_multi_cart_by_adding_products(self):
        """
        通过在首页和Global+页面加购商品来创建多种类型购物车

        Returns:
            dict: 包含加购结果的字典
        """
        result = {
            'home_added': 0,
            'global_added': 0,
            'total_added': 0,
            'success': False
        }

        try:
            # 在首页加购2个商品
            home_added = self.add_products_from_home(2)
            result['home_added'] = home_added

            # 进入Global+页面
            if self.navigate_to_global_plus():
                # 在Global+页面加购2个商品
                global_added = self.add_products_from_global_plus(2)
                result['global_added'] = global_added

            result['total_added'] = result['home_added'] + result['global_added']
            result['success'] = result['total_added'] >= 4

            log.info(f"多购物车创建结果: {result}")
            return result
        except Exception as e:
            log.error(f"创建多购物车失败: {str(e)}")
            return result

    def click_cart_button(self):
        """
        点击首页Cart按钮进入购物车页面

        Returns:
            bool: 是否成功点击Cart按钮
        """
        try:
            # 点击Cart按钮
            self.page.locator("//img[@alt='Go to cart']").click()
            # 等待购物车页面加载完成
            self.page.wait_for_timeout(3000)
            log.info("成功点击Cart按钮，进入购物车页面")
            return True
        except Exception as e:
            log.error(f"点击Cart按钮失败: {str(e)}")
            return False
