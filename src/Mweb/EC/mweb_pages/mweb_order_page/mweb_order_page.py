import re

from src.Mweb.EC.mweb_ele.mweb_order import mweb_order_list_ele
from src.config.base_config import TEST_URL
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_common_page import MWebCommonPage
from src.config.weee.log_help import log


class MWebOrderPage(MWebCommonPage):
    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入指定页面
        self.page.goto(TEST_URL + page_url + "?joinEnki=true")
        self.page.wait_for_timeout(5000)
        # # 清空购物车
        # try:
        #     empty_cart(self.header)
        # except Exception as e:
        #     log.info("清空购物车发生异常" + str(e))

    def scroll_to_pos(self, test_id: str):
        """
        滚动到指定元素位置
        Args:
            test_id: 元素的test-id
        """
        try:
            element = self.page.get_by_test_id(test_id)
            if element.is_visible():
                element.scroll_into_view_if_needed()
                self.page.wait_for_timeout(1000)
                log.info(f"成功滚动到元素: {test_id}")
            else:
                log.warning(f"元素不可见，无法滚动: {test_id}")
        except Exception as e:
            log.error(f"滚动到元素时发生异常: {test_id}, 错误: {str(e)}")

    def verify_my_orders_ui_elements(self):
        """
        验证我的订单模块的UI元素
        Returns:
            bool: 验证是否成功
        """
        try:
            # 验证箭头存在
            arrow = self.page.get_by_test_id("wid-order-arrow-right")
            if not arrow.is_visible():
                log.error("我的订单箭头未找到")
                return False

            # 验证各个订单状态的UI元素
            order_statuses = ["pending", "unshipped", "shipped", "to_review", "returns"]

            for status in order_statuses:
                # 验证图标
                image_element = self.page.get_by_test_id(f"wid-order-{status}-image")
                if not image_element.is_visible():
                    log.error(f"{status} 图标未找到")
                    return False

                # 验证标题（处理unshipped的特殊情况）
                if status == "unshipped":
                    title_element = self.page.get_by_test_id(f"wid-order-{status}-title  ")
                else:
                    title_element = self.page.get_by_test_id(f"wid-order-{status}-title")

                if not title_element.is_visible():
                    log.error(f"{status} 标题未找到")
                    return False

                # 验证数字球（如果存在且大于99显示99+）
                value_element = self.page.get_by_test_id(f"wid-order-{status}-value")
                if value_element.is_visible():
                    try:
                        value_text = value_element.text_content()
                        if value_text and value_text.isdigit() and int(value_text) > 99:
                            if value_text != "99+":
                                log.error(f"{status} 数字球显示错误，期望99+，实际{value_text}")
                                return False
                    except (ValueError, TypeError):
                        # 如果不是数字，跳过验证
                        pass

                log.info(f"{status} 状态UI元素验证成功")

            log.info("我的订单模块UI元素验证成功")
            return True

        except Exception as e:
            log.error(f"验证我的订单模块UI元素时发生异常: {str(e)}")
            return False

    def click_my_orders_link(self, link_test_id: str):
        """
        点击我的订单链接
        Args:
            link_test_id: 链接的test-id
        Returns:
            bool: 操作是否成功
        """
        try:
            my_orders_link = self.page.get_by_test_id(link_test_id)
            if my_orders_link.is_visible():
                my_orders_link.click()
                self.page.wait_for_timeout(2000)

                # 验证是否成功进入订单列表页面
                if "/order/list" in self.page.url:
                    log.info("成功点击我的订单链接，进入订单列表页面")
                    return True
                else:
                    log.error("点击我的订单链接后未进入订单列表页面")
                    return False
            else:
                log.error("我的订单链接未找到")
                return False

        except Exception as e:
            log.error(f"点击我的订单链接时发生异常: {str(e)}")
            return False

    def click_order_status_tab(self, status_test_id: str, expected_url: str):
        """
        点击订单状态tab
        Args:
            status_test_id: 状态tab的test-id
            expected_url: 期望的URL
        Returns:
            bool: 操作是否成功
        """
        try:
            status_tab = self.page.get_by_test_id(status_test_id)
            if status_tab.is_visible():
                status_tab.click()
                self.page.wait_for_timeout(2000)

                # 验证URL是否正确
                current_url = self.page.url
                if expected_url in current_url:
                    log.info(f"成功点击状态tab，当前URL: {current_url}")
                    return True
                else:
                    log.error(f"点击状态tab后URL不正确，期望包含: {expected_url}，实际: {current_url}")
                    return False
            else:
                log.error(f"状态tab未找到: {status_test_id}")
                return False

        except Exception as e:
            log.error(f"点击状态tab时发生异常: {str(e)}")
            return False


    def assert_order_tab_info(self, order_item, order_tab):
        """
        验证seller订单卡片信息的公共断言方法
        Args:
            order_item: 订单卡片元素
        Returns:
            bool: 验证是否成功
        """
        status_text = order_item.get_by_test_id(mweb_order_list_ele.order_list_card_statu_ele).text_content()

        if order_tab == "all":
            # 全部订单
            # 断言全部订单里不存在状态是Cancelled的订单
            assert status_text not in ("Canceled", "Cancelled"), f"全部订单里有Cancelled订单"
        if order_tab == "1":
            # 待支付订单
            # 断言全部订单里不存在状态是Cancelled的订单
            assert status_text in ("Pending", "待支付"), f"待支付订单里状态不对"

        elif order_tab == "2":
            # 待发货订单
            assert status_text in ("Ready to Ship"), f"待发货订单里状态不对"

        elif order_tab == "3":
            # 已发货订单
            assert status_text in ("Delivered"), f"已发货订单里状态不对"

        elif order_tab == "4":
            # 已取消订单
            assert status_text not in ("Canceled", "Cancelled"), f"已取消订单状态不对"
        return True

    def assert_order_card_info(self, order_item,order_type):
        """
        验证seller订单卡片信息的公共断言方法
        Args:
            order_item: 订单卡片元素
        Returns:
            bool: 验证是否成功
        """
        # 验证订单内容信息
        assert order_item.get_by_test_id(mweb_order_list_ele.order_list_card_status_ele).is_visible(), "订单内容信息不存在"
        assert order_item.get_by_test_id(mweb_order_list_ele.order_list_card_detail_ele).is_visible(), "订单内容信息不存在"
        # seller 订单
        if order_type == "S":
            assert order_item.get_by_test_id("wid-order-list-seller-delivery-date-label").is_visible(), " 预计送达信息不存在"
            assert order_item.get_by_test_id("wid-order-list-vendor-title-link").is_visible(), "seller订单icon信息不存在"
            assert order_item.get_by_test_id("wid-order-btn-buy_again").is_visible(), "积分订单的存在再来一单按钮"
        if order_type == "R":
            # 如果是pantry ，这里会报错
            assert order_item.get_by_test_id("wid-order-list-delivery-date-label").is_visible(), " 预计送达信息不存在"
            assert order_item.get_by_test_id("wid-order-btn-buy_again").is_visible(), "积分订单的存在再来一单按钮"
        # 礼品卡订单
        elif order_type == "G":
            assert order_item.get_by_test_id("wid-order-list-giftcard-delivery-date-label").is_visible(), " 预计送达信息不存在"

            assert order_item.get_by_test_id("wid-order-list-giftcard-send-to").is_visible(), "giftcard订单发送给 XX信息不存在"
            assert not order_item.get_by_test_id("wid-order-btn-buy_again").is_visible(),"礼品卡存在再来一单按钮"
        # 积分订单
        elif order_type == "P":
            assert order_item.get_by_test_id("wid-order-list-effective-date-label").is_visible(), " 预计送达信息不存在"
            assert not order_item.get_by_test_id("wid-order-btn-buy_again").is_visible(),"积分订单的存在再来一单按钮"

        # 验证订单卡片上 Delivery date\Order number\Items\Total 信息
        assert order_item.get_by_test_id("wid-order-list-pick-date").is_visible(), " Delivery date信息不存在"
        assert order_item.get_by_test_id("wid-order-list-order-number-label").is_visible(), " Order number 信息不存在"
        assert order_item.get_by_test_id("wid-order-list-order-number").is_visible(), " Order number信息不存在"
        assert order_item.get_by_test_id("wid-order-list-totals-label").is_visible(), "Total 信息不存在"
        assert order_item.get_by_test_id(mweb_order_list_ele.order_list_card_statu_ele).is_visible(), "订单状态不存在"
        # 验证Total金额
        totals_amount = order_item.get_by_test_id("wid-order-list-totals-amount").text_content()
        # 提取数字部分
        amount_matches = re.findall(r'\d+\.\d+', totals_amount)
        assert len(amount_matches) > 0 and float(amount_matches[0]) > 0, "Total 信息不存在或金额无效"
        # 验证订单产品信息
        assert order_item.locator(mweb_order_list_ele.order_list_card_product_ele).is_visible(), "订单产品信息不存在"
        log.info("seller订单卡片信息验证成功")
        return True


