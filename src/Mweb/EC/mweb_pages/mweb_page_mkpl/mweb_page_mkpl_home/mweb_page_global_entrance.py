from playwright.sync_api import Page
from src.config.weee.log_help import log
from src.Mweb.EC.mweb_ele.mweb_home.mweb_home_ele import (
    ele_h5_home_menu_side_bar,
    ele_h5_home_menu_global_entrance)
from src.Mweb.EC.mweb_ele.mweb_mkpl_home.mweb_global_waterfall import close_popup_on_home


class MWebGlobalEntrancePage:

    def __init__(self, page: Page, header):
        self.page = page
        self.header = header

    def navigate_and_click_global_entrance(self):
        """访问主页并点击Global+入口"""
        # 1. 访问主页
        self.page.goto("https://www.sayweee.com/zh")
        log.info("访问主页完成")
        
        # 2. 等待5秒，检查并关闭弹窗
        self.page.wait_for_timeout(5000)
        
        close_btn = self.page.get_by_test_id(close_popup_on_home)
        if close_btn.count() > 0:
            close_btn.click()
            log.info("点击关闭弹窗按钮成功")
        else:
            log.info("关闭弹窗按钮不存在")
        
        # 3. 检查并点击侧边栏菜单元素
        sidebar_menu = self.page.get_by_test_id(ele_h5_home_menu_side_bar)
        if sidebar_menu.count() > 0:
            sidebar_menu.first.click()
            log.info("点击侧边栏菜单成功")
        else:
            log.info("侧边栏菜单元素不存在")
            return False
        
        # 4. 等待3秒后，检查并点击Global+入口元素
        self.page.wait_for_timeout(3000)
        
        global_entrance = self.page.get_by_test_id(ele_h5_home_menu_global_entrance)
        if global_entrance.count() > 0:
            global_entrance.first.click()
            log.info("点击Global+入口成功")
            return True
        else:
            log.info("Global+入口元素不存在")
            return False





