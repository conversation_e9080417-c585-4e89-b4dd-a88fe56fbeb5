from playwright.sync_api import Page
from src.config.weee.log_help import log
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.Mweb.EC.mweb_ele.mweb_mkpl_home.mweb_global_waterfall import (
    global_search_input,
    search_product_card,
    global_product_free_shipping_label,
    close_popup_on_home
)


class MWebMkplSearchPage(PageH5CommonOperations):

    def __init__(self, page: Page, header):
        super().__init__(page, header)

    def navigate_to_homepage(self):
        """访问主页"""
        self.page.goto("https://www.sayweee.com/")
        log.info("访问主页完成")
        
        # 等待7秒
        self.page.wait_for_timeout(7000)
        
        # 点击关闭弹窗按钮
        close_btn = self.page.get_by_test_id(close_popup_on_home)
        if close_btn.count() > 0:
            close_btn.click()
            log.info("点击关闭弹窗按钮成功")
        else:
            log.info("关闭弹窗按钮不存在")

    def search_mkpl_product(self, search_text="资生堂胶原蛋白口服液 加强奢华版"):
        """搜索商品"""
        # 等待7秒后检查搜索框
        self.page.wait_for_timeout(7000)
        
        search_input = self.page.get_by_test_id(global_search_input)
        if search_input.count() > 0:
            search_input.click()
            log.info("点击搜索框成功")
            
            # 等待3秒检查页面跳转
            self.page.wait_for_timeout(3000)
            current_url = self.page.url
            if "search" in current_url:
                log.info(f"页面跳转验证成功: {current_url}")
            else:
                log.info(f"页面跳转可能失败: {current_url}")
            
            # 输入搜索内容并回车
            search_input.fill(search_text)
            search_input.press("Enter")
            log.info(f"输入搜索内容并回车: {search_text}")
            
            return True
        else:
            log.info("搜索框不存在")
            return False

    def check_search_results(self):
        """检查搜索结果"""
        # 等待6秒
        self.page.wait_for_timeout(6000)
        
        # 检查第一个商品卡片
        product_card = self.page.get_by_test_id(search_product_card).first
        if product_card.count() > 0:
            log.info("第一个商品卡片存在")
            
            # 检查免运费标签
            free_shipping_label = self.page.get_by_test_id(global_product_free_shipping_label)
            if free_shipping_label.count() > 0:
                log.info("免运费标签存在")
                return True
            else:
                log.info("免运费标签不存在")
                return False
        else:
            log.info("第一个商品卡片不存在")
            return False

    def search_seller(self, seller_name="Woo Japan"):
        """搜索商家"""
        # 等待7秒后检查搜索框
        self.page.wait_for_timeout(7000)
        
        search_input = self.page.get_by_test_id(global_search_input)
        if search_input.count() > 0:
            search_input.click()
            log.info("点击搜索框成功")
            
            # 等待3秒检查页面跳转
            self.page.wait_for_timeout(3000)
            
            # 输入商家名称并回车
            search_input.fill(seller_name)
            search_input.press("Enter")
            log.info(f"输入商家名称并回车: {seller_name}")
            
            # 等待5秒让页面完全加载和重定向
            self.page.wait_for_timeout(5000)
            
            return True
        else:
            log.info("搜索框不存在")
            return False

    def execute_search_flow(self):
        """执行完整搜索流程"""
        self.navigate_to_homepage()
        
        if self.search_mkpl_product():
            return self.check_search_results()
        else:
            return False