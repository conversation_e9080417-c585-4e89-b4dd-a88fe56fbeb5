from src.Mweb.EC.mweb_ele.mweb_promotion import mweb_promotion_page_ele
from src.Mweb.EC.mweb_pages.mweb_page_pdp import mweb_page_pdp
from src.config.base_config import TEST_URL
from playwright.sync_api import Page
from src.common.commfunc import empty_cart
from src.config.weee.log_help import log
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_common_page import MWebCommonPage
from src.Mweb.EC.mweb_ele.mweb_promotion import mweb_promotion_page_ele


class MWebPromotionPage(MWebCommonPage):
    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入promotion页面
        self.page.goto(TEST_URL + page_url + "&joinEnki=true")
        self.page.wait_for_timeout(5000)
        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        # 关闭首页广告
        self.close_advertisement_in_homepage()

    def close_advertisement_in_homepage(self):
        if self.page.locator("//img[contains(@aria-label, 'close button')]").all():
            self.page.locator("//img[contains(@aria-label, 'close button')]").click()

    def goto_activity_page_and_check_share(self, activity_url):
        """
        此方法包含以下功能：
        1. 进入活动页面
        2. 校验页面基本元素
        3. 校验分享按钮存在
        4. 测试分享功能
        5. 校验分享弹窗内容
        """
        # 1. 进入活动页面
        self.page.goto(activity_url)
        self.page.wait_for_timeout(5000)
    
        # 2. 校验页面基本元素
        self._check_activity_page()
    
        # 3. 校验分享功能
        self._check_activity_share_function()
    
    
    def _check_activity_page(self):
        """
        校验活动页面基本元素
        """
        # 等待页面加载完成
        self.page.wait_for_load_state("networkidle", timeout=60000)
    
        # 校验返回按钮存在
        back_button = self.FE.ele(mweb_promotion_page_ele.ele_activity_back_button)
        assert back_button.is_visible(), "活动页面返回按钮不可见"
    
        # 校验返回按钮文本
        back_text = back_button.text_content()
        assert "返回" in back_text, f"返回按钮文本不正确: {back_text}"
    
        # 校验分享按钮存在
        share_button = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_button)
        assert share_button.is_visible(), "活动页面分享按钮不可见"
    
        log.info("活动页面基本元素校验完成")
    
    
    def _check_activity_share_function(self):
        """
        校验活动页面分享功能
        """
        # 点击分享按钮
        share_button = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_button)
        share_button.click()
        self.page.wait_for_timeout(2000)
    
        # 校验分享弹窗出现
        share_popup = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_popup)
        assert share_popup.is_visible(), "分享弹窗未出现"
    
        # 校验分享弹窗头部
        popup_header = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_popup_header)
        assert popup_header.is_visible(), "分享弹窗头部不可见"
    
        # 校验分享弹窗标题
        popup_title = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_popup_title)
        assert popup_title.is_visible(), "分享弹窗标题不可见"
        title_text = popup_title.text_content()
        assert title_text == "分享", f"分享弹窗标题不正确: {title_text}"
    
        # 校验关闭按钮
        close_button = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_popup_close)
        assert close_button.is_visible(), "分享弹窗关闭按钮不可见"
    
        # 校验分享方式选项
        self._check_share_methods()
    
        # 关闭分享弹窗
        close_button.click()
        self.page.wait_for_timeout(1000)
    
        # 验证弹窗已关闭
        assert not share_popup.is_visible(), "分享弹窗未正确关闭"
    
        log.info("活动页面分享功能校验完成")

    
    def _check_share_methods(self):
        """
        校验分享方式选项
        """


        # 校验复制链接选项
        copy_link_option = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_copy_link)
        assert copy_link_option.is_visible(), "复制链接选项不可见"
    
        # 验证复制链接选项的data-method属性
        copy_method = copy_link_option.get_attribute("data-method")
        assert copy_method == "copyLink", f"复制链接方法属性不正确: {copy_method}"
    
        # 验证复制链接选项的文本
        copy_text = copy_link_option.text_content()
        assert "复制链接" in copy_text, f"复制链接文本不正确: {copy_text}"
    
        log.info("分享方式选项校验完成")

    def goto_promotion_page_and_check_elements(self, promotion_url):
        """
        此方法包含以下功能：
        1. 进入活动页面
        2. 校验页面基本元素
        3. 校验活动标题和规则
        4. 检查商品数量和过滤器
        5. 测试价格过滤器功能
        """
        # 1. 进入活动页面
        self.page.goto(promotion_url)
        self.page.wait_for_timeout(5000)

        # 2. 校验页面基本元素
        self._check_promotion_page_elements()

        # 3. 检查商品数量和过滤器功能
        self._check_products_and_filters()

    def _check_promotion_page_elements(self):
        """
        校验活动页面基本元素
        """
        # 校验活动标题
        promotion_title = self.FE.ele(mweb_promotion_page_ele.ele_promotion_drawer_title)
        assert promotion_title.is_visible(), "活动标题不可见"
        title_text = promotion_title.text_content()
        assert title_text, "活动标题为空"

        # 校验活动规则
        promotion_rules = self.FE.ele(mweb_promotion_page_ele.ele_promotion_drawer_rules)
        assert promotion_rules.is_visible(), "活动规则不可见"
        rules_text = promotion_rules.text_content()
        assert rules_text, "活动规则为空"

        # 校验商品列表容器
        product_list = self.FE.ele(mweb_promotion_page_ele.ele_promotion_product_list)
        assert product_list.is_visible(), "商品列表容器不可见"

        # 校验活动进度信息（如果存在）
        progress_info = self.FE.ele(mweb_promotion_page_ele.ele_promotion_processing_info)
        if progress_info and progress_info.is_visible():
            progress_text = progress_info.text_content()
            log.info(f"活动进度信息: {progress_text}")

        log.info(f"活动标题: {title_text}")
        log.info(f"活动规则: {rules_text}")

    def _check_products_and_filters(self):
        """
        检查商品数量和过滤器功能
        """
        # 获取商品数量
        product_cards = self.FE.eles(mweb_promotion_page_ele.ele_promotion_product_card)
        product_count = len(product_cards)
        log.info(f"页面商品数量: {product_count}")

        # 检查是否存在价格过滤器
        price_filters = self.FE.ele(mweb_promotion_page_ele.ele_promotion_price_filters)

        if product_count > 20 and price_filters and price_filters.is_visible():
            log.info("商品超过20个，发现价格过滤器，开始测试过滤功能")
            self._test_price_filters()
        elif product_count > 20:
            log.warning("商品超过20个但未发现价格过滤器")
        else:
            log.info(f"商品数量为{product_count}，未达到显示过滤器的条件")

    def _test_price_filters(self):
        """
        测试价格过滤器功能
        """
        # 获取所有过滤器选项
        filter_items = self.FE.eles(mweb_promotion_page_ele.ele_promotion_price_filter_item)

        if len(filter_items) > 1:
            # 记录过滤前的商品数量
            products_before = self.FE.eles(mweb_promotion_page_ele.ele_promotion_product_card)
            count_before = len(products_before)

            # 点击第二个过滤器选项（Under $5）
            second_filter = filter_items[1]
            filter_text = second_filter.text_content()
            log.info(f"点击价格过滤器: {filter_text}")

            second_filter.click()
            self.page.wait_for_timeout(3000)

            # 检查过滤后的商品数量
            products_after = self.FE.eles(mweb_promotion_page_ele.ele_promotion_product_card)
            count_after = len(products_after)

            log.info(f"过滤前商品数量: {count_before}, 过滤后商品数量: {count_after}")

            # 验证过滤器是否生效
            if count_after != count_before:
                log.info("价格过滤器功能正常")
            else:
                log.warning("价格过滤器可能未生效")