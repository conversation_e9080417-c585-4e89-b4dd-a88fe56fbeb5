from src.Mweb.EC.mweb_ele.mweb_promotion import mweb_promotion_page_ele
from src.Mweb.EC.mweb_pages.mweb_page_pdp import mweb_page_pdp
from src.config.base_config import TEST_URL
from playwright.sync_api import Page
from src.common.commfunc import empty_cart
from src.config.weee.log_help import log
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_common_page import MWebCommonPage


class MWebPromotionPage(MWebCommonPage):
    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入promotion页面
        self.page.goto(TEST_URL + page_url + "?joinEnki=true")
        self.page.wait_for_timeout(5000)
        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        # 关闭首页广告
        self.close_advertisement_in_homepage()

    def close_advertisement_in_homepage(self):
        if self.page.locator("//img[contains(@aria-label, 'close button')]").all():
            self.page.locator("//img[contains(@aria-label, 'close button')]").click()

    def goto_activity_page_and_check_share(self, activity_url):
        """
        此方法包含以下功能：
        1. 进入活动页面
        2. 校验页面基本元素
        3. 校验分享按钮存在
        4. 测试分享功能
        5. 校验分享弹窗内容
        """
        # 1. 进入活动页面
        self.page.goto(activity_url)
        self.page.wait_for_timeout(5000)
    
        # 2. 校验页面基本元素
        self._check_activity_page()
    
        # 3. 校验分享功能
        self._check_activity_share_function()
    
    
    def _check_activity_page(self):
        """
        校验活动页面基本元素
        """
        # 等待页面加载完成
        self.page.wait_for_load_state("networkidle", timeout=60000)
    
        # 校验返回按钮存在
        back_button = self.FE.ele(mweb_promotion_page_ele.ele_activity_back_button)
        assert back_button.is_visible(), "活动页面返回按钮不可见"
    
        # 校验返回按钮文本
        back_text = back_button.text_content()
        assert "返回" in back_text, f"返回按钮文本不正确: {back_text}"
    
        # 校验分享按钮存在
        share_button = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_button)
        assert share_button.is_visible(), "活动页面分享按钮不可见"
    
        log.info("活动页面基本元素校验完成")
    
    
    def _check_activity_share_function(self):
        """
        校验活动页面分享功能
        """
        # 点击分享按钮
        share_button = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_button)
        share_button.click()
        self.page.wait_for_timeout(2000)
    
        # 校验分享弹窗出现
        share_popup = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_popup)
        assert share_popup.is_visible(), "分享弹窗未出现"
    
        # 校验分享弹窗头部
        popup_header = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_popup_header)
        assert popup_header.is_visible(), "分享弹窗头部不可见"
    
        # 校验分享弹窗标题
        popup_title = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_popup_title)
        assert popup_title.is_visible(), "分享弹窗标题不可见"
        title_text = popup_title.text_content()
        assert title_text == "分享", f"分享弹窗标题不正确: {title_text}"
    
        # 校验关闭按钮
        close_button = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_popup_close)
        assert close_button.is_visible(), "分享弹窗关闭按钮不可见"
    
        # 校验分享内容
        self._check_share_popup_content()
    
        # 校验分享方式选项
        self._check_share_methods()
    
        # 关闭分享弹窗
        close_button.click()
        self.page.wait_for_timeout(1000)
    
        # 验证弹窗已关闭
        assert not share_popup.is_visible(), "分享弹窗未正确关闭"
    
        log.info("活动页面分享功能校验完成")
    
    
    def _check_share_popup_content(self):
        """
        校验分享弹窗内容
        """

        # 校验分享图片
        share_image = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_image)
        assert share_image.is_visible(), "分享图片不可见"
    
        # 验证图片属性
        image_alt = share_image.get_attribute("alt")
        assert image_alt == "share popup share image", f"分享图片alt属性不正确: {image_alt}"
    
        # 校验分享标题
        share_title = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_item_title)
        assert share_title.is_visible(), "分享标题不可见"
        title_text = share_title.text_content()
        assert title_text, "分享标题为空"
    
        # 校验分享描述（可能为空）
        share_desc = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_item_desc)
        assert share_desc.is_visible(), "分享描述元素不可见"
    
        log.info(f"分享内容标题: {title_text}")
    
    
    def _check_share_methods(self):
        """
        校验分享方式选项
        """

        # 校验分享方式区域
        share_methods = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_methods)
        assert share_methods.is_visible(), "分享方式区域不可见"
    
        # 校验复制链接选项
        copy_link_option = self.FE.ele(mweb_promotion_page_ele.ele_activity_share_copy_link)
        assert copy_link_option.is_visible(), "复制链接选项不可见"
    
        # 验证复制链接选项的data-method属性
        copy_method = copy_link_option.get_attribute("data-method")
        assert copy_method == "copyLink", f"复制链接方法属性不正确: {copy_method}"
    
        # 验证复制链接选项的文本
        copy_text = copy_link_option.text_content()
        assert "复制链接" in copy_text, f"复制链接文本不正确: {copy_text}"
    
        log.info("分享方式选项校验完成")