"""
<AUTHOR>  AI Assistant
@Version        :  V1.0.0
------------------------------------
@File           :   test_111415_mweb_rewards_total_saving.py
@Description    :  MWeb Total Saving-绿色横幅点击流程验证
@CreateTime     :  2025/07/10
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/07/10
"""
import allure
import pytest
from playwright.sync_api import Page, TimeoutError
from src.Mweb.EC.mweb_ele.mweb_rewards import mweb_rewards_ele
from src.Mweb.EC.mweb_pages.mweb_page_rewards.mweb_page_rewards_total_saving import MWebRewardsTotalSavingPage
from src.config.weee.log_help import log
from src.common.commonui import scroll_one_page_until


@allure.story("Total Saving-绿色横幅点击流程")
class TestMWebRewardsTotalSaving:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.transaction]

    @allure.title("Total Saving-绿色横幅点击流程验证")
    def test_111415_total_saving_banner_click(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        测试移动端 Total Saving 绿色横幅点击流程
        步骤：
        1. 进入移动端rewards页面
        2. 等待页面完全加载
        3. 点击 Total saving 绿色横幅
        4. 等待弹窗弹出
        5. 验证弹窗中出现 total saving 文案
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        try:
            # 初始化移动端 Total Saving 页面
            total_saving_page = MWebRewardsTotalSavingPage(p, h5_autotest_header, browser_context=c)
            log.info("成功初始化移动端 Total Saving 页面")

            # 等待页面完全加载
            p.wait_for_load_state("load")
            log.info("页面加载完成")

            # 1. 等待 Total saving 绿色横幅可见
            p.wait_for_selector(mweb_rewards_ele.ele_rewards_total_saving_banner, state="visible", timeout=30000)
            log.info("Total saving 绿色横幅已可见")

            # 2. 点击 Total saving 绿色横幅
            total_saving_page.click_total_saving_banner()
            log.info("成功点击 Total saving 绿色横幅")
            p.wait_for_timeout(2000)

            # 3. 等待弹窗弹出（等待文案元素出现）
            log.info("开始等待弹窗弹出...")
            p.wait_for_selector(mweb_rewards_ele.ele_rewards_delivery_service_fee_savings, state="visible", timeout=30000)
            log.info("弹窗已弹出")

            # 4. 验证弹窗中出现 total saving 文案
            try:
                # 验证 Delivery and service fee savings 文案
                verification_result = total_saving_page.verify_delivery_service_fee_savings_text()
                
                if verification_result:
                    log.info("✅ 成功验证弹窗中的 total saving 文案")
                else:
                    log.error("❌ 验证弹窗中的 total saving 文案失败")
                    raise AssertionError("弹窗中未找到预期的 total saving 文案")
                
            except Exception as e:
                log.error(f"验证弹窗文案时发生异常: {str(e)}")
                raise

        except Exception as e:
            log.error(f"测试过程中发生异常: {str(e)}")
            # 添加调试信息
            try:
                log.info(f"当前页面URL: {p.url}")
                log.info(f"页面标题: {p.title()}")
                # 截图保存
                import time
                screenshot_path = f"error_screenshot_{int(time.time())}.png"
                p.screenshot(path=screenshot_path)
                log.info(f"错误截图已保存: {screenshot_path}")
            except Exception as debug_e:
                log.error(f"保存调试信息时出错: {str(debug_e)}")
            raise
