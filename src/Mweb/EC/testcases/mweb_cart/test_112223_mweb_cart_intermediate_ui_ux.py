"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_112223_mweb_cart_intermediate_ui_ux.py
@Description    :  购物车-中间-验证中间页的交互
@CreateTime     :  2025/4/25 10:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/4/25 10:30
"""
import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_ele.mweb_home import mweb_home_ele
from src.Mweb.EC.mweb_pages.mweb_page_category.mweb_page_category import MWebCategorypage, verify_cart_items, \
    apply_multiple_filters_and_add_products
from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home import MWebPageHome
from src.common.commfunc import empty_cart
from src.config.weee.log_help import log
from src.api.zipcode import switch_zipcode
from src.config.base_config import TEST_URL


@allure.story("购物车-中间-验证中间页的交互")
class TestMwebCartIntermediatePage:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.zhuli]


    @allure.title("【112223】 购物车-中间-验证中间页的交互")
    @pytest.mark.present
    def test_112223_mweb_cart_intermediate_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【112223】 购物车-中间-验证中间页的交互
        测试步骤：
        1. 清空购物车
        2. 从首页进入Deals分类，添加多种类型的商品到购物车（至少3种不同类型）
           - 通过分类筛选加购本地配送商品
           - 通过分类筛选加购Global+商品
           - 通过分类筛选加购Pantry商品
        3. 点击结算，验证中间页面交互
        4. 清空购物车，添加单一类型商品，验证直接跳转结算页
        5. 再次添加多种类型商品，验证勾选一个购物车后的结算流程
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 0. 切换zipcode到98011
        with allure.step("切换zipcode到98011"):
            try:
                # 如果zipcode不是98011， 则切换到98011
                if not p.locator("//span[text()='98011']").all():
                    switch_zipcode(h5_autotest_header, "98011")
                    p.reload()
                    p.wait_for_load_state("networkidle", timeout=30000)
                    log.info("成功切换zipcode到98011")
            except Exception as e:
                log.warning(f"切换zipcode失败，继续测试: {str(e)}")

        # 1. 清空购物车
        with allure.step("清空购物车"):
            try:
                empty_cart(h5_autotest_header)
                log.info("购物车清空成功")
            except Exception as e:
                log.error(f"清空购物车失败: {str(e)}")
                # 继续测试，不中断

        # 从首页进入Deals分类
        with allure.step("从首页进入Deals分类"):
            # 创建首页对象
            home_page = MWebPageHome(p, h5_autotest_header, bc=c)
            home_page.go_to_special_category_from_home(mweb_home_ele.ele_deals)
            p.wait_for_load_state("networkidle", timeout=30000)
            p.wait_for_timeout(2000)

            # 创建分类页面对象
            category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="/category/sale")

            log.info("成功从首页进入Deals分类")

        # 3. 添加多种类型的商品
        with allure.step("添加多种类型的商品"):
            # 配置多个筛选条件
            filters_config = [
                {
                    "filter_type": "Local Delivery",
                    "test_id": mweb_category_ele.ele_local_delivery_xpath,
                    "items_to_add": 1
                },
                {
                    "filter_type": "Pantry",
                    "test_id": mweb_category_ele.ele_pantry_delivery_xpath,
                    "items_to_add": 1
                }
            ]

            # 应用多个筛选条件并加购商品
            results = apply_multiple_filters_and_add_products(p, filters_config)

            # 验证结果
            local_added = results.get("Local Delivery", 0)
            pantry_added = results.get("Pantry", 0)

            assert local_added > 0, "未能添加Local Delivery商品"
            assert pantry_added > 0, "未能添加Pantry商品"

            total_added = local_added + pantry_added
            log.info(f"成功添加{local_added}个Local Delivery商品和{pantry_added}个Pantry商品，总共{total_added}个商品")

        # 5. 进入购物车页面，验证商品已添加成功
        with allure.step("进入购物车页面，验证商品已添加成功"):
            # 进入购物车页面
            p.goto(f"{TEST_URL}/cart")
            p.wait_for_load_state("networkidle", timeout=30000)
            p.wait_for_timeout(3000)

            # 验证购物车中的商品
            assert verify_cart_items(p), "购物车中没有找到商品，加购可能失败"

            # 获取购物车商品
            cart_items = p.query_selector_all(mweb_cart_ele.ele_cart_normal_card)
            log.info(f"购物车中有{len(cart_items)}个商品")

            # 验证购物车中商品数量与加购数量一致
            # assert len(cart_items) >= total_added, f"购物车中商品数量({len(cart_items)})少于加购数量({total_added})"

            # 验证购物车中有不同类型的商品
            normal_cart = p.locator(mweb_cart_ele.ele_cart_normal).is_visible(timeout=3000)
            pantry_cart = p.locator(mweb_cart_ele.ele_cart_pantry).is_visible(timeout=3000)

            if local_added > 0 and pantry_added > 0:
                assert normal_cart and pantry_cart, "购物车中未显示所有类型的商品"
            elif local_added > 0:
                assert normal_cart, "购物车中未显示Local Delivery商品"
            elif pantry_added > 0:
                assert pantry_cart, "购物车中未显示Pantry商品"

            log.info("成功验证购物车中的商品")

        # 3. 点击结算，验证中间页面交互
        with (allure.step("点击结算按钮，验证中间页面元素")):
            # 滚动到页面底部找到结算按钮
            p.evaluate('window.scrollTo(0, document.body.scrollHeight)')
            p.wait_for_timeout(2000)

            # 点击结算按钮
            checkout_btn =p.get_by_test_id("btn-checkout")
            p.locator(mweb_cart_ele.ele_cart_checkout_button)
            assert checkout_btn.is_visible(timeout=2000), "结算按钮不可见"

            log.info("找到结算按钮，准备点击")
            checkout_btn.click()
            p.wait_for_timeout(2000)

            # 验证中间页面元素
            # 1. 验证标题
            title = p.locator(mweb_cart_ele.ele_cart_middle_title)
            assert title.is_visible(timeout=2000), "中间页标题不可见"
            assert title.text_content() == "Select carts for checkout", f"中间页标题文本不正确: {title.text_content()}"
            log.info("验证中间页标题成功")

            # 2. 验证全选按钮
            select_all = p.locator(mweb_cart_ele.ele_cart_select_all)
            assert select_all.is_visible(timeout=5000), "全选按钮不可见"

            select_all_text = p.locator(mweb_cart_ele.ele_cart_select_all_text)
            assert select_all_text.is_visible(), "全选文本不可见"
            assert "Select all carts" in select_all_text.text_content(), f"全选文本不正确: {select_all_text.text_content()}"
            log.info("验证全选按钮成功")

            # 3. 验证购物车类型
            normal_cart = p.locator(mweb_cart_ele.ele_cart_normal_id)
            pantry_cart = p.locator(mweb_cart_ele.ele_cart_pantry_id)

            assert normal_cart.is_visible(timeout=3000), "Local Delivery购物车不可见"
            assert pantry_cart.is_visible(timeout=3000), "Pantry购物车不可见"
            log.info("验证购物车类型成功")

            # 4. 验证购物车选择框
            normal_select = p.locator(mweb_cart_ele.ele_cart_select_normal)
            pantry_select = p.locator(mweb_cart_ele.ele_cart_select_pantry)

            assert normal_select.is_visible(timeout=3000), "Local Delivery购物车选择框不可见"
            assert pantry_select.is_visible(timeout=3000), "Pantry购物车选择框不可见"
            log.info("验证购物车选择框成功")

            # 5. 验证底部提示文本
            tip = p.locator(mweb_cart_ele.ele_cart_middle_tip)
            assert tip.is_visible(timeout=3000), "底部提示文本不可见"
            assert "You can select multiple carts for checkout" in tip.text_content(), f"底部提示文本不正确: {tip.text_content()}"
            log.info("验证底部提示文本成功")

            # 6. 验证结算按钮-- 有2个一模一样的test_id后续优化
            checkout_btn = p.locator(mweb_cart_ele.ele_cart_middle_checkout)
            assert checkout_btn.is_visible(timeout=3000), "结算按钮不可见"

            # 验证结算按钮是否禁用
            btn_class = checkout_btn.get_attribute("class") or ""
            assert "disabled" in btn_class or "btn-disabled" in btn_class, f"结算按钮未被禁用: {btn_class}"
            log.info("验证结算按钮成功")

            # 7. 验证小计金额--小计购物车跟中间页有重复的test_id后续优化
            subtotal = p.locator(mweb_cart_ele.ele_cart_middle_subtotal)
            assert subtotal.is_visible(timeout=3000), "小计金额不可见"
            assert "$0.00" in subtotal.text_content(), f"小计金额不正确: {subtotal.text_content()}"
            log.info("验证小计金额成功")

            # 8. 验证关闭按钮
            close_btn = p.locator(mweb_cart_ele.ele_cart_middle_close)
            assert close_btn.is_visible(timeout=3000), "关闭按钮不可见"
            log.info("验证关闭按钮成功")

            # 点击关闭按钮
            close_btn.click()
            p.wait_for_timeout(2000)

            # 验证中间页已关闭
            assert not p.locator(mweb_cart_ele.ele_cart_middle_title).is_visible(timeout=3000), "关闭按钮点击后中间页仍然可见"
            log.info("成功关闭中间页")

            log.info("所有中间页元素验证成功")