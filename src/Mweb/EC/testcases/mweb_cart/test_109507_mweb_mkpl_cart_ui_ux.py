"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_109507_mweb_mkpl_cart_ui_ux.py
@Description    :  购物车-验证从首页进入Deals筛选Global+加购商品后点击结算
@CreateTime     :  2025/4/25 10:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/4/25 10:30
"""
import allure
import pytest
from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_ele.mweb_checkout import mweb_checkout_ele
from src.Mweb.EC.mweb_ele.mweb_home import mweb_home_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_category.mweb_page_category import MWebCategorypage
from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home import MWebPageHome
from src.common.commfunc import empty_cart
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log
from src.api.zipcode import switch_zipcode


@allure.story("H5购物车-mkpl购物车UI/UX验证")
class TestMwebCartGlobalCheckout:
    pytestmark = [pytest.mark.mweb_regression]

    @allure.title("H5购物车-mkpl购物车UI/UX验证")
    @pytest.mark.present
    def test_109507_mweb_mkpl_cart_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        购物车-验证从首页进入Deals筛选Global+加购商品后点击结算
        测试步骤：
        1. 清空购物车
        2. 从首页进入Deals分类
        3. 筛选并加购Global+商品
        4. 进入购物车页面并点击结算
        5. 验证跳转到结算页面
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 准备测试环境
        with allure.step("准备测试环境"):
            # 切换zipcode到98011
            try:
                if not p.locator("//span[text()='98011']").all():
                    switch_zipcode(h5_autotest_header, "98011")
                    p.reload()
                    log.info("成功切换zipcode到98011")
            except Exception as e:
                log.warning(f"切换zipcode失败，继续测试: {str(e)}")
            
            # 清空购物车
            empty_cart(h5_autotest_header)
            # 2. 使用封装方法加购Local Delivery商品
        with allure.step("使用封装方法加购global+商品"):
            # 创建分类页面对象
            category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="/category/sale")
            p.wait_for_timeout(2000)

            # 检查筛选按钮是否存在
            filter_button = p.get_by_test_id("btn-sub-category-filter")
            log.info(f"筛选按钮是否可见: {filter_button.is_visible(timeout=1000)}")

            # 检查Local Delivery元素选择器
            log.info(f"global+元素选择器: {mweb_category_ele.ele_global_delivery}")

            # 尝试直接使用XPath定位Local Delivery选项
            global_filter_id = p.get_by_test_id(mweb_category_ele.ele_global_delivery)

            # 调用封装方法加购global+商品
            added_count = category_page.add_products_from_home_by_filter(
                filter_name="global+",
                filter_id=global_filter_id,
                count=2,  # 加购2个商品
            )
            assert added_count > 0, "未能成功加购mkpl商品"
            p.wait_for_timeout(2000)  # 增加等待时间
                # 3. 进入购物车页面
            with allure.step("进入购物车页面"):
                # 创建购物车页面对象
                cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
                p.wait_for_timeout(2000)

                # 关闭可能出现的广告弹窗
                if p.locator("//img[contains(@aria-label, 'close button')]").all():
                    p.locator("//img[contains(@aria-label, 'close button')]").click()
                    log.info("关闭广告弹窗")

                # 4. 验证购物车UI
            with allure.step("验证mkpl购物车UI"):
                # 滚动回购物车顶部
                log.info("滚动回购物车顶部")
                scroll_one_page_until(p, mweb_cart_ele.ele_cart_seller)
                assert cart_page.FE.ele(mweb_cart_ele.ele_cart_seller).is_visible(), "global+购物车不存在"
                p.wait_for_timeout(2000)

                # 判断生鲜购物车的标题=Local delivery
                # assert "Local delivery" == cart_page.FE.ele(
                #     mweb_cart_ele.ele_cart_grocery_top_title).text_content(), "购物车标题不正确"
                # 判断只有购物车标题下面的文案显示正确
                assert "Shipping via FedEx, UPS, etc." == cart_page.FE.ele(
                    mweb_cart_ele.ele_mo_cart_text).text_content(), "mkpl购物车配送文案不正确"

                # 验证购物车商品
                with allure.step("验证购物车商品"):
                    # 使用合并后的方法验证购物车商品
                    assert cart_page.verify_cart_items(cart_type="seller"), "购物车商品验证失败"

                # 执行稍后再买操作
                with allure.step("执行稍后再买操作"):
                    assert cart_page.save_for_later_operations(cart_type="seller"), "稍后再买操作失败"
            p.wait_for_timeout(2000)

            # 执行购物车商品删除操作
            with allure.step("执行商品删除操作"):
                assert cart_page.remove_cart_item(cart_type="seller"), "mkpl商品删除操作失败"

            log.info("mkpl购物车UI自动化验证完成")
