"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_112719_mweb_grocery_cart_ui_ux.py
@Description    :  
@CreateTime     :  2025/4/21 14:22
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/4/21 14:22
"""
import re
import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage

from src.api.zipcode import switch_zipcode
from src.common.commfunc import empty_cart
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log


@allure.story("H5购物车-生鲜购物车UI/UX验证")
class TestMwebGroceryCartUIUX:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.zhuli]

    @allure.title("H5生鲜购物车UI自动化验证")
    @pytest.mark.fresh
    def test_fresh_grocery_cart_ui(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        生鲜购物车UI自动化验证:
        1. 调用切换zipcode接口切换到98011
        2. 加购为你推荐商品
        3. 遍历购物车商品
        4. 获取购物车商品标题和价格(带$符号)
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 调用切换zipcode接口切换到98011
        log.info("切换zipcode到98011")
        res = switch_zipcode(h5_autotest_header, "98011")
        assert res.get('object') == 'Success', f"切换zipcode失败，res={res}"
        p.wait_for_timeout(2000)

        # 进入购物车页面
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        p.wait_for_timeout(2000)

        # 关闭可能出现的广告弹窗
        if p.locator("//img[contains(@aria-label, 'close button')]").all():
            p.locator("//img[contains(@aria-label, 'close button')]").click()
            log.info("关闭广告弹窗")
        try:
            empty_cart(h5_autotest_header)
            log.info("清空购物车成功")
            p.wait_for_timeout(2000)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
            p.wait_for_timeout(3000)

        # 2. 滚动到推荐商品区域
        log.info("滚动到推荐商品区域")
        scroll_one_page_until(p, mweb_cart_ele.ele_recommend_tab)
        p.wait_for_timeout(1000)

        # 获取推荐商品列表
        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)
        added_count = 0

        # 加购推荐商品
        log.info("开始加购推荐商品")
        for index, item in enumerate(recommend_card):
            plus_btn = item.query_selector("[data-testid='btn-atc-plus']")
            if not plus_btn:
                # 备用定位方式
                plus_btn = item.query_selector(mweb_cart_ele.ele_cart_atc_normal_plus)

            if plus_btn and plus_btn.is_visible():
                plus_btn.click()
                p.wait_for_timeout(1000)
                added_count += 1
                log.info(f"成功添加第 {added_count} 个推荐商品")

                # 添加2个商品后停止
                if added_count >= 2:
                    break


        assert added_count > 0, "没有成功添加商品到购物车"

        # 等待页面加载完成
        p.wait_for_timeout(2000)

        # 3. 滚动回购物车顶部
        log.info("滚动回购物车顶部")
        scroll_one_page_until(p, mweb_cart_ele.ele_cart_normal_card)
        assert cart_page.FE.ele(mweb_cart_ele.ele_cart_normal).is_visible(), "生鲜购物车不存在"
        p.wait_for_timeout(2000)

        # 判断生鲜购物车的标题=Local delivery
        assert "Local delivery" == cart_page.FE.ele(mweb_cart_ele.ele_cart_grocery_top_title).text_content()
        # 判断只有购物车标题下面的文案显示正确
        assert "Delivered by Weee! Truck" == cart_page.FE.ele(mweb_cart_ele.ele_grocery_cart_text).text_content()

        # 4. 遍历购物车商品，获取标题和价格
        # 使用test_id定位购物车商品
        cart_items = p.locator("[data-testid='cart-normal-card']").all()
        if not cart_items:
            # 备用定位方式
            cart_items = cart_page.FE.eles(mweb_cart_ele.ele_cart_normal_card)

        assert len(cart_items) > 0, "购物车中没有商品"
        log.info(f"购物车中共有 {len(cart_items)} 个商品")

        # 遍历购物车商品
        for index, item in enumerate(cart_items):
            # 获取商品标题
            title_element = item.query_selector("//div[@data-testid='wid-product-card-title']")

            assert title_element, f"未找到第 {index + 1} 个商品的标题元素"
            title = title_element.text_content().strip()
            log.info(f"商品 {index + 1} 标题: {title}")
            assert len(title) > 0, f"商品 {index + 1} 标题为空"

            # 获取商品价格
            price_element = item.query_selector("div[data-testid='wid-product-card-price']")
            assert price_element, f"未找到第 {index + 1} 个商品的价格元素"
            price = price_element.text_content().strip()
            log.info(f"商品 {index + 1} 价格: {price}")

            # 验证价格包含$符号
            assert "$" in price, f"商品 {index + 1} 价格 {price} 不包含 $ 符号"

            # 验证价格格式（$xx.xx）
            # 有些商品有几个价格，text_content取值为：$1.99$2.99，不能匹配，去掉此校验
            #price_pattern = r'^\$\d+\.\d{2}$'
            #assert re.match(price_pattern, price), f"商品 {index + 1} 价格 {price} 格式不正确"

        log.info("生鲜购物车UI自动化验证完成")
