"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test.py
@Description    :  
@CreateTime     :  2025/5/16 13:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/5/16 13:30
"""
import allure
import pytest
from playwright.sync_api import Page

from src.common.commfunc import empty_cart
from src.config.weee.log_help import log

class TestMwebCartIntermediatePage22:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.zhuli]

    @allure.title("【112223】 购物车-验证加购一个购物车的交互")
    @pytest.mark.present
    def verify_single_cart_checkout(self, page: Page, category_page, filter_type, test_id, h5_autotest_header):
        """
        验证单一购物车直接跳转结算页

        Args:
            page: Playwright页面对象
            category_page: 分类页面对象
            filter_type: 筛选类型名称
            test_id: 筛选按钮的test-id
            h5_autotest_header: 请求头信息

        Returns:
            bool: 验证是否成功
        """
        with allure.step(f"验证单一{filter_type}购物车直接跳转结算页"):
            try:
                # 返回购物车页面
                page.go_back()
                page.wait_for_timeout(2000)

                # 清空购物车
                empty_cart(h5_autotest_header)
                page.reload()
                page.wait_for_load_state("networkidle", timeout=30000)
                page.wait_for_timeout(2000)

                # 进入分类页面添加单一类型商品
                page.goto("/category/sale")
                page.wait_for_load_state("networkidle", timeout=30000)
                page.wait_for_timeout(3000)

                # 只添加指定类型商品
                added_count = category_page.add_products_by_category_filter(
                    page, category_page, filter_type, test_id, 2
                )

                if added_count <= 0:
                    log.error(f"未能添加{filter_type}商品")
                    return False

                # 进入购物车页面
                page.goto("/cart")
                page.wait_for_load_state("networkidle", timeout=30000)
                page.wait_for_timeout(3000)

                page.evaluate('window.scrollTo(0, document.body.scrollHeight)')
                page.wait_for_timeout(2000)
                # 点击结算按钮
                checkout_btn = page.get_by_test_id("btn-checkout")
                if not checkout_btn.is_visible(timeout=5000):
                    log.error("结算按钮不可见")
                    return False

                checkout_btn.click()
                page.wait_for_timeout(3000)
                page.wait_for_load_state("networkidle", timeout=30000)

                # 验证直接跳转到结算页（检查结算页特有元素）
                checkout_header = page.get_by_test_id("wid-checkout-header")
                if not checkout_header.is_visible(timeout=5000):
                    log.error("未直接跳转到结算页")
                    return False

                log.info(f"单一{filter_type}购物车成功直接跳转到结算页")
                return True

            except Exception as e:
                log.error(f"验证单一{filter_type}购物车直接跳转结算页失败: {str(e)}")
                import traceback
                log.error(traceback.format_exc())
                return False
