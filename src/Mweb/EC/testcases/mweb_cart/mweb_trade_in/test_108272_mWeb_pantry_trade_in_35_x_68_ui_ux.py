import allure
import pytest
from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_trade_in_ele
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_recommendations_ele
from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_trade_in_page import MWebTradeInPage
from src.Mweb.EC.mweb_pages.mweb_page_category.mweb_page_category import MWebCategorypage
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart, CommonCheck



@allure.story("【108272】 Pantry-$35<X<$68-换购页面UI/UX验证")
class TestMWebPantryTradeInUIUX:
    def setup_cart_page(self, phone_page, h5_autotest_header):
        """
        初始化购物车页面
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 如果不是98011，切换回98011
        try:
            CommonCheck().set_porder(h5_autotest_header, 98011)
        except Exception as e:
            log.info("账号没有在98011下" + str(e))
        try:
            empty_cart(h5_autotest_header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        # 进入指定页面
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        # 滚动到指定位置-猜你喜欢
        scroll_one_page_until(p, mweb_cart_ele.ele_recommend_tab)

        return p, c, cart_page

    pytestmark = [pytest.mark.h5cart, pytest.mark.todo, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108272-1】 Pantry-金额小于$35时换购模块验证")
    def test_108272_mWeb_pantry_cart_trade_in_less_35_ui_ux(self, phone_page: dict, h5_autotest_header,
                                                            h5_open_and_close_trace):
        """
        【109167-1】 购物车-金额小于$35时换购模块验证
        测试步骤：
        1. 清空购物车
        2. 调用分类加购商品的公共方法add_category_filter_product_to_cart 加购商品
        3.判断生鲜购物车data-testid="wid-cart-normal" 的金额data-testid="wid-cart-total-price"
        4. 如果金额大于35，需要删除：data-testid="wid-cart-section-normal-goods-remove-btn"第一个商品，然后再判断金额，直到金额小于35
        5. 如果金额小于35，不会显示换购模块
        6.此时还是显示的免运费banner data-testid="wid-cart-section-normal-shipping-fee-banner"

        3. 验证不显示购物车换购模块
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="")

        # 1. 清空购物车
        try:
            empty_cart(h5_autotest_header)
            log.info("成功清空购物车")
        except Exception as e:
            log.info(f"清空购物车发生异常: {str(e)}")

        # 2. 切指定zipcode
        try:
            CommonCheck().set_porder(h5_autotest_header, 98011)
            log.info("成功切换到98011 zipcode")
        except Exception as e:
            log.info(f"账号没有在98011下: {str(e)}")
        # 关闭首页广告
        category_page.close_advertisement_in_homepage()

        # 3. 调用分类加购商品的公共方法加购商品
        added_count = category_page.add_category_filter_product_to_cart(
            mweb_category_ele.ele_cate_sale,
            mweb_category_ele.ele_pantry_delivery, category="sale",
            count=6
        )
        assert added_count > 0, "加购商品失败"
        log.info(f"成功加购{added_count}个商品")

        # 进入购物车页面
        # 点击购物车按钮进入购物车
        p.get_by_test_id("wid-cart").click()
        p.wait_for_timeout(2000)

        # 4. 判断生鲜购物车的金额
        cart_pantry = p.get_by_test_id(mweb_cart_ele.ele_cart_pantry)
        assert cart_pantry.is_visible(), "生鲜购物车不可见"

        # 获取购物车金额
        cart_total_price = cart_pantry.get_by_test_id("wid-cart-total-price")
        total_text = cart_total_price.text_content()
        cart_amount = float(total_text.replace('$', '').replace(',', ''))
        log.info(f"当前购物车金额: ${cart_amount}")
        # shipping_fee = cart_pantry.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_price)
        # shipping_free = cart_pantry.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_free_price)

        # 5. 如果金额大于35，需要删除商品直到金额小于35
        while True:
            try:
                # 如果满足免运费
                shipping_free = cart_pantry.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_free_price)
                if shipping_free.is_visible():

                    log.info("购物车金额已经满足免运费，删除第一个商品")
                    remove_btn = p.get_by_test_id(mweb_cart_ele.ele_cart_pantry_remove).first
                    remove_btn.click()
                    p.wait_for_timeout(2000)
                    # 重新获取金额
                    try:
                        total_text = p.get_by_test_id("wid-cart-total-price").text_content()
                        cart_amount = float(total_text.replace('$', '').replace(',', ''))
                        log.info(f"删除商品后，当前购物车金额: ${cart_amount}")
                    except:
                        # 如果购物车为空，金额为0
                        cart_amount = 0
                        log.info("购物车已空，金额为$0")
                    break

                # 确认还在收运费
                shipping_fee = cart_pantry.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_price)
                assert shipping_fee.is_visible(), f"购物车金额应小于$35，实际为${cart_amount}"
                log.info(f"确认购物车金额小于$35: ${cart_amount}")

                # 6. 如果金额小于35，不会显示换购模块
                trade_in_module = cart_pantry.get_by_test_id("mod-cart-activity-cart_deal")
                assert not trade_in_module.is_visible(), "金额小于$35时不应显示换购模块"
                log.info("验证金额小于$35时不显示换购模块")

                # 7. 此时还是显示的凑运费banner
                shipping_banner = cart_pantry.get_by_test_id("wid-cart-selection-detail-shop-more-bar")
                assert shipping_banner.is_visible(), "金额小于$35时应显示免运费banner"
                log.info("验证金额小于$35时显示免运费banner")

                log.info("【108272-1】 购物车-金额小于$35时换购模块验证完成")
            except:
                break

    @allure.title("【108272-2】 Pantry-换购页-金额大于$35小于$68时换购模块验证")
    def test_108272_mWeb_pantry_trade_in_35_to_68_ui_ux(self, phone_page: dict, h5_autotest_header,
                                                        h5_open_and_close_trace):
        """
        【109167-2】 购物车-金额大于免运费金额小于$68时换购模块验证
        测试步骤：
        1. 清空购物车
        2. 调用分类加购商品的公共方法add_category_filter_product_to_cart 加购商品
        3. 判断生鲜购物车data-testid="wid-cart-normal" 的金额data-testid="wid-cart-total-price"
        4. 如果金额大于68，需要删除：data-testid="wid-cart-section-normal-goods-remove-btn"第一个商品，然后再判断金额，直到金额小于68
        5. 如果金额小于35，需要继续加购
        6. 当金额大于$35小于$68
        7. 此时显示购物车换购模块
        8. 验证换购文案提示包含"to unlock extra deals!"
        9. 验证换购卡片上不可加购 data-testid="wid-cart-activity-lock"
        10.验证换购模块下有view more 按钮data-testid="btn-cart-activity-bottom-view-more"
        11.滑动换购模块商品data-testid="wid-product-card-container"到后面，也有 view more按钮data-testid="wid-cart-activity-list-view-more"
        12. 点击view more按钮，进入换购页面/promotion/trade-in
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="")
        trade_in_page = MWebTradeInPage(p, h5_autotest_header, browser_context=c, page_url="")

        # 1. 清空购物车
        try:
            empty_cart(h5_autotest_header)
            log.info("成功清空购物车")
        except Exception as e:
            log.info(f"清空购物车发生异常: {str(e)}")

        # 2. 调用分类加购商品的公共方法加购商品
        added_count = category_page.add_category_filter_product_to_cart(
            "wid-nav-tab-deals",
            mweb_category_ele.ele_pantry_delivery,
            count=6
        )
        # assert added_count > 0, "加购商品失败"
        log.info(f"成功加购{added_count}个商品")

        # 点击购物车按钮进入购物车
        # p.goto("https://www.sayweee.com/en/cart")
        p.get_by_test_id("wid-cart").click()
        p.wait_for_timeout(2000)

        # 3. 判断生鲜购物车的金额
        cart_pantry = p.get_by_test_id(mweb_cart_ele.ele_cart_pantry)
        assert cart_pantry.is_visible(), "生鲜购物车不可见"

        # 获取购物车金额
        cart_total_price = p.get_by_test_id("wid-cart-total-price")
        total_text = cart_total_price.text_content()
        cart_amount = float(total_text.replace('$', '').replace(',', ''))
        log.info(f"当前购物车金额: ${cart_amount}")
        # shipping_fee = cart_pantry.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_price)
        # shipping_free = cart_pantry.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_free_price)

        # 4. 如果已经满足免运费门槛（shipping_free存在）但是超过了$68，需要删除商品直到不满足免运费
        while True:
            try:
                shipping_free = cart_pantry.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_free_price)
                if shipping_free.is_visible() and cart_amount >= 68:
                    log.info("购物车已满足免运费门槛，删除第一个商品")
                    remove_btn = p.get_by_test_id(mweb_cart_ele.ele_cart_pantry_remove).first
                    remove_btn.click()
                    p.wait_for_timeout(2000)
                    # 重新获取金额
                    try:
                        total_text = p.get_by_test_id("wid-cart-total-price").text_content()
                        cart_amount = float(total_text.replace('$', '').replace(',', ''))
                        log.info(f"删除商品后，当前购物车金额: ${cart_amount}")
                    except:
                        cart_amount = 0
                        log.info("购物车已空，金额为$0")
                        break
                else:
                    break
            except:
                break

        # 5. 如果还在收运费但金额过低（shipping_fee存在但需要加购到换购门槛），需要继续加购
        while True:
            try:
                shipping_fee = cart_pantry.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_price)
                if shipping_fee.is_visible():  # 假设35是换购门槛
                    log.info("购物车还在收运费且金额过低，需要继续加购商品")
                    # 返回分类页面继续加购
                    p.go_back()
                    p.wait_for_timeout(2000)

                    # 继续加购商品
                    additional_count = category_page.add_category_filter_product_to_cart(
                        "wid-nav-tab-deals",
                        mweb_category_ele.ele_pantry_delivery,
                        count=3
                    )
                    log.info(f"继续加购{additional_count}个商品")

                    # 重新进入购物车
                    p.get_by_test_id("wid-cart").click()
                    p.wait_for_timeout(2000)

                    # 重新获取金额
                    total_text = p.get_by_test_id("wid-cart-total-price").text_content()
                    cart_amount = float(total_text.replace('$', '').replace(',', ''))
                    log.info(f"加购后，当前购物车金额: ${cart_amount}")
                else:
                    break
            except:
                break

        # 6. 确认金额在$35-$68之间
        shipping_free = cart_pantry.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_free_price)
        assert shipping_free.is_visible() and cart_amount < 68, f"购物车金额应在$35-$68之间，实际为${cart_amount}"
        log.info(f"确认购物车金额在$35-$68之间: ${cart_amount}")
        # 7. 此时也不显示购物车换购模块
        trade_in_module = p.get_by_test_id("mod-cart-activity-cart_deal")
        assert not trade_in_module.is_visible(), "金额在$35-$68之间时应显示换购模块"
        log.info("验证显示购物车换购模块")
        # 显示换购banner
        shop_more_bar = cart_pantry.get_by_test_id("wid-cart-selection-detail-shop-more-bar")
        shop_more_bar_tag = cart_pantry.get_by_test_id("wid-cart-selection-detail-shop-more-bar-tag")
        shop_more_bar_text = cart_pantry.get_by_test_id("wid-cart-selection-detail-shop-more-bar-tag-text")
        shop_more_bar_content = cart_pantry.get_by_test_id("wid-cart-selection-detail-shop-more-bar-content")
        shop_more_bar_more = cart_pantry.get_by_test_id("wid-cart-selection-detail-shop-more-bar-btn")
        shop_more_bar_more_text = cart_pantry.get_by_test_id("wid-cart-selection-detail-shop-more-bar-btn-text")

        assert shop_more_bar.is_visible()
        assert shop_more_bar_tag.is_visible()
        assert shop_more_bar_text.is_visible()
        assert shop_more_bar_content.is_visible()
        assert shop_more_bar_more.is_visible()
        assert shop_more_bar_more_text.is_visible()
        # 12. 点击view 按钮，进入换购页面
        shop_more_bar_more.click()
        p.wait_for_timeout(3000)

        # 验证成功跳转到换购页面
        assert "/promotion/trade-in" in p.url, f"应跳转到换购页面，实际URL: {p.url}"
        log.info("验证成功跳转到换购页面/promotion/trade-in")
        # 换购页面 断言
        assert p.get_by_test_id(mweb_trade_in_ele.ele_trade_in_tip).is_visible()
        assert p.get_by_test_id(mweb_trade_in_ele.ele_trade_in_txt).is_visible()
        assert p.get_by_test_id(mweb_trade_in_ele.ele_trade_in_card).is_visible()
        trade_in_cards = p.get_by_test_id(mweb_trade_in_ele.ele_trade_in_card).all()
        # 换购卡片公共断言
        trade_in_page.trade_in_card_assert(trade_in_cards, cart_amount)

        # 点击 shop more 跳转分类页面
        shop_more = p.get_by_test_id(mweb_trade_in_ele.ele_trade_in_shop_more)
        assert shop_more.is_visible()
        shop_more.click()
        assert "/category/trending" in p.url, f"应跳转到分类页面，实际URL: {p.url}"

        log.info("【108272-2】 购物车-金额大于$35小于$68时换购模块验证完成")

    @allure.title("【108272-3】 Pantry-换购页-金额大于$68时换购模块验证")
    def test_108272_mWeb_pantry_trade_in_more_68_ui_ux(self, phone_page: dict, h5_autotest_header,
                                                       h5_open_and_close_trace):
        """
        【109167-3】 购物车-金额大于$68时换购模块验证
        测试步骤：
        1. 清空购物车
        2. 从分类加购商品，确保金额大于$68
        3. 验证显示购物车换购模块
        4. 验证换购卡片上可加购
        5. 点击加购换购商品
        6. 验证换购商品加入购物车成功
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="")
        trade_in_page = MWebTradeInPage(p, h5_autotest_header, browser_context=c, page_url="")

        # 1. 清空购物车
        try:
            empty_cart(h5_autotest_header)
            log.info("成功清空购物车")
        except Exception as e:
            log.info(f"清空购物车发生异常: {str(e)}")

        # 2. 调用分类加购商品的公共方法加购商品
        added_count = category_page.add_category_filter_product_to_cart(
            "wid-nav-tab-deals",
            mweb_category_ele.ele_pantry_delivery,
            count=6
        )
        assert added_count > 0, "加购商品失败"
        log.info(f"成功加购{added_count}个商品")

        # 点击购物车按钮进入购物车
        p.get_by_test_id("wid-cart").click()
        p.wait_for_timeout(2000)

        # 3. 判断生鲜购物车的金额
        cart_pantry = p.get_by_test_id(mweb_cart_ele.ele_cart_pantry)
        assert cart_pantry.is_visible(), "生鲜购物车不可见"

        # 获取购物车金额
        cart_total_price = p.get_by_test_id("wid-cart-total-price")
        total_text = cart_total_price.text_content()
        cart_amount = float(total_text.replace('$', '').replace(',', ''))
        log.info(f"当前购物车金额: ${cart_amount}")
        # shipping_fee = cart_pantry.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_price)
        # shipping_free = cart_pantry.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_free_price)

        while cart_amount < 68:
            # 继续加购商品
            additional_count = category_page.add_category_filter_product_to_cart(
                "wid-nav-tab-deals",
                mweb_category_ele.ele_pantry_delivery,
                count=3
            )
            log.info(f"继续加购{additional_count}个商品")

            # 重新进入购物车
            p.get_by_test_id("wid-cart").click()
            p.wait_for_timeout(2000)

            # 重新获取金额
            total_text = p.get_by_test_id("wid-cart-total-price").text_content()
            cart_amount = float(total_text.replace('$', '').replace(',', ''))
            log.info(f"加购后，当前购物车金额: ${cart_amount}")
        # 6. 确认金额>$68
        assert cart_amount >= 68, f"购物车金额应在$35-$68之间，实际为${cart_amount}"
        log.info(f"确认购物车金额在$35-$68之间: ${cart_amount}")
        # 显示换购banner
        shop_more_bar = cart_pantry.get_by_test_id("wid-cart-selection-detail-shop-more-bar")
        shop_more_bar_tag = cart_pantry.get_by_test_id("wid-cart-selection-detail-shop-more-bar-tag")
        shop_more_bar_text = cart_pantry.get_by_test_id("wid-cart-selection-detail-shop-more-bar-tag-text")
        shop_more_bar_content = cart_pantry.get_by_test_id("wid-cart-selection-detail-shop-more-bar-content")
        shop_more_bar_more = cart_pantry.get_by_test_id("wid-cart-selection-detail-shop-more-bar-btn")
        shop_more_bar_more_text = cart_pantry.get_by_test_id("wid-cart-selection-detail-shop-more-bar-btn-text")

        assert shop_more_bar.is_visible()
        assert shop_more_bar_tag.is_visible()
        assert shop_more_bar_text.is_visible()
        assert shop_more_bar_content.is_visible()
        assert shop_more_bar_more.is_visible()
        assert shop_more_bar_more_text.is_visible()
        # 8. 验证换购文案提示包含"to unlock extra deals!"
        trade_in_text = shop_more_bar_content.text_content()
        assert "You have selected 0/5 deals!" in trade_in_text, f"换购文案应包含'You have selected 0/5 deals!'，实际为'{trade_in_text}'"
        log.info("验证换购文案提示包含'You have selected 0/5 deals!'")

        # 12. 点击view 按钮，进入换购页面
        shop_more_bar_more.click()
        p.wait_for_timeout(3000)

        # 验证成功跳转到换购页面
        assert "/promotion/trade-in" in p.url, f"应跳转到换购页面，实际URL: {p.url}"
        log.info("验证成功跳转到换购页面/promotion/trade-in")

        # 选择换购商品，最多选择5件

        assert p.get_by_test_id(mweb_trade_in_ele.ele_go_to_cart).is_visible()

        trade_in_cards = p.get_by_test_id(mweb_trade_in_ele.ele_trade_in_card).all()
        # 换购卡片公共断言
        trade_in_page.trade_in_card_assert(trade_in_cards, cart_amount)

        # 获取换购商品的加购按钮
        add_buttons = p.get_by_test_id(mweb_trade_in_ele.ele_trade_in_select).all()
        assert len(add_buttons) > 0, "应该有可加购的换购商品"

        # 连续加购5件换购商品，验证状态变化
        selected_cards = []
        for i in range(5):
            if i < len(trade_in_cards):
                # 点击加购按钮
                add_buttons[i].click()
                p.wait_for_timeout(1000)

                # 验证该商品状态变为已选择
                btn_element = trade_in_cards[i].get_attribute("data-status")
                assert btn_element == "btn-trade-in-add-to-cart-selected", f"第{i + 1}件商品选择后应显示已选择状态"
                selected_cards.append(i)
                log.info(f"成功加购第{i + 1}件换购商品，状态已变为selected")
            else:
                # 如果商品不够，滚动查找更多商品
                p.mouse.wheel(0, 300)
                p.wait_for_timeout(1000)
                trade_in_cards = p.get_by_test_id(mweb_trade_in_ele.ele_trade_in_card).all()
                add_buttons = p.get_by_test_id(mweb_trade_in_ele.ele_trade_in_select).all()
                if i < len(add_buttons):
                    add_buttons[i].click()
                    p.wait_for_timeout(1000)
                    btn_element = trade_in_cards[i].get_attribute("data-status")
                    assert btn_element == "btn-trade-in-add-to-cart-selected", f"第{i + 1}件商品选择后应显示已选择状态"
                    selected_cards.append(i)
                    log.info(f"成功加购第{i + 1}件换购商品，状态已变为selected")

        # 验证已选择5件商品后，其他商品状态变为disabled
        all_cards = p.get_by_test_id(mweb_trade_in_ele.ele_trade_in_card).all()
        for i, card in enumerate(all_cards):
            if i not in selected_cards:
                btn_element = card.get_attribute("data-status")
                assert btn_element == "btn-trade-in-add-to-cart-disabled"
        # 点击go to cart
        p.get_by_test_id(mweb_trade_in_ele.ele_go_to_cart).click()
        p.wait_for_timeout(2000)
        # 断言回到购物车
        assert "/cart" in p.url
        # 验证已加购5件商品的提示文案 -- 待补充

        # page_text = p.text_content()
        # if "You have selected 5/5 deals!" in page_text:
        #     log.info("验证已加购5件换购商品的提示文案")
        # else:
        #     log.info("未找到5/5提示文案，可能显示在其他位置")

        log.info("【108272-3】 购物车-金额大于$68时换购模块验证完成，包含5件商品限制测试")


