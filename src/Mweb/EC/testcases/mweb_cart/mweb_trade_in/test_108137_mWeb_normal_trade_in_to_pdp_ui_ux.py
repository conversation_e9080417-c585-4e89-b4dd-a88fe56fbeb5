import allure
import pytest
from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_trade_in_ele
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_recommendations_ele
from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_trade_in_page import MWebTradeInPage
from src.Mweb.EC.mweb_pages.mweb_page_category.mweb_page_category import MWebCategorypage
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart, CommonCheck



@allure.story("【107320】 购物车-换购模块加购金额验证")
class TestMWebNormalCartTradeInPDP:
    def setup_cart_page(self, phone_page, h5_autotest_header):
        """
        初始化购物车页面
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 如果不是98011，切换回98011
        try:
            CommonCheck().set_porder(h5_autotest_header, 98011)
        except Exception as e:
            log.info("账号没有在98011下" + str(e))
        try:
            empty_cart(h5_autotest_header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        # 进入指定页面
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        # 滚动到指定位置-猜你喜欢
        scroll_one_page_until(p, mweb_cart_ele.ele_recommend_tab)

        return p, c, cart_page

    pytestmark = [pytest.mark.h5cart, pytest.mark.todo, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【107320-2】 换购页-金额大于$35小于$68时换购模块验证")
    def test_108137_mWeb_normal_trade_in_to_pdp_ui_ux(self, phone_page: dict, h5_autotest_header,
                                                             h5_open_and_close_trace):
        """
        【109167-2】 购物车-金额大于免运费金额小于$68时换购模块验证
        测试步骤：
        1. 清空购物车
        2. 调用分类加购商品的公共方法add_category_filter_product_to_cart 加购商品
        3. 判断生鲜购物车data-testid="wid-cart-normal" 的金额data-testid="wid-cart-total-price"
        4. 如果金额大于68，需要删除：data-testid="wid-cart-section-normal-goods-remove-btn"第一个商品，然后再判断金额，直到金额小于68
        5. 如果金额小于35，需要继续加购
        6. 当金额大于$35小于$68
        7. 此时显示购物车换购模块
        8. 验证换购文案提示包含"to unlock extra deals!"
        9. 验证换购卡片上不可加购 data-testid="wid-cart-activity-lock"
        10.验证换购模块下有view more 按钮data-testid="btn-cart-activity-bottom-view-more"
        11.滑动换购模块商品data-testid="wid-product-card-container"到后面，也有 view more按钮data-testid="wid-cart-activity-list-view-more"
        12. 点击view more按钮，进入换购页面/promotion/trade-in
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="")
        trade_in_page = MWebTradeInPage(p, h5_autotest_header, browser_context=c, page_url="")

        # 1. 清空购物车
        try:
            empty_cart(h5_autotest_header)
            log.info("成功清空购物车")
        except Exception as e:
            log.info(f"清空购物车发生异常: {str(e)}")

        # 2. 调用分类加购商品的公共方法加购商品
        added_count = category_page.add_category_filter_product_to_cart(
            "wid-nav-tab-deals",
            mweb_category_ele.ele_local_delivery,
            count=6
        )
        # assert added_count > 0, "加购商品失败"
        log.info(f"成功加购{added_count}个商品")

        # 点击购物车按钮进入购物车
        # p.goto("https://www.sayweee.com/en/cart")
        p.get_by_test_id("wid-cart").click()
        p.wait_for_timeout(2000)

        # 3. 判断生鲜购物车的金额
        cart_normal = p.get_by_test_id(mweb_cart_ele.ele_cart_normal)
        assert cart_normal.is_visible(), "生鲜购物车不可见"

        # 获取购物车金额
        cart_total_price = p.get_by_test_id("wid-cart-total-price")
        total_text = cart_total_price.text_content()
        cart_amount = float(total_text.replace('$', '').replace(',', ''))
        log.info(f"当前购物车金额: ${cart_amount}")
        # shipping_fee = cart_normal.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_price)
        # shipping_free = cart_normal.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_free_price)

        # 4. 如果已经满足免运费门槛（shipping_free存在）但是超过了$68，需要删除商品直到不满足免运费
        while True:
            try:
                shipping_free = cart_normal.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_free_price)
                if shipping_free.is_visible() and cart_amount >= 68:
                    log.info("购物车已满足免运费门槛，删除第一个商品")
                    remove_btn = p.get_by_test_id(mweb_cart_ele.ele_cart_normal_remove).first
                    remove_btn.click()
                    p.wait_for_timeout(2000)
                    # 重新获取金额
                    try:
                        total_text = p.get_by_test_id("wid-cart-total-price").text_content()
                        cart_amount = float(total_text.replace('$', '').replace(',', ''))
                        log.info(f"删除商品后，当前购物车金额: ${cart_amount}")
                    except:
                        cart_amount = 0
                        log.info("购物车已空，金额为$0")
                        break
                else:
                    break
            except:
                break

        # 5. 如果还在收运费但金额过低（shipping_fee存在但需要加购到换购门槛），需要继续加购
        while True:
            try:
                shipping_fee = cart_normal.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_price)
                if shipping_fee.is_visible():  # 假设35是换购门槛
                    log.info("购物车还在收运费且金额过低，需要继续加购商品")
                    # 返回分类页面继续加购
                    p.go_back()
                    p.wait_for_timeout(2000)

                    # 继续加购商品
                    additional_count = category_page.add_category_filter_product_to_cart(
                        "wid-nav-tab-deals",
                        mweb_category_ele.ele_local_delivery,
                        count=3
                    )
                    log.info(f"继续加购{additional_count}个商品")

                    # 重新进入购物车
                    p.get_by_test_id("wid-cart").click()
                    p.wait_for_timeout(2000)

                    # 重新获取金额
                    total_text = p.get_by_test_id("wid-cart-total-price").text_content()
                    cart_amount = float(total_text.replace('$', '').replace(',', ''))
                    log.info(f"加购后，当前购物车金额: ${cart_amount}")
                else:
                    break
            except:
                break

        # 6. 确认金额在$35-$68之间
        shipping_free = cart_normal.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_free_price)
        assert shipping_free.is_visible() and cart_amount < 68, f"购物车金额应在$35-$68之间，实际为${cart_amount}"
        log.info(f"确认购物车金额在$35-$68之间: ${cart_amount}")

        # 7. 此时显示购物车换购模块
        trade_in_module = p.get_by_test_id("mod-cart-activity-cart_deal")
        assert trade_in_module.is_visible(), "金额在$35-$68之间时应显示换购模块"
        log.info("验证显示购物车换购模块")

        # 10. 验证换购模块下有view more按钮
        view_more_bottom = p.get_by_test_id("btn-cart-activity-bottom-view-more")
        assert view_more_bottom.is_visible(), "换购模块下应有view more按钮"
        log.info("验证换购模块下有view more按钮")

        # 12. 点击view more按钮，进入换购页面
        view_more_bottom.click()
        p.wait_for_timeout(3000)

        # 验证成功跳转到换购页面
        assert "/promotion/trade-in" in p.url, f"应跳转到换购页面，实际URL: {p.url}"
        log.info("验证成功跳转到换购页面/promotion/trade-in")
        # 换购页面 断言
        assert p.get_by_test_id(mweb_trade_in_ele.ele_trade_in_tip).is_visible()
        assert p.get_by_test_id(mweb_trade_in_ele.ele_trade_in_txt).is_visible()
        assert p.get_by_test_id(mweb_trade_in_ele.ele_trade_in_card).is_visible()
        trade_in_cards = p.get_by_test_id(mweb_trade_in_ele.ele_trade_in_card).all()
        # 换购卡片公共断言
        trade_in_page.trade_in_card_assert(trade_in_cards,cart_amount)
        # 点击换购进入pdp
        trade_in_price = trade_in_cards[0].get_by_test_id("wid-product-card-price-value")
        trade_in_cards[0].get_by_test_id("wid-product-card-container").click()
        p.wait_for_timeout(2000)
        assert p.get_by_test_id("mod-pdp-main-banner-0").is_visible()
        # 断言pdp toast -- 待补充
        assert p.get_by_test_id("").is_visible()
        # 加购商品
        p.get_by_test_id("btn-add-cart").click()
        p.wait_for_timeout(2000)
        # 回到购物车
        p.get_by_test_id("wid-cart").click()
        p.wait_for_timeout(2000)
        # 断言加购的金额 -- 待补充

        cart_product = cart_normal.get_by_test_id("wid-product-card-container").all()
        cart_price = cart_product[0].get_by_test_id("wid-cart-section-normal-goods-price")

        assert cart_price > trade_in_price
        log.info("【108137-2】 PDP-金额小于$68时换购模块验证完成")

    @allure.title("【108137-3】 换购页-金额大于$68时换购模块验证")
    def test_108137_mWeb_normal_trade_in_to_pdp_ui_ux(self, phone_page: dict, h5_autotest_header,
                                                            h5_open_and_close_trace):
        """
        【109167-3】 购物车-金额大于$68时换购模块验证
        测试步骤：
        1. 清空购物车
        2. 从分类加购商品，确保金额大于$68
        3. 验证显示购物车换购模块
        4. 验证换购卡片上可加购
        5. 点击加购换购商品
        6. 验证换购商品加入购物车成功
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="")
        trade_in_page = MWebTradeInPage(p, h5_autotest_header, browser_context=c, page_url="")

        # 1. 清空购物车
        try:
            empty_cart(h5_autotest_header)
            log.info("成功清空购物车")
        except Exception as e:
            log.info(f"清空购物车发生异常: {str(e)}")

        # 2. 调用分类加购商品的公共方法加购商品
        added_count = category_page.add_category_filter_product_to_cart(
            "wid-nav-tab-deals",
            mweb_category_ele.ele_local_delivery,
            count=6
        )
        assert added_count > 0, "加购商品失败"
        log.info(f"成功加购{added_count}个商品")

        # 点击购物车按钮进入购物车
        p.get_by_test_id("wid-cart").click()
        p.wait_for_timeout(2000)

        # 3. 判断生鲜购物车的金额
        cart_normal = p.get_by_test_id("wid-cart-normal")
        assert cart_normal.is_visible(), "生鲜购物车不可见"

        # 获取购物车金额
        cart_total_price = p.get_by_test_id("wid-cart-total-price")
        total_text = cart_total_price.text_content()
        cart_amount = float(total_text.replace('$', '').replace(',', ''))
        log.info(f"当前购物车金额: ${cart_amount}")
        # shipping_fee = cart_normal.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_price)
        # shipping_free = cart_normal.get_by_test_id(mweb_cart_ele.ele_cart_shipping_fee_free_price)

        while cart_amount < 68:
            # 继续加购商品
            additional_count = category_page.add_category_filter_product_to_cart(
                "wid-nav-tab-deals",
                mweb_category_ele.ele_local_delivery,
                count=3
            )
            log.info(f"继续加购{additional_count}个商品")

            # 重新进入购物车
            p.get_by_test_id("wid-cart").click()
            p.wait_for_timeout(2000)

            # 重新获取金额
            total_text = p.get_by_test_id("wid-cart-total-price").text_content()
            cart_amount = float(total_text.replace('$', '').replace(',', ''))
            log.info(f"加购后，当前购物车金额: ${cart_amount}")
        # 6. 确认金额>$68
        assert cart_amount >= 68, f"购物车金额应在$35-$68之间，实际为${cart_amount}"
        log.info(f"确认购物车金额在$35-$68之间: ${cart_amount}")
        # 7. 此时显示购物车换购模块
        trade_in_module = p.get_by_test_id("mod-cart-activity-cart_deal")
        assert trade_in_module.is_visible(), "金额在>$68应显示换购模块"
        log.info("验证显示购物车换购模块")

        # 8. 验证换购文案提示包含"to unlock extra deals!"
        trade_in_text = trade_in_module.text_content()
        assert "You have selected 0/5 deals!" in trade_in_text, f"换购文案应包含'You have selected 0/5 deals!'，实际为'{trade_in_text}'"
        log.info("验证换购文案提示包含'You have selected 0/5 deals!'")

        # 9. 验证换购卡片上可加购
        add_icon = trade_in_module.get_by_test_id("btn-atc-plus")
        assert add_icon.is_visible(), "金额在$35-$68之间时换购卡片应显示加购按钮"
        log.info("验证换购卡片上不可加购")

        # 10. 验证换购模块下有view more按钮
        view_more_bottom = p.get_by_test_id("btn-cart-activity-bottom-view-more")
        assert view_more_bottom.is_visible(), "换购模块下应有view more按钮"
        log.info("验证换购模块下有view more按钮")

        # 12. 点击view more按钮，进入换购页面
        view_more_bottom.click()
        p.wait_for_timeout(3000)

        # 验证成功跳转到换购页面
        assert "/promotion/trade-in" in p.url, f"应跳转到换购页面，实际URL: {p.url}"
        log.info("验证成功跳转到换购页面/promotion/trade-in")
        # 选择换购商品，最多选择5件

        assert p.get_by_test_id(mweb_trade_in_ele.ele_go_to_cart).is_visible()

        trade_in_cards = p.get_by_test_id(mweb_trade_in_ele.ele_trade_in_card).all()
        # 换购卡片公共断言
        trade_in_page.trade_in_card_assert(trade_in_cards,cart_amount)

        # 点击换购进入pdp
        trade_in_price = trade_in_cards[0].get_by_test_id("wid-product-card-price-value")
        trade_in_cards[0].get_by_test_id("wid-product-card-container").click()
        p.wait_for_timeout(2000)
        assert p.get_by_test_id("mod-pdp-main-banner-0").is_visible()
        # 断言pdp toast -- 待补充
        assert p.get_by_test_id("").is_visible()
        # 加购商品
        p.get_by_test_id("btn-add-cart").click()
        p.wait_for_timeout(2000)
        # 回到购物车
        p.get_by_test_id("wid-cart").click()
        p.wait_for_timeout(2000)
        # 断言加购的金额 -- 待补充

        cart_product = cart_normal.get_by_test_id("wid-product-card-container").all()
        cart_price = cart_product[0].get_by_test_id("wid-cart-section-normal-goods-price")

        assert cart_price > trade_in_price

        log.info("【109167-3】 PDP-金额大于$68时换购模块验证完成")


