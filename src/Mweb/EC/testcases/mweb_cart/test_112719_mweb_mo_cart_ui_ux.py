"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_112719_mweb_mo_cart_ui_ux.py
@Description    :
@CreateTime     :  2025/4/10 14:33
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/4/10 14:33
"""

import re
import allure
import pytest
from playwright.async_api import Page
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_category.mweb_page_category import MWebCategorypage
from src.api.zipcode import switch_zipcode
from src.common.commfunc import empty_cart
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log


@allure.story("H5购物车-MO购物车UI/UX验证 -- zhuli")
class TestMwebMoCartUIUX:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.zhuli]
    @allure.title("H5购物车-MO购物车UI/UX验证--zhuli")
    @pytest.mark.present
    def test_112719_MWeb_mo_cart_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【112719】 H5购物车-MO购物车UI/UX验证
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        switch_zipcode(h5_autotest_header, '99348')
        p.wait_for_timeout(2000)
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c,
                                 page_url="/cart")

        # 2.清除购物车
        empty_cart(h5_autotest_header)

        # 2. 使用封装方法加购Local Delivery商品
        with allure.step("使用封装方法加购MO Local Delivery商品"):
            # 创建分类页面对象
            category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="/category/sale")
            p.wait_for_timeout(2000)

            # 检查筛选按钮是否存在
            filter_button = p.get_by_test_id(mweb_category_ele.ele_filter_button)
            log.info(f"筛选按钮是否可见: {filter_button.is_visible(timeout=1000)}")
            local_filter_id = mweb_category_ele.ele_mo_delivery

            # 调用封装方法加购Local Delivery商品
            added_count = category_page.add_products_from_home_by_filter(
                filter_name="Fulfilled by Weee",
                filter_id=local_filter_id,
                count=2,  # 加购2个商品
            )
            p.wait_for_timeout(5000)  # 增加等待时间
            log.info(f"成功加购{added_count}个商品")
            assert added_count > 0, "未能成功加购商品"
        # 滚动到指定位置-购物车顶部
        with allure.step("验证购物车UI"):
            # 创建购物车页面对象
            cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
            cart_page.page.wait_for_timeout(2000)
            # 关闭可能出现的广告弹窗
            if cart_page.page.locator("//img[contains(@aria-label, 'close button')]").all():
                cart_page.page.locator("//img[contains(@aria-label, 'close button')]").click()
                log.info("关闭广告弹窗")
            # scroll_one_page_until(p, mweb_cart_ele.ele_cart_hearder_title)
            assert p.get_by_test_id(mweb_cart_ele.ele_cart_normal).is_visible(), "直邮购物车不存在"

            # 判断直邮购物车的标题=Direct mail
            assert "Direct mail" == cart_page.FE.ele(mweb_cart_ele.ele_cart_direct_mail_top_title).text_content()
            # 判断只有购物车标题下面的文案显示正确
            assert "Shipping via FedEx, UPS, etc." == cart_page.FE.ele(mweb_cart_ele.ele_mo_cart_text).text_content()

            # 判断shipping_fee中有美元符号存在或为free
            shipping_fee = cart_page.FE.eles(mweb_cart_ele.ele_shipping_fee)
            for sf in shipping_fee:
                log.info("delivery_fee的content===>" + sf.text_content())
                assert "$" in sf.text_content() or 'Free delivery' == sf.text_content()
            
            # 获取所有的items total
            items_total = cart_page.page.get_by_test_id(mweb_cart_ele.ele_cart_total_price)
            # assert items_total, f"items_total={items_total}"
            log.info("item.text_content===>" + items_total.text_content())
            assert "$" in items_total.text_content()
            p.wait_for_timeout(2000)
            # 判断items_total中有美元符号存在
            # for item in items_total:
            #     log.info("item.text_content===>" + item.text_content())
            #     assert "$" in item.text_content()
            #     p.wait_for_timeout(2000)
        
        # 验证购物车商品
        with allure.step("验证购物车商品"):
            # 使用合并后的方法验证购物车商品
            assert cart_page.verify_cart_items(cart_type="normal"), "MO购物车商品验证失败"
        
        # 执行稍后再买操作
        with allure.step("执行稍后再买操作"):
            assert cart_page.save_for_later_operations(cart_type="normal"), "MO稍后再买操作失败"
        p.wait_for_timeout(2000)

        # 执行购物车商品删除操作
        with allure.step("执行商品删除操作"):
            assert cart_page.remove_cart_item(cart_type="normal"), "MO购物车商品删除操作失败"
        log.info("H5购物车-MO购物车UI/UX----验证通过")

    # @allure.title("H5购物车-稍后再买功能验证")
    # def test_112719_MWeb_mo_cart_save_for_later(self, phone_page: dict, h5_autotest_header):
    #     """
    #     验证购物车稍后再买功能:
    #     1. 添加推荐商品到购物车
    #     2. 将商品移到稍后再买
    #     3. 验证稍后再买区域显示
    #     4. 将商品移回购物车
    #     5. 验证商品回到购物车
    #     """
    #     p: Page = phone_page.get("page")
    #     c = phone_page.get("context")
    #
    #     # 进入购物车页面
    #     cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
    #     p.wait_for_timeout(2000)
    #
    #     # 滚动到推荐商品区域并添加商品
    #     recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)
    #     for index1, item1 in enumerate(recommend_card):
    #         # 加购推荐商品
    #         item1.query_selector(mweb_cart_ele.ele_cart_atc_normal_plus).click()
    #         p.wait_for_timeout(1000)
    #         if index1 == 2:
    #             break
    #     # 滚动回购物车顶部
    #     scroll_one_page_until(p, mweb_cart_ele.ele_cart_normal_card)
    #
    #     # 执行稍后再买操作
    #     assert cart_page.save_for_later_operations(), "稍后再买操作失败"

        # 验证稍后再买区域
        # save_later_section = cart_page.FE.ele("[data-testid='save-for-later-section']")
        # assert save_later_section.is_visible(), "稍后再买区域未显示"

        # 将商品移回购物车
        # assert cart_page.move_to_cart_from_save_later(), "移回购物车操作失败"
        #
        # # 验证商品已回到购物车
        # normal_cards = cart_page.FE.eles("[data-testid='cart-normal-card']")
        # assert len(normal_cards) > 0, "商品未成功移回购物车"

