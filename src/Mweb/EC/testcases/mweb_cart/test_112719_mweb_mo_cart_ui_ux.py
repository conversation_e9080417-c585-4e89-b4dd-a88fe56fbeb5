"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_112719_mweb_mo_cart_ui_ux.py
@Description    :
@CreateTime     :  2025/4/10 14:33
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/4/10 14:33
"""

import re
import allure
import pytest
from playwright.async_api import Page
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.api.zipcode import switch_zipcode
from src.common.commfunc import empty_cart
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log


@allure.story("H5购物车-MO购物车UI/UX验证")
class TestMwebMoCartUIUX:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.zhuli]

    @allure.title("H5购物车-MO购物车UI/UX验证")
    @pytest.mark.present
    def test_112719_MWeb_mo_cart_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【112719】 H5购物车-MO购物车UI/UX验证
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        switch_zipcode(h5_autotest_header, 99348)
        p.wait_for_timeout(2000)
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c,
                                 page_url="/cart")

        try:
            empty_cart(h5_autotest_header)
            log.info("清空购物车成功")
            p.wait_for_timeout(2000)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
            p.wait_for_timeout(3000)
        # 滚动到指定位置-猜你喜欢
        scroll_one_page_until(p, mweb_cart_ele.ele_recommend_tab)
        p.wait_for_timeout(2000)
        # 获取猜你喜欢商品
        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)
        for index1, item1 in enumerate(recommend_card):
            # 加购推荐商品
            item1.query_selector("//div[@data-testid='btn-atc-plus']").click()
            # item1.query_selector(mweb_cart_ele.ele_cart_atc_normal_plus).click()
            p.wait_for_timeout(1000)
            if index1 == 2:
                break
        # 滚动到指定位置-购物车顶部
        scroll_one_page_until(p, mweb_cart_ele.ele_cart_normal_card)
        assert cart_page.FE.ele(mweb_cart_ele.ele_cart_normal).is_visible(), "直邮购物车不存在"

        # 判断直邮购物车的标题=Direct mail
        assert "Direct mail" == cart_page.FE.ele(mweb_cart_ele.ele_cart_pantry_top_title).text_content()
        # 判断只有购物车标题下面的文案显示正确
        assert "Shipping via FedEx, UPS, etc." == cart_page.FE.ele(mweb_cart_ele.ele_mo_cart_text).text_content()

        # 判断shipping_fee中有美元符号存在或为free
        shipping_fee = cart_page.FE.eles(mweb_cart_ele.ele_shipping_fee)
        for sf in shipping_fee:
            log.debug("delivery_fee的content===>" + sf.text_content())
            assert "$" in sf.text_content() or 'Free delivery' == sf.text_content()
        # 获取所有的items total
        items_total = cart_page.FE.eles(mweb_cart_ele.ele_items_total)
        assert items_total, f"items_total={items_total}"
        # 3. 判断items_total中有美元符号存在
        for item in items_total:
            log.debug("item.text_content===>" + item.text_content())
            assert "$" in item.text_content()
            p.wait_for_timeout(2000)
        cart_page.FE.ele(mweb_cart_ele.ele_cart_quality_click).click()
        p.wait_for_timeout(2000)
        # 查到购物车商品卡片上加/减元素部分
        # assert cart_page.FE.ele(mweb_cart_ele.ele_cart_atc_normal_minus).is_visible(), "删减商品按钮不存在"
        # assert cart_page.FE.ele(mweb_cart_ele.ele_cart_atc_normal_plus).is_visible(), "增加商品按钮不存在"

        # 方式2：先获取购物车区域，再获取其中的商品
        cart_div = cart_page.FE.ele(mweb_cart_ele.ele_cart_normal)
        if cart_div:
            # 使用商品卡片的选择器，这个选择器在mweb_cart_ele.py中定义
            all_goods = cart_page.FE.eles(mweb_cart_ele.ele_cart_normal_card)
            goods_count = len(all_goods) if all_goods else 0
            log.info(f"购物车商品总数: {goods_count}")
            if goods_count == 0:
                # 添加调试信息
                log.info("未找到商品，检查页面状态...")
                log.info(f"购物车区域是否存在: {cart_div is not None}")
                log.info(f"使用的选择器: {mweb_cart_ele.ele_cart_normal_card}")
                # 尝试等待商品加载
                p.wait_for_timeout(2000)
                # 重试一次
                all_goods = cart_div.query_selector_all(mweb_cart_ele.ele_cart_normal_card)
                goods_count = len(all_goods) if all_goods else 0
                log.info(f"重试后购物车商品总数: {goods_count}")
            assert goods_count > 0, "购物车中没有商品"
            
            # 记录找到的非free商品数量
            normal_goods_count = 0
            
            # 如果需要遍历每个商品
            for index, good in enumerate(all_goods):
                log.info(f"检查第 {index+1} 个商品")
                
                # 检查当前商品是否有free标签
                # 尝试多种可能的free标签选择器
                free_label = None
                free_selectors = [
                    "//span[contains(text(), 'Free')]",
                    "//div[contains(text(), 'Free')]",
                    "//div[@data-testid='gift-label']",
                    "//div[contains(@class, 'gift-label')]"
                ]
                
                for selector in free_selectors:
                    try:
                        element = good.query_selector(selector)
                        if element and element.is_visible():
                            free_label = element
                            log.info(f"找到free标签: {element.text_content()}")
                            break
                    except Exception as e:
                        log.debug(f"查找free标签时出错: {str(e)}")
                
                # 获取价格元素
                product_prices = good.query_selector("//div[@data-testid='wid-product-card-price']")
                if not product_prices:
                    log.warning(f"商品 {index+1} 未找到价格元素")
                    continue
                
                price_text = product_prices.text_content()
                log.info(f"商品 {index+1} 价格: {price_text}")
                
                # 如果是免费/赠品商品，跳过按钮检查
                if free_label or "Free" in price_text:
                    log.info(f"商品 {index+1} 是免费/赠品商品，跳过按钮检查")
                    # 对于免费/赠品商品，只检查价格显示正确
                    assert "Free" in price_text or "$" in price_text, "免费/赠品商品价格显示不正确"
                    continue  # 跳过此商品的其他检查
                
                # 计数非free商品
                normal_goods_count += 1
                
                # 以下是对非免费/赠品商品的检查
                price_pattern = r'^\$\d+\.\d{2}$'
                
                # 查到购物车商品卡片上加/减元素部分
                quality_click_element = good.query_selector("//div[@data-testid='btn-quantity-click']")
                if not quality_click_element:
                    # 尝试备用选择器
                    quality_click_element = good.query_selector("//div[@class='absolute bottom-0 right-0']")
                
                if quality_click_element:
                    log.info(f"点击商品 {index+1} 的数量区域")
                    quality_click_element.click()
                    p.wait_for_timeout(1000)
                else:
                    log.warning(f"商品 {index+1} 未找到数量点击元素，跳过加减按钮检查")
                    continue
                
                # 检查加减按钮 - 使用相对于当前商品的选择器
                minus_btn = good.query_selector("//div[@data-testid='btn-atc-minus']")
                plus_btn = good.query_selector("//div[@data-testid='btn-atc-plus']")
                
                if minus_btn and plus_btn:
                    assert minus_btn.is_visible(), "删减商品按钮不存在"
                    assert plus_btn.is_visible(), "增加商品按钮不存在"
                    log.info(f"商品 {index+1} 的加减按钮检查通过")
                else:
                    log.warning(f"商品 {index+1} 未找到加减按钮，可能是特殊商品")
                    # 尝试再次点击数量区域
                    if quality_click_element:
                        log.info(f"再次点击商品 {index+1} 的数量区域")
                        quality_click_element.click()
                        p.wait_for_timeout(1000)
                        
                        # 重新检查加减按钮
                        minus_btn = good.query_selector("//div[@data-testid='btn-atc-minus']")
                        plus_btn = good.query_selector("//div[@data-testid='btn-atc-plus']")
                        
                        if minus_btn and plus_btn:
                            assert minus_btn.is_visible(), "删减商品按钮不存在"
                            assert plus_btn.is_visible(), "增加商品按钮不存在"
                            log.info(f"商品 {index+1} 的加减按钮检查通过")
                        else:
                            log.warning(f"再次尝试后仍未找到商品 {index+1} 的加减按钮，跳过此商品")
                            continue
                    else:
                        continue
                
                # 校验商品的价格以$开头
                assert "$" in price_text, "商品价格不包含$符号"
                # 校验价格是两位小数
                assert re.match(price_pattern, price_text), f"Price 价格不是两位小数"
                
                # 校验remove按钮
                remove = good.query_selector("//div[@data-testid='btn-remove-product']")
                if remove:
                    assert remove.is_visible(), "删除按钮不存在"
                else:
                    log.warning(f"商品 {index+1} 未找到删除按钮")
                
                # 校验save_for_later
                save_for_later = good.query_selector("//div[@data-testid='btn-save-for-later']")
                if save_for_later:
                    assert save_for_later.is_visible(), "稍后再买按钮不存在"
                else:
                    log.warning(f"商品 {index+1} 未找到稍后再买按钮")
                
                # 检查划线价格
                try:
                    good_in_cart_base_price = good.query_selector("//div[contains(@class,'line-through')]")
                    if good_in_cart_base_price:
                        log.info("each product single price in cart===>" + good_in_cart_base_price.text_content())
                        assert "$" in good_in_cart_base_price.text_content()
                        assert re.match(price_pattern, good_in_cart_base_price.text_content()), f"Price 价格不是两位小数"
                except Exception as e:
                    log.warning(f"检查划线价格时出错: {str(e)}")
            
            # 确保至少有一个正常商品被测试
            log.info(f"购物车中找到 {normal_goods_count} 个非免费商品")
            if normal_goods_count == 0:
                log.warning("购物车中所有商品都是免费/赠品商品，跳过按钮检查")

        # 执行稍后再买操作
        assert cart_page.save_for_later_operations(), "稍后再买操作失败"
        log.info("H5购物车-MO购物车UI/UX----验证通过")

    @allure.title("H5购物车-稍后再买功能验证")
    def test_112719_MWeb_mo_cart_save_for_later(self, phone_page: dict, h5_autotest_header):
        """
        验证购物车稍后再买功能:
        1. 添加推荐商品到购物车
        2. 将商品移到稍后再买
        3. 验证稍后再买区域显示
        4. 将商品移回购物车
        5. 验证商品回到购物车
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 进入购物车页面
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        p.wait_for_timeout(2000)

        # 滚动到推荐商品区域并添加商品
        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)
        for index1, item1 in enumerate(recommend_card):
            # 加购推荐商品
            item1.query_selector(mweb_cart_ele.ele_cart_atc_normal_plus).click()
            p.wait_for_timeout(1000)
            if index1 == 2:
                break
        # 滚动回购物车顶部
        scroll_one_page_until(p, mweb_cart_ele.ele_cart_normal_card)

        # 执行稍后再买操作
        assert cart_page.save_for_later_operations(), "稍后再买操作失败"

        # 验证稍后再买区域
        # save_later_section = cart_page.FE.ele("[data-testid='save-for-later-section']")
        # assert save_later_section.is_visible(), "稍后再买区域未显示"

        # 将商品移回购物车
        # assert cart_page.move_to_cart_from_save_later(), "移回购物车操作失败"
        #
        # # 验证商品已回到购物车
        # normal_cards = cart_page.FE.eles("[data-testid='cart-normal-card']")
        # assert len(normal_cards) > 0, "商品未成功移回购物车"

