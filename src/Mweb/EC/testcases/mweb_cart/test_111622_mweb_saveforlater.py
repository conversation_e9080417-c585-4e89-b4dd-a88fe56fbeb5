"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_111622_mweb_saveforlater.py
@Description    :  
@CreateTime     :  2025/8/15 17:37
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/8/15 17:37
"""
import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage


@allure.story("【111622】购物车-save for later交互")
class TestMWebSaveForLaterUIUX:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.zhuli]
    @allure.title("【111622】购物车-save for later交互--zhuli")
    @pytest.mark.present
    def test_111622_mWeb_save_for_later_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        直接检查'稍后再买'模块的存在性和核心功能:
        1. 直接进入购物车页面。
        2. 检查'稍后再买'模块是否存在。
        3. 如果存在，则遍历所有商品，验证其按钮（删除、加回购物车、售罄）状态。
        4. 如果不存在，则跳过测试，因为这是一种有效状态。
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 初始化页面
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")

        # 2. 检查'稍后再买'模块是否存在
        # 使用更稳定的网络空闲等待
        cart_page.page.wait_for_timeout(2000)
        # 移动到稍后再买区域
        cart_page.FE.ele(u"//div[@data-testid='wid-cart-save-for-later']").scroll_into_view_if_needed()
        save_later_section = cart_page.page.get_by_test_id("wid-cart-save-for-later").first
        if not save_later_section.is_visible(timeout=5000):
            pytest.skip("'稍后再买'模块不存在或不可见，跳过后续功能验证。")

        # 如果模块存在，则执行后续验证
        allure.step("'稍后再买'模块可见，开始验证其内部功能")

        # 3. 遍历并校验'稍后再买'区域所有商品的元素
        if cart_page.get_save_for_later_count() > 0:
            assert cart_page.verify_save_for_later_items_elements(), "'稍后再买'商品元素综合验证失败"
        else:
            pytest.skip("'稍后再买'模块中没有商品，跳过具体功能验证。")

        # 4. 测试删除功能
        initial_count = cart_page.get_save_for_later_count()
        assert cart_page.delete_from_save_later(item_index=0), "删除'稍后再买'商品失败"
        current_count = cart_page.get_save_for_later_count()
        assert current_count == initial_count - 1, f"删除商品后，数量验证失败。期望: {initial_count - 1}, 实际: {current_count}"

        # 5. 测试加回购物车功能 (前提是删除后还有商品)
        if cart_page.get_save_for_later_count() > 0:
            assert cart_page.move_to_cart_from_save_later(), "将商品移回购物车失败"
