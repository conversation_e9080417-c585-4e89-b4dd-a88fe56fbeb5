"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_112719_mweb_cart_global_checkout.py
@Description    :  购物车-验证从首页进入Deals筛选Global+加购商品后点击结算
@CreateTime     :  2025/4/25 10:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/4/25 10:30
"""
import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_ele.mweb_checkout import mweb_checkout_ele
from src.Mweb.EC.mweb_ele.mweb_home import mweb_home_ele
from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_category.mweb_page_category import MWebCategorypage
from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home import MWebPageHome
from src.common.commfunc import empty_cart
from src.config.weee.log_help import log
from src.api.zipcode import switch_zipcode
from src.config.base_config import TEST_URL


@allure.story("购物车-验证从首页进入Deals筛选Global+加购商品后点击结算")
class TestMwebCartGlobalCheckout:
    pytestmark = [pytest.mark.mweb_regression]

    @allure.title("购物车-验证从首页进入Deals筛选Global+加购商品后点击结算")
    @pytest.mark.present
    def test_mweb_cart_global_checkout(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        购物车-验证从首页进入Deals筛选Global+加购商品后点击结算
        测试步骤：
        1. 清空购物车
        2. 从首页进入Deals分类
        3. 点击分类的筛选条件选择Global+选项并应用
        4. 加购筛选Global+筛选条件后的商品
        5. 进入购物车页面，验证商品已添加成功
        6. 点击结算按钮，验证跳转到结算页面
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 0. 切换zipcode到98011
        with allure.step("切换zipcode到98011"):
            try:
                # 如果zipcode不是98011， 则切换到98011
                if not p.locator("//span[text()='98011']").all():
                    switch_zipcode(h5_autotest_header, "98011")
                    p.reload()
                    p.wait_for_load_state("networkidle", timeout=30000)
                    log.info("成功切换zipcode到98011")
            except Exception as e:
                log.warning(f"切换zipcode失败，继续测试: {str(e)}")

        # 1. 清空购物车
        with allure.step("清空购物车"):
            try:
                empty_cart(h5_autotest_header)
                log.info("购物车清空成功")
            except Exception as e:
                log.error(f"清空购物车失败: {str(e)}")
                # 继续测试，不中断

        # 2. 从首页进入Deals分类
        with allure.step("从首页进入Deals分类"):
            # 创建首页对象
            home_page = MWebPageHome(p, h5_autotest_header, bc=c)

            # 从首页进入Deals分类
            home_page.go_to_special_category_from_home(mweb_home_ele.ele_deals)
            p.wait_for_load_state("networkidle", timeout=30000)
            p.wait_for_timeout(2000)

            # 创建分类页面对象
            category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="/category/sale")

            log.info("成功从首页进入Deals分类")

        # 3. 点击分类的筛选条件选择Global+选项并应用，加购商品
        with allure.step("点击分类的筛选条件选择Global+选项并应用，加购商品"):
            # 使用优化后的add_products_by_category_filter方法添加Global+商品
            added_count = MWebCategorypage.add_products_by_category_filter(
                p, category_page, mweb_category_ele.ele_global_delivery_xpath, 1
            )

            assert added_count > 0, "未能成功加购Global+商品"
            log.info(f"成功加购{added_count}个Global+商品")

        # 4. 进入购物车页面，验证商品已添加成功
        with allure.step("进入购物车页面，验证商品已添加成功"):
            # 进入购物车页面
            p.goto(f"{TEST_URL}/cart")
            p.wait_for_load_state("networkidle", timeout=30000)
            p.wait_for_timeout(3000)

            # 验证购物车中的商品
            cart_items = p.query_selector_all(mweb_cart_ele.ele_cart_seller_card)
            assert len(cart_items) > 0, "购物车中没有找到商品，加购可能失败"
            log.info(f"购物车中有{len(cart_items)}个商品")

            # 验证购物车中商品数量与加购数量一致
            assert len(cart_items) >= added_count, f"购物车中商品数量({len(cart_items)})少于加购数量({added_count})"

            log.info("成功验证购物车中的商品")

        # 5. 点击结算按钮，验证跳转到结算页面
        with (allure.step("点击结算按钮，验证跳转到结算页面")):
            # 滚动到页面底部找到结算按钮
            p.evaluate('window.scrollTo(0, document.body.scrollHeight)')
            p.wait_for_timeout(1000)
            # 点击结算按钮 - 判断购物车类型
            # 检查是否有多个购物车
            cart_types = []

            # 使用count()方法检查元素数量，处理多个相同test_id的情况
            normal_count = p.locator(mweb_cart_ele.ele_cart_normal).count()
            if normal_count > 0:
                cart_types.append(f"normal({normal_count})")

            pantry_count = p.locator(mweb_cart_ele.ele_cart_pantry).count()
            if pantry_count > 0:
                cart_types.append(f"pantry({pantry_count})")

            # 对于seller类型，使用更精确的选择器
            # 先检查基本选择器
            seller_count = p.locator(mweb_cart_ele.ele_cart_seller).count()

            # 如果有多个相同的test_id，尝试使用更精确的选择器
            if seller_count > 1:
                # 尝试使用属性组合来区分
                unique_sellers = p.locator(mweb_cart_ele.ele_cart_seller + "[data-unique]")
                if unique_sellers.count() > 0:
                    seller_count = unique_sellers.count()
                else:
                    # 如果没有唯一属性，尝试使用XPath来获取唯一元素
                    unique_xpath = "//div[starts-with(@id, 'cart-seller_') and not(preceding-sibling::div[@id=@id])]"
                    unique_sellers = p.locator(unique_xpath)
                    if unique_sellers.count() > 0:
                        seller_count = unique_sellers.count()

            if seller_count > 0:
                cart_types.append(f"seller({seller_count})")

            log.info(f"检测到购物车类型: {cart_types}")

            # 检查是否有多个购物车类型或者有多个相同类型的购物车
            has_multiple_carts = len(cart_types) > 1

            # 检查是否有多个相同test_id的购物车
            has_duplicate_ids = False
            for cart_type in cart_types:
                if "(" in cart_type and ")" in cart_type:
                    count_str = cart_type.split("(")[1].split(")")[0]
                    if count_str.isdigit() and int(count_str) > 1:
                        has_duplicate_ids = True
                        break

            # 根据购物车情况选择定位方式
            if has_multiple_carts or has_duplicate_ids:
                log.info("检测到多个购物车类型或相同类型的多个购物车，使用locator方式定位结算按钮")

                # 尝试使用更精确的选择器
                checkout_selectors = [
                    mweb_cart_ele.ele_cart_checkout_button,
                    "//button[@data-testid='btn-checkout' and contains(@class, 'primary')]",  # 使用类名进一步缩小范围
                    "//button[@data-testid='btn-checkout' and not(ancestor::div[contains(@style, 'display: none')])]",  # 排除隐藏元素
                    "//button[@data-testid='btn-checkout' and position()=last()]",  # 选择最后一个元素
                    "//div[contains(@class, 'cart-footer')]/button[@data-testid='btn-checkout']",  # 使用父元素缩小范围
                ]

                checkout_btn = None
                for selector in checkout_selectors:
                    btn = p.locator(selector)
                    if btn.count() > 0 and btn.first.is_visible(timeout=1000):
                        checkout_btn = btn.first
                        log.info(f"使用选择器 '{selector}' 成功找到结算按钮")
                        break

                # 如果仍然未找到，尝试使用最后一个可见的结算按钮
                if not checkout_btn:
                    all_checkout_btns = p.locator("//button[@data-testid='btn-checkout']").all()
                    for btn in reversed(all_checkout_btns):  # 从后向前遍历，因为最后一个通常是我们需要的
                        if btn.is_visible(timeout=1000):
                            checkout_btn = btn
                            log.info("使用最后一个可见的结算按钮")
                            break
            else:
                log.info("检测到单一购物车类型，使用test_id方式定位结算按钮")
                checkout_btn = p.get_by_test_id("btn-checkout")

            # 如果上述方法都失败，尝试其他方法
            if not checkout_btn or not checkout_btn.is_visible(timeout=2000):
                log.warning("使用首选方法未找到结算按钮，尝试备选方法")
                # 尝试其他定位方式
                checkout_selectors = [
                    "//button[contains(text(), 'Checkout')]",
                    "//button[contains(@class, 'checkout-button')]",
                    "//button[contains(@class, 'cart-checkout')]",
                    "//div[contains(@class, 'cart-footer')]//button",
                    "//div[contains(@class, 'sticky-bottom')]//button"
                ]

                for selector in checkout_selectors:
                    btn = p.locator(selector)
                    if btn.count() > 0 and btn.first.is_visible(timeout=1000):
                        checkout_btn = btn.first
                        log.info(f"使用选择器 '{selector}' 成功找到结算按钮")
                        break

            assert checkout_btn.is_visible(timeout=2000), "结算按钮不可见"

            log.info("找到结算按钮，准备点击")
            checkout_btn.click()
            p.wait_for_timeout(3000)
            p.wait_for_load_state("networkidle", timeout=30000)

            # 验证是否跳转到结算页面
            # 检查结算页面特有元素，如地址选择、支付方式等
            checkout_header = p.locator(mweb_checkout_ele.ele_checkout_title)
            assert checkout_header.is_visible(timeout=2000), "未跳转到结算页面"

            # 验证结算页面包含地址信息部分
            address_section = p.locator(mweb_checkout_ele.ele_delivery_info_section)
            assert address_section.is_visible(timeout=2000), "结算页面未显示地址信息部分"

            # 验证结算页面包含支付方式部分
            payment_section = p.locator(mweb_checkout_ele.ele_payment_method_section)
            assert payment_section.is_visible(timeout=2000), "结算页面未显示支付方式部分"

            log.info("成功验证跳转到结算页面")
