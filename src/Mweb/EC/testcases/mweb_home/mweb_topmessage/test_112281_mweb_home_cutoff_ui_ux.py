"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_112281_mweb_home_cutoff_ui_ux.py
@Description    :  
@CreateTime     :  2025/7/28 13:38
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/7/28 13:38
"""
import allure
import pytest
from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_home.mweb_topmessage import mweb_topmessage_ele
from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home import MWebPageHome
from src.api.zipcode import switch_zipcode
from src.common.commfunc import empty_cart
from src.config.weee.log_help import log


@allure.story("H5首页-首页截单提醒的UI/UX验证")
class TestMwebGroceryCartUIUX:
    pytestmark = [pytest.mark.mweb_todo, pytest.mark.zhuli]

    @allure.title("H5首页-首页截单提醒的UI/UX验证")
    @pytest.mark.fresh
    def test_112281_mweb_home_cutoff_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        test_id还未补充完整，后续优化
        首页截单提醒UI/UX验证:
        1. 调用切换zipcode接口切换到90001
        2.选择本周第一个配送日期触发截单提醒
        3. 验证top message元素存在
        4. 点击top message
        5. 验证跳转到指定分类页面
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 准备测试环境
        with allure.step("准备测试环境"):
            # 切换zipcode到90001
            log.info("切换zipcode到90001")
            res = switch_zipcode(h5_autotest_header, "90001")
            assert res.get('object') == 'Success', f"切换zipcode失败，res={res}"
            p.wait_for_timeout(2000)

        # 2. 验证top message点击跳转
        with allure.step("验证top message点击跳转到分类页面"):
            # 创建首页页面对象
            MWebPageHome(p, h5_autotest_header, browser_context=c)
            p.wait_for_timeout(3000)
            # 查找日期选择器
            date_selector = p.get_by_test_id("wid-modal-zip-code-and-eta")
            if date_selector.is_visible(timeout=3000):
                date_selector.click()
                p.wait_for_timeout(1000)

                # 只选择本周的第一个可用日期才能触发截单提醒
                this_week_dates = p.locator("[data-testid='wid-delivery-date-this-week-content'] [data-testid*='date']").all()
                if this_week_dates:
                    this_week_dates[0].click()
                    p.wait_for_timeout(1000)
                    log.info("成功选择本周第一个日期")
                else:
                    log.info("本周没有可用日期，无法触发截单提醒")
                # 关闭日期选择器
                close_button = p.get_by_test_id("btn-modal-close")
                if close_button.is_visible(timeout=1000):
                    close_button.click()
                    p.wait_for_timeout(1000)
            else:
                log.info("未找到日期选择器")
            
            p.wait_for_timeout(2000)
            # 验证top message元素存在
            top_message = p.get_by_test_id(mweb_topmessage_ele.ele_home_cutoff)
            assert top_message.is_visible(), "top message元素不可见"
            log.info("top message元素验证成功")
            
            # 点击top message
            top_message.click()
            p.wait_for_timeout(2000)
            
            # 验证跳转到指定的分类页面
            expected_url = "/category/sale?filter_sub_category=sale&sort=&filters=&out_filters="
            current_url = p.url
            
            assert expected_url in current_url, f"页面跳转失败，期望包含: {expected_url}, 实际URL: {current_url}"
            log.info(f"页面跳转验证成功，当前URL: {current_url}")



        log.info("首页截单提醒UI/UX验证完成")
