"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_112281_mweb_home_cutoff_ui_ux.py
@Description    :  
@CreateTime     :  2025/7/28 13:38
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/7/28 13:38
"""
import allure
import pytest
from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_home.mweb_topmessage import mweb_topmessage_ele
from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home import MWebPageHome
from src.api.zipcode import switch_zipcode
from src.common.commfunc import empty_cart
from src.config.weee.log_help import log
from src.config.base_config import TEST_URL


@allure.story("H5首页-首页合单提醒的UI/UX验证")
class TestMwebGroceryCartUIUX:
    pytestmark = [pytest.mark.mweb_todo, pytest.mark.zhuli]

    @allure.title("H5首页-首页合单提醒的UI/UX验证")
    @pytest.mark.fresh
    def test_112495_mweb_home_reminder_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        首页截单提醒UI/UX验证:
        1. 切换到首页检查是否存在截单提醒的banner
        2. 如果不存在调用通用的加购代码加购选择积分结算
        3. 验证首页有合单提醒
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 准备测试环境
        with allure.step("准备测试环境"):
            # 清空购物车
            empty_cart(h5_autotest_header)
            # 切换zipcode到90001
            switch_zipcode(h5_autotest_header, "90001")
            p.wait_for_timeout(2000)

        # 2. 执行合单提醒完整流程
        with allure.step("合单提醒流程验证"):
            home_page = MWebPageHome(p, h5_autotest_header, browser_context=c)
            success = home_page.handle_shipping_reminder_flow()
            assert success, "合单提醒流程验证失败"

        log.info("首页合单提醒UI/UX验证完成")
    
