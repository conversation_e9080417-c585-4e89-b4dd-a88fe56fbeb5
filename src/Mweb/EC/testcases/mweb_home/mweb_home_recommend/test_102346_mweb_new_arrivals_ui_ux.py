import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_ele.mweb_home.mweb_home_ele import mweb_see_all, ele_h5_home_new_arrivals,ele_h5_home_new_arrivals_prod,ele_h5_home_new_arrivals_add_to_cart
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.common.commonui import scroll_one_page_until
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
class TestMwebNewArrivalsUI:

    def test_102346_mweb_new_arrivals_ui_ux(self, phone_page: dict, h5_autotest_header):
        """
        1、首页组件new arrivals可以正常展示
        2、获取对应new arrivals商品为10个
        3、加购商品可以正常展示
        4、点击查看更多按钮可以正常跳转至分类页bestseller标签
        """
        p: Page = phone_page.get("page")
        # 1. 进入首页
        log.info("开始测试new arrivals 加购按钮UI")
        p.goto(TEST_URL)
        p.wait_for_timeout(5000)

        # 关闭对应弹窗：
        PageH5CommonOperations(p, h5_autotest_header).close_advertisement_in_homepage()

        # 2. 滚动到new arrivals区域
        scroll_one_page_until(p, ele_h5_home_new_arrivals)
        p.wait_for_timeout(1000)

        # 3. 验证new arrivals区域标题
        new_arrivals_title = p.locator(ele_h5_home_new_arrivals)
        assert new_arrivals_title.is_visible(), "new arrivals组件未找到"
        log.info("new arrivals标题展示正常")

        # 4. 获取所有产品卡片
        new_arrivals_product_cards = p.locator(ele_h5_home_new_arrivals_prod).all()
        product_count = len(new_arrivals_product_cards)

        # 5. 验证产品个数为3
        assert product_count == 3, f"new arrivals组件产品个数不对，实际{product_count}个"
        log.info(f"new arrivals产品个数验证通过，共{product_count}个产品")

        # 6.加购组件产品卡片
        bestseller_add_to_cart = p.locator(ele_h5_home_new_arrivals_add_to_cart)
        assert bestseller_add_to_cart,"产品加购成功"

        # 7.点击查看更多按钮跳转
        new_arrivals_see_all = ele_h5_home_new_arrivals + mweb_see_all
        p.click(new_arrivals_see_all)
