# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2025/8/4
@Software       :  PyCharm
------------------------------------
"""
import allure
import pytest

from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home_ftu import MWebTimeBannerPage
from playwright.sync_api import Page
from src.Dweb.EC.conftest import ReportPage
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
@allure.story("Mweb-注册-FTU 注册新用户流程验证")
class TestMwebFtuEmailSignup:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.mweb_wangyue]

    @allure.title("mweb-注册-FTU 注册新用户流程验证")
    @pytest.mark.time_banner
    def test_1105645_mweb_ftu_email_signup_verify(self, h5_autotest_header, not_login_phone_page):
        "DWEB-注册-FTU 注册新用户流程验证"
        p: Page = not_login_phone_page.get("page")
        c = not_login_phone_page.get("context")
        pc_home_page = MWebTimeBannerPage(p, h5_autotest_header, c)
        p.goto(TEST_URL)
        p.wait_for_timeout(10000)

        with allure.step("验证FTU Popup是否存在"):
            if pc_home_page.m_verify_ftu_visibility():
                log.info("FTU存在，继续执行注册流程")

                with allure.step("执行邮箱注册"):
                    pc_home_page.m_ftu_email_signup()
                    p.wait_for_timeout(10000)


                with allure.step("检查Store Popup是否存在"):
                    if pc_home_page.m_store_pop_visibility():
                        log.info("Store popup存在")

                        with allure.step("点击Store Popup"):
                            pc_home_page.m_store_pop_click()
                            p.wait_for_timeout(5000)

                        with allure.step("验证是否跳转到click.sayweee.com"):
                            current_url = p.url
                            if "click.sayweee.com" in current_url:
                                log.info(f"成功跳转到click.sayweee.com域名: {current_url}")
                                log.info("注册成功")
                            else:
                                log.error(f"未跳转到目标域名，当前URL: {current_url}")
                    else:
                        log.info("无法注册，需要手工执行")
            else:
                # FTU不存在
                log.info("FTU不存在，不需要执行此用例")