import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele import mweb_common_ele
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.api.porder import update_zipcode_v1
from src.common.commfunc import empty_cart
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log


@allure.story("【108558】 购物车页面-切换日期验证")
class TestMWebChangeDeliveryDateOnCartPageUIUXV2:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108558】 购物车页面-切换日期验证 V2")
    def test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux_v2(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【108558】 购物车页面-切换日期验证
        测试步骤：
        1、进入购物车页面
        2、检查并切换zipcode为98011
        3、如果购物车为空，从推荐模块加购商品
        4、点击生鲜购物车上的切换日期按钮
        5、弹出切换日期pop
        6、点击pop里的日期进行切换日期
        7、验证购物车日期切换成功
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        
        # 1. 直接进入指定页面页面
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        p.wait_for_timeout(3000)
        log.info("成功进入购物车页面")
        
        # 2. 检查并切换zipcode为98011
        update_zipcode_v1(h5_autotest_header, "98011")
        p.reload()
        p.wait_for_timeout(3000)

        # 3. 如果购物车为空，从推荐模块加购商品


        self.add_items_if_cart_empty(p,cart_page)
        p.reload()
        p.wait_for_timeout(3000)
        # 4. 点击生鲜购物车上的切换日期按钮
        # 使用data-testid定位切换日期按钮
        delivery_date_btn = p.get_by_test_id("btn-change-delivery-date")
        assert delivery_date_btn.is_visible(), "购物车页面切换日期按钮未显示"
        
        # 获取当前显示的配送日期文本，用于后续验证
        current_date_text = delivery_date_btn.text_content()
        log.info(f"当前配送日期: {current_date_text}")
        
        # 点击切换日期按钮
        delivery_date_btn.click()
        p.wait_for_timeout(1000)
        log.info("点击切换日期按钮")
        
        # 5. 断言进入切换日期pop页面
        # 断言进入切换日期pop页面
        assert cart_page.FE.ele(mweb_common_ele.ele_delivery_date_popup).is_visible(), "切换日期弹窗未显示"

        # date_popup = p.get_by_test_id("delivery-date-popup")
        # assert date_popup.is_visible(), "切换日期弹窗未显示"
        log.info("成功弹出切换日期弹窗")
        
        # 6. 点击pop里的日期进行切换
        # 获取所有可选日期
        date_options = p.get_by_test_id("wid-delivery-date-item").all()
        assert len(date_options) > 0, "没有可选的配送日期"
        
        # 选择第一个不同于当前日期的选项
        selected_new_date = False
        for date_option in date_options:
            date_text = date_option.text_content()
            if date_text != current_date_text:
                date_option.click()
                selected_new_date = True
                log.info(f"选择新的配送日期: {date_text}")
                break
        
        # 如果所有日期都与当前日期相同，则选择第一个日期
        if not selected_new_date and len(date_options) > 0:
            date_options[0].click()
            log.info(f"选择第一个配送日期: {date_options[0].text_content()}")
        
        p.wait_for_timeout(2000)
        
        # 7. 验证购物车日期切换成功
        # 验证弹窗已关闭
        # assert not date_popup.is_visible(), "切换日期后弹窗未关闭"
        
        # 验证日期已更新
        updated_date_btn = p.get_by_test_id("btn-change-delivery-date")
        assert updated_date_btn.is_visible(), "购物车页面切换日期按钮未显示"
        
        # 获取更新后的日期文本
        updated_date_text = updated_date_btn.text_content()
        log.info(f"更新后的配送日期: {updated_date_text}")
        
        # 如果选择了新日期，验证日期已更改
        if selected_new_date:
            assert updated_date_text != current_date_text, "配送日期未成功更新"
        
        log.info("购物车页面切换日期验证成功")

    def add_items_if_cart_empty(self, p: Page,cart_page):
        """
        如果购物车为空，从推荐模块加购商品
        """
        # 检查购物车是否为空
        empty_cart = cart_page.FE.ele(mweb_cart_ele.ele_empty_cart_start_shopping)
        # cart_items = p.get_by_test_id("cart-item").all()
        if empty_cart:
            log.info("购物车为空，开始从推荐模块加购商品")

            # 滚动到推荐模块
            recommend_tab = p.get_by_test_id("mod-cart-Recommendations")
            if not recommend_tab.is_visible():
                p.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                p.wait_for_timeout(2000)

            # 确保推荐模块可见
            assert recommend_tab.is_visible(), "未找到推荐模块"

            # 获取推荐商品列表
            recommend_items = p.get_by_test_id("mod-cart-Recommendations").get_by_test_id("wid-product-card-container").all()
            assert len(recommend_items) > 0, "推荐模块没有商品"

            # 加购2件商品
            added_count = 0
            for item in recommend_items:
                add_btn = item.get_by_test_id("btn-atc-plus")
                if add_btn.is_visible():
                    add_btn.click()
                    p.wait_for_timeout(1000)
                    added_count += 1
                    log.info(f"成功加购第{added_count}件推荐商品")

                    if added_count >= 2:
                        break

            # 滚动回购物车顶部
            p.evaluate("window.scrollTo(0, 0)")
            p.wait_for_timeout(2000)

            # 验证商品已加入购物车
            cart_items = p.get_by_test_id("wid-product-card-container").all()
            assert len(cart_items) > 0, "加购商品后购物车仍为空"
            log.info(f"购物车现有{len(cart_items)}件商品")
        else:
            log.info(f"购物车已有商品，无需加购")