import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele import mweb_common_ele
from src.Mweb.EC.mweb_ele.mweb_account.mweb_order import mweb_order_detail_ele
from src.Mweb.EC.mweb_pages.mweb_account_page.mweb_order_page.mweb_order_page import MWebOrderPage


@allure.story("【108560】 再来一单-切换日期验证")
class TestMWebChangeDeliveryDateOnBuyAgainPageUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108560】 再来一单-切换日期验证")
    def test_108560_mWeb_change_delivery_date_on_buy_again_page_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【108560】 再来一单-切换日期验证
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 直接进入指定页面
        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url="/order/detail/********")
        p.wait_for_timeout(3000)
        # 关闭Continue pop
        # p.locator(u"//button[contains(text(), 'Continue')]").click()
        # 滚动到指定位置-分享按钮
        # scroll_one_page_until(p, mweb_pdp_ele.ele_share)
        # 点击再来一单按钮
        order_page.FE.ele(mweb_order_detail_ele.ele_reorder).click()
        p.wait_for_timeout(3000)
        # 断言进入再来一单选择商品页面
        assert order_page.FE.ele(mweb_order_detail_ele.ele_reorder_page).is_visible()
        # 点击切换日期按钮
        order_page.FE.ele(mweb_order_detail_ele.ele_reorder_delivery_date).click()
        p.wait_for_timeout(3000)
        # 断言进入切换日期pop页面
        assert order_page.FE.ele(mweb_common_ele.ele_delivery_date_popup).is_visible()
        # 点击切换日期
        delivery_data = order_page.FE.eles(mweb_common_ele.ele_delivery_date)
        for index,item in enumerate(delivery_data) :
            item.click()
            if index==0:
                break

        p.wait_for_timeout(3000)
        # 断言回到再来一单选择商品页面
        assert order_page.FE.ele(mweb_order_detail_ele.ele_reorder_page).is_visible()

