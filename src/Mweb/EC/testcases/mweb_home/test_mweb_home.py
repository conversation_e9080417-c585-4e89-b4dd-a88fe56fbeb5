import json

import allure
import pytest
from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home import MWebPageHome as PH5


@allure.story("H5首页操作")
class TestH5HomePage:
    pytestmark = [pytest.mark.mweb_regression]
    # @pytest.mark.repeat(10)
    @allure.title("H5页面元素操作")
    @pytest.mark.h5home
    def test_mweb_home_page_search_operations(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        ph5 = PH5(phone_page["page"], h5_autotest_header, phone_page["context"])
        ph5.search_on_home_page_operations()

    def _test_mweb_home_page_add_address(self, phone_page: dict, h5_autotest_header):
        """可以正常进入pin页面操作保存地址"""
        #todo  enki样式下，首页zipcode无法定位，等开发加上test_id以后再写
        ph5 = PH5(phone_page["page"], h5_autotest_header, phone_page["context"])
        ph5.save_address_in_home_page()

    # @pytest.mark.h5home
    def deprecated_test_mweb_home_page_scroll(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        # 现在已改成waterfall的形式没有具体的合集了，逻辑完全改动，去掉h5hmoe标签
        ph5 = PH5(phone_page["page"], h5_autotest_header, phone_page["context"])
        ph5.home_page_scroll_and_add_to_cart()

    
    def deprecated_test_home_page_click_global_bar(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        从首页Global+ tab点击进入全球购waterfall页面
        """
        # 新的enki样式底部已无tab，此用例废弃
        ph5 = PH5(phone_page["page"], h5_autotest_header, phone_page["context"])
        ph5.home_page_click_global_bar()




