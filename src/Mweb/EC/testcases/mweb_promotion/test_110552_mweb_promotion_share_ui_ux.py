import allure
import pytest

from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_promotion.mweb_page_promotion import MWebPromotionPage
from src.api.zipcode import switch_zipcode


@allure.story("【110552】mweb-promotion page 分享功能流程验证")
class TestMWebPromotionPageShareUIUX:
    pytestmark = [pytest.mark.h5activity, pytest.mark.mweb_regression, pytest.mark.transaction]

    @allure.title("【110552】mweb-promotion page 分享功能流程验证")
    def test_110552_mweb_promotion_page_share_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【110552】mweb-promotion page 分享功能流程验证
        测试步骤：
        1. 访问活动页面
        2. 校验页面基本元素
        3. 校验返回按钮和分享按钮
        4. 测试分享按钮点击功能
        5. 校验分享弹窗内容
        6. 校验分享方式选项
        7. 测试复制链接功能
        8. 测试弹窗关闭功能
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 初始化页面对象
        promotion_page = MWebPromotionPage(p, h5_autotest_header, browser_context=c,
                                   page_url="/promotion/free-gift/landing?ps_id=10758")
        switch_zipcode(headers=h5_autotest_header, zipcode="99991")
        p.wait_for_timeout(1000)
        # 关闭可能的弹窗
        continue_buttons = p.get_by_text("Continue").all()
        if continue_buttons:
            continue_buttons[0].click()

        # 2. 校验页面基本元素
        # 校验返回按钮存在 - 使用get_by_test_id
        back_button = p.get_by_test_id("wid-page-nav-header-back-button")
        assert back_button.is_visible(), "活动页面返回按钮不可见"

        # 验证返回按钮文本和图标
        back_text = back_button.text_content()
        assert "Back" in back_text, f"返回按钮文本不正确: {back_text}"

        # 验证返回按钮包含SVG图标
        back_svg = back_button.locator("svg")
        assert back_svg.is_visible(), "返回按钮图标不可见"

        print(f"返回按钮验证完成 - 文本: {back_text}")

        # 3. 校验分享按钮存在 - 使用get_by_test_id
        share_button = p.get_by_test_id("btn-share")
        assert share_button.is_visible(), "活动页面分享按钮不可见"

        # 验证分享按钮的样式类
        share_classes = share_button.get_attribute("class")
        assert "icon iconfont icona-share-ios1" in share_classes, f"分享按钮样式类不正确: {share_classes}"

        # 验证分享按钮的alt属性
        share_alt = share_button.get_attribute("alt")
        assert share_alt == "share button image", f"分享按钮alt属性不正确: {share_alt}"

        print("分享按钮验证完成")

        # 4. 测试分享按钮点击功能
        share_button.click()
        p.wait_for_timeout(2000)

        # 5. 校验分享弹窗出现 - 使用get_by_test_id
        share_popup = p.get_by_test_id("mod-share-popup")
        assert share_popup.is_visible(), "分享弹窗未出现"

        # 验证分享弹窗的样式类
        popup_classes = share_popup.get_attribute("class")
        expected_popup_classes = ["share-popup-content", "enki-elevation-5", "bg-surface-100-bg"]
        for expected_class in expected_popup_classes:
            assert expected_class in popup_classes, f"分享弹窗缺少样式类'{expected_class}': {popup_classes}"

        print("分享弹窗显示验证完成")

        # 6. 校验分享弹窗头部 - 使用get_by_test_id
        popup_header = p.get_by_test_id("wid-popup-header")
        assert popup_header.is_visible(), "分享弹窗头部不可见"

        # 验证弹窗头部ID
        header_id = popup_header.get_attribute("id")
        assert header_id == "share-popup-header", f"分享弹窗头部ID不正确: {header_id}"

        # 7. 校验分享弹窗标题
        popup_title = popup_header.locator("h2")
        assert popup_title.is_visible(), "分享弹窗标题不可见"
        title_text = popup_title.text_content()
        assert title_text == "Share", f"分享弹窗标题不正确: {title_text}"

        print(f"分享弹窗标题验证完成 - 标题: {title_text}")

        # 8. 校验关闭按钮 - 使用get_by_test_id
        close_button = p.get_by_test_id("btn-modal-close")
        assert close_button.is_visible(), "分享弹窗关闭按钮不可见"

        # 验证关闭按钮包含图标
        close_icon = close_button.locator("i")
        assert close_icon.is_visible(), "关闭按钮图标不可见"
        close_icon_classes = close_icon.get_attribute("class")
        assert "iconclose" in close_icon_classes, f"关闭按钮图标样式不正确: {close_icon_classes}"

        # 9. 校验分享内容
        # 校验分享图片 - 使用get_by_test_id
        share_image = p.get_by_test_id("wid-share-image")
        assert share_image.is_visible(), "分享图片不可见"

        # 验证图片属性
        image_alt = share_image.get_attribute("alt")
        assert image_alt == "share popup share image", f"分享图片alt属性不正确: {image_alt}"

        image_src = share_image.get_attribute("src")
        assert "weeecdn" in image_src, f"分享图片src不正确: {image_src}"

        # 10. 校验分享标题 - 使用get_by_test_id
        share_title = p.get_by_test_id("wid-share-title")
        assert share_title.is_visible(), "分享标题不可见"
        share_title_text = share_title.text_content()
        assert share_title_text, "分享标题为空"

        # 验证分享标题样式
        title_classes = share_title.get_attribute("class")
        assert "enki-body-base-medium" in title_classes, f"分享标题样式不正确: {title_classes}"
        assert "line-clamp-2" in title_classes, f"分享标题行数限制样式不正确: {title_classes}"

        print(f"分享内容验证完成 - 标题: {share_title_text}")

        # 11. 校验分享描述 - 使用get_by_test_id
        share_desc = p.get_by_test_id("wid-share-desc")
        if share_desc.count() > 0 and share_desc.is_visible():
            # 验证分享描述样式
            desc_classes = share_desc.get_attribute("class")
            assert "enki-body-sm" in desc_classes, f"分享描述样式不正确: {desc_classes}"

            # 获取描述内容（允许为空）
            desc_text = share_desc.text_content()
            print(f"分享描述内容: {desc_text if desc_text.strip() else '描述为空'}")
        else:
            print("分享描述元素不存在或不可见，跳过描述验证")

        # 12. 校验分享方式选项
        # 校验复制链接选项 - 使用get_by_test_id
        copy_link_option = p.get_by_test_id("btn-share-method-copyLink")
        assert copy_link_option.is_visible(), "复制链接选项不可见"

        # 验证复制链接选项的data-method属性
        copy_method = copy_link_option.get_attribute("data-method")
        assert copy_method == "copyLink", f"复制链接方法属性不正确: {copy_method}"

        # 验证复制链接选项的文本
        copy_text = copy_link_option.text_content()
        assert "Copy link" in copy_text, f"复制链接文本不正确: {copy_text}"

        # 验证复制链接选项的样式
        copy_classes = copy_link_option.get_attribute("class")
        assert "flex-shrink-0" in copy_classes, f"复制链接选项样式不正确: {copy_classes}"

        print(f"复制链接选项验证完成 - 文本: {copy_text}")

        # 13. 测试复制链接功能
        copy_link_option.click()
        p.wait_for_timeout(1000)

        # 这里可以添加复制成功的验证逻辑
        print("复制链接功能测试完成")

        # 验证弹窗已关闭
        assert not share_popup.is_visible(), "分享弹窗未正确关闭"

        print("分享弹窗关闭功能验证完成")

        # 15. 测试返回按钮功能（可选）
        # back_button.click()
        # p.wait_for_timeout(2000)
        # 这里可以验证页面是否正确返回

        print("活动页面分享功能UI/UX验证全部完成")

        # 16. 输出验证结果摘要
        print("=" * 50)
        print("验证结果摘要:")
        print(f"✓ 返回按钮: 文本包含'{back_text}'")
        print(f"✓ 分享按钮: 样式类正确")
        print(f"✓ 分享弹窗: 正确显示和关闭")
        print(f"✓ 弹窗标题: '{title_text}'")
        print(f"✓ 分享内容: '{share_title_text}'")
        print(f"✓ 复制链接: 功能正常")
        print("=" * 50)
        switch_zipcode(headers=h5_autotest_header, zipcode="98011")