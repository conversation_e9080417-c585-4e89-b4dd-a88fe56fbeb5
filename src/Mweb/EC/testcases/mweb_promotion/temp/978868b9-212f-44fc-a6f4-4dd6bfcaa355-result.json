{"name": "[110551][mweb]prmotionh活动页展示UI/UX验证", "status": "passed", "description": "\n        H5活动页价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 验证活动标题和规则\n        4. 检查商品数量\n        5. 如果商品超过20个，验证过滤器存在\n        6. 测试价格过滤器功能\n        7. 验证商品信息展示\n        ", "start": 1757748751035, "stop": 1757748767154, "uuid": "4770e651-ec30-4731-88e3-cd6789715fb9", "historyId": "0195dbef9aa0b351fb6a5090646d6eaa", "testCaseId": "0195dbef9aa0b351fb6a5090646d6eaa", "fullName": "src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux.TestMWebPromotionFilterUIUX#test_110551_mweb_promotion_filter_ui_ux", "labels": [{"name": "story", "value": "[110551][mweb]prmotionh活动页展示UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5promotion"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_promotion"}, {"name": "suite", "value": "test_110551_mweb_promotion_detail_ui_ux"}, {"name": "subSuite", "value": "TestMWebPromotionFilterUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "24300-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux"}]}