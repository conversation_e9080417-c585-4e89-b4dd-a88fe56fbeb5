{"name": "【H5-活动页】价格过滤器UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Unsupported token \"@data-testid\" while parsing selector \"li[contains(@data-testid,'wid-promotion-drawer-price-filters-')]\"", "trace": "self = <src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux.TestMWebPromotionFilterUIUX object at 0x0000021CA3CA1C50>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc...20.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13143&pin_id=2155604'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...QhjvAWDEWOXWDc8PKcSW89rqfB8Y1gkEW5_CgXaVyi2ODN9i_LJ5diacZ2jIJrPV08-NfeeLTNX0n_VTFsMUDaxigq9Pltr6Eg2Fmdu7IwePjmIQ', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【H5-活动页】价格过滤器UI/UX验证\")\n    def test_mweb_promotion_filter_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        H5活动页价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 验证活动标题和规则\n        4. 检查商品数量\n        5. 如果商品超过20个，验证过滤器存在\n        6. 测试价格过滤器功能\n        7. 验证商品信息展示\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 直接进入活动页面\n        promotion_url = \"https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13143&pin_id=2155604\"\n        p.goto(promotion_url)\n        p.wait_for_timeout(5000)\n    \n        # 关闭可能的弹窗\n        if p.locator(\"//button[contains(text(), 'Continue')]\").all():\n            p.locator(\"//button[contains(text(), 'Continue')]\").click()\n    \n        print(\"活动页面加载完成\")\n    \n        # 2. 校验活动标题\n        promotion_title = p.locator(\"h3[data-testid='txt-promotion-drawer-title']\")\n        assert promotion_title.is_visible(), \"活动标题不可见\"\n        title_text = promotion_title.text_content()\n        assert title_text, \"活动标题为空\"\n    \n        # 验证标题样式\n        title_classes = promotion_title.get_attribute(\"class\")\n        assert \"enki-body-2xs\" in title_classes, f\"活动标题样式不正确: {title_classes}\"\n    \n        print(f\"活动标题验证完成: {title_text}\")\n    \n        # 3. 校验活动规则\n        promotion_rules = p.locator(\"p[data-testid='txt-promotion-drawer-rules']\")\n        assert promotion_rules.is_visible(), \"活动规则不可见\"\n        rules_text = promotion_rules.text_content()\n        assert rules_text, \"活动规则为空\"\n        assert \"Rules:\" in rules_text, f\"活动规则格式不正确: {rules_text}\"\n    \n        print(f\"活动规则验证完成: {rules_text[:50]}...\")\n    \n        # 4. 校验商品列表容器\n        product_list = p.locator(\"div[data-testid='wid-promotion-drawer-product-list']\")\n        assert product_list.is_visible(), \"商品列表容器不可见\"\n    \n        # 5. 获取商品数量\n        product_cards = p.locator(\"a[data-testid='wid-product-card-container']\").all()\n        product_count = len(product_cards)\n        assert product_count > 0, \"没有找到商品\"\n    \n        print(f\"页面商品数量: {product_count}\")\n    \n        # 6. 验证商品基本信息\n        self._verify_product_information(p, product_cards[:2])  # 验证前2个商品\n    \n        # 7. 检查价格过滤器\n        price_filters_container = p.locator(\"div[data-testid='wid-promotion-drawer-price-filters']\")\n    \n        if product_count > 20:\n            print(\"商品超过20个，检查价格过滤器...\")\n            assert price_filters_container.is_visible(), \"商品超过20个但价格过滤器不可见\"\n    \n            # 8. 测试价格过滤器功能\n            self._test_price_filter_functionality(p, product_count)\n        else:\n            print(f\"商品数量为{product_count}，未达到显示过滤器的条件\")\n            if price_filters_container.is_visible():\n                print(\"商品数量少于20个但仍显示过滤器，继续测试过滤功能\")\n>               self._test_price_filter_functionality(p, product_count)\n\ntest_110551_mweb_promotion_detail_ui_ux.py:88: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\ntest_110551_mweb_promotion_detail_ui_ux.py:152: in _test_price_filter_functionality\n    filter_items = p.locator(\"li[contains(@data-testid,'wid-promotion-drawer-price-filters-')]\").all()\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17056: in all\n    return mapping.from_impl_list(self._sync(self._impl_obj.all()))\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:384: in all\n    for index in range(await self.count()):\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:391: in count\n    return await self._frame._query_count(self._selector)\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:122: in _query_count\n    return await self._channel.send(\"queryCount\", {\"selector\": selector})\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x0000021CA1A17B90>\nmethod = 'queryCount'\nparams = {'selector': \"li[contains(@data-testid,'wid-promotion-drawer-price-filters-')]\"}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: Unsupported token \"@data-testid\" while parsing selector \"li[contains(@data-testid,'wid-promotion-drawer-price-filters-')]\"\n\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        H5活动页价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 验证活动标题和规则\n        4. 检查商品数量\n        5. 如果商品超过20个，验证过滤器存在\n        6. 测试价格过滤器功能\n        7. 验证商品信息展示\n        ", "start": 1754978398074, "stop": 1754978406861, "uuid": "3ce7ea93-971d-4c28-9112-7530a130ca68", "historyId": "f952be12ecba99ee55a0fda8c907b0b5", "testCaseId": "f952be12ecba99ee55a0fda8c907b0b5", "fullName": "src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux.TestMWebPromotionFilterUIUX#test_mweb_promotion_filter_ui_ux", "labels": [{"name": "story", "value": "【H5-活动页】价格过滤器UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5promotion"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_promotion"}, {"name": "suite", "value": "test_110551_mweb_promotion_detail_ui_ux"}, {"name": "subSuite", "value": "TestMWebPromotionFilterUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "26836-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux"}]}