{"name": "【H5-活动页】价格过滤器UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: 商品列表容器不可见\nassert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13573&pin_id=2252160%3FjoinEnki%3Dtrue'> selector='internal:testid=[data-testid=\"wid-promotion-drawer-product-list\"s]'>>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13573&pin_id=2252160%3FjoinEnki%3Dtrue'> selector='internal:testid=[data-testid=\"wid-promotion-drawer-product-list\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13573&pin_id=2252160%3FjoinEnki%3Dtrue'> selector='internal:testid=[data-testid=\"wid-promotion-drawer-product-list\"s]'>.is_visible", "trace": "self = <src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux.TestMWebPromotionFilterUIUX object at 0x0000021068AD9D50>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc...age': <Page url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13573&pin_id=2252160%3FjoinEnki%3Dtrue'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...SsUdg2OJ-YEQkGED7ixBO9OwmiDYwhtJ58C9ntfP3gKrAnsXNGgsZTvL-VDu6pfc_Y4yc7KdJObp3BYij8vYSq_T6oap3hhjBAxoNvQfK-whtjSs', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【H5-活动页】价格过滤器UI/UX验证\")\n    def test_mweb_promotion_filter_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        H5活动页价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 验证活动标题和规则\n        4. 检查商品数量\n        5. 如果商品超过20个，验证过滤器存在\n        6. 测试价格过滤器功能\n        7. 验证商品信息展示\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 初始化页面对象\n        promotion_page = MWebPDPPage(p, h5_autotest_header, browser_context=c,\n                                   page_url=\"/promotion/free-gift/landing?ps_id=13573&pin_id=2252160\")\n    \n        # 关闭可能的弹窗\n        continue_buttons = p.get_by_text(\"Continue\").all()\n        if continue_buttons:\n            continue_buttons[0].click()\n    \n        print(\"活动页面加载完成\")\n    \n        # 2. 校验活动标题 - 使用get_by_test_id\n        promotion_title = p.get_by_test_id(\"txt-promotion-drawer-title\")\n        assert promotion_title.is_visible(), \"活动标题不可见\"\n        title_text = promotion_title.text_content()\n        assert title_text, \"活动标题为空\"\n    \n        # 验证标题样式\n        title_classes = promotion_title.get_attribute(\"class\")\n        assert \"enki-body-2xs\" in title_classes, f\"活动标题样式不正确: {title_classes}\"\n    \n        print(f\"活动标题验证完成: {title_text}\")\n    \n        # 3. 校验活动规则 - 使用get_by_test_id\n        promotion_rules = p.get_by_test_id(\"txt-promotion-drawer-rules\")\n        assert promotion_rules.is_visible(), \"活动规则不可见\"\n        rules_text = promotion_rules.text_content()\n        assert rules_text, \"活动规则为空\"\n        assert \"Rules:\" in rules_text, f\"活动规则格式不正确: {rules_text}\"\n    \n        print(f\"活动规则验证完成: {rules_text[:50]}...\")\n    \n        # 4. 校验商品列表容器 - 使用get_by_test_id\n        product_list = p.get_by_test_id(\"wid-promotion-drawer-product-list\")\n>       assert product_list.is_visible(), \"商品列表容器不可见\"\nE       AssertionError: 商品列表容器不可见\nE       assert False\nE        +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13573&pin_id=2252160%3FjoinEnki%3Dtrue'> selector='internal:testid=[data-testid=\"wid-promotion-drawer-product-list\"s]'>>()\nE        +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13573&pin_id=2252160%3FjoinEnki%3Dtrue'> selector='internal:testid=[data-testid=\"wid-promotion-drawer-product-list\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13573&pin_id=2252160%3FjoinEnki%3Dtrue'> selector='internal:testid=[data-testid=\"wid-promotion-drawer-product-list\"s]'>.is_visible\n\ntest_110551_mweb_promotion_detail_ui_ux.py:63: AssertionError"}, "description": "\n        H5活动页价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 验证活动标题和规则\n        4. 检查商品数量\n        5. 如果商品超过20个，验证过滤器存在\n        6. 测试价格过滤器功能\n        7. 验证商品信息展示\n        ", "start": 1755049942171, "stop": 1755049957308, "uuid": "a37c465c-0370-40b7-be7c-f43c81658b20", "historyId": "f952be12ecba99ee55a0fda8c907b0b5", "testCaseId": "f952be12ecba99ee55a0fda8c907b0b5", "fullName": "src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux.TestMWebPromotionFilterUIUX#test_mweb_promotion_filter_ui_ux", "labels": [{"name": "story", "value": "【H5-活动页】价格过滤器UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5promotion"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_promotion"}, {"name": "suite", "value": "test_110551_mweb_promotion_detail_ui_ux"}, {"name": "subSuite", "value": "TestMWebPromotionFilterUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "10052-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux"}]}