{"name": "[110551][mweb]prmotionh活动页展示UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: 活动标题不可见\nassert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=10760&joinEnki=true'> selector='internal:testid=[data-testid=\"txt-promotion-drawer-title\"s]'>>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=10760&joinEnki=true'> selector='internal:testid=[data-testid=\"txt-promotion-drawer-title\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=10760&joinEnki=true'> selector='internal:testid=[data-testid=\"txt-promotion-drawer-title\"s]'>.is_visible", "trace": "self = <src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux.TestMWebPromotionFilterUIUX object at 0x0000022396C9EF50>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc...120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=10760&joinEnki=true'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...x6jQRkN1hqR5ProgYiyk9JryzXsy4vdRN_Xtd6mqMUFuI_D4iQBrfAhHmO_4TPhW2E2ma16Rfh0oeQMsQmond92UzKyBBhxw_4rgI1-ZF87akk5M', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"[110551][mweb]prmotionh活动页展示UI/UX验证\")\n    def test_110551_mweb_promotion_filter_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        H5活动页价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 验证活动标题和规则\n        4. 检查商品数量\n        5. 如果商品超过20个，验证过滤器存在\n        6. 测试价格过滤器功能\n        7. 验证商品信息展示\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 初始化页面对象 - 使用一个稳定的活动ID\n        promotion_page = MWebPromotionPage(p, h5_autotest_header, browser_context=c,\n                                   page_url=\"/promotion/free-gift/landing?ps_id=10760\")\n        switch_zipcode(headers=h5_autotest_header, zipcode=\"99991\")\n        # 关闭可能的弹窗\n        continue_buttons = p.get_by_text(\"Continue\").all()\n        if continue_buttons:\n            continue_buttons[0].click()\n    \n        print(\"活动页面加载完成\")\n    \n        # 1.5. 检查活动状态\n        self._check_promotion_status(p)\n    \n        # 2. 校验活动标题\n        promotion_title = p.get_by_test_id(\"txt-promotion-drawer-title\")\n>       assert promotion_title.is_visible(), \"活动标题不可见\"\nE       AssertionError: 活动标题不可见\nE       assert False\nE        +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=10760&joinEnki=true'> selector='internal:testid=[data-testid=\"txt-promotion-drawer-title\"s]'>>()\nE        +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=10760&joinEnki=true'> selector='internal:testid=[data-testid=\"txt-promotion-drawer-title\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=10760&joinEnki=true'> selector='internal:testid=[data-testid=\"txt-promotion-drawer-title\"s]'>.is_visible\n\ntest_110551_mweb_promotion_detail_ui_ux.py:47: AssertionError"}, "description": "\n        H5活动页价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 验证活动标题和规则\n        4. 检查商品数量\n        5. 如果商品超过20个，验证过滤器存在\n        6. 测试价格过滤器功能\n        7. 验证商品信息展示\n        ", "start": 1757748457451, "stop": 1757748468448, "uuid": "b2203df0-0b13-4ed8-90af-abcc8c794395", "historyId": "0195dbef9aa0b351fb6a5090646d6eaa", "testCaseId": "0195dbef9aa0b351fb6a5090646d6eaa", "fullName": "src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux.TestMWebPromotionFilterUIUX#test_110551_mweb_promotion_filter_ui_ux", "labels": [{"name": "story", "value": "[110551][mweb]prmotionh活动页展示UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5promotion"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_promotion"}, {"name": "suite", "value": "test_110551_mweb_promotion_detail_ui_ux"}, {"name": "subSuite", "value": "TestMWebPromotionFilterUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "27452-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux"}]}