{"name": "【110552】mweb-promotion page 分享功能流程验证", "status": "passed", "description": "\n        【110552】mweb-promotion page 分享功能流程验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 校验返回按钮和分享按钮\n        4. 测试分享按钮点击功能\n        5. 校验分享弹窗内容\n        6. 校验分享方式选项\n        7. 测试复制链接功能\n        8. 测试弹窗关闭功能\n        ", "start": 1754032634745, "stop": 1754032647532, "uuid": "e2b65378-e74d-48ad-beef-55fe5e71f50e", "historyId": "354156880756b1abe386e04ca861b4d5", "testCaseId": "354156880756b1abe386e04ca861b4d5", "fullName": "src.Mweb.EC.testcases.mweb_promotion.test_110552_mweb_promotion_ui_ux.TestMWebPromotionPageShareUIUX#test_110552_mweb_promotion_page_share_ui_ux", "labels": [{"name": "story", "value": "【110552】mweb-promotion page 分享功能流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5activity"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_promotion"}, {"name": "suite", "value": "test_110552_mweb_promotion_ui_ux"}, {"name": "subSuite", "value": "TestMWebPromotionPageShareUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "21984-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_promotion.test_110552_mweb_promotion_ui_ux"}]}