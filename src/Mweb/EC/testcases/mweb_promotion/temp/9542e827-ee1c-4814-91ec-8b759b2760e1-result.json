{"name": "【H5-活动页】价格过滤器UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: 第3个商品图片不可见\nassert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13143&pin_id=2155604'> selector=\"a[data-testid='wid-product-card-container'] >> nth=2 >> img[data-testid='wid-product-card-product-image']\">>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13143&pin_id=2155604'> selector=\"a[data-testid='wid-product-card-container'] >> nth=2 >> img[data-testid='wid-product-card-product-image']\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13143&pin_id=2155604'> selector=\"a[data-testid='wid-product-card-container'] >> nth=2 >> img[data-testid='wid-product-card-product-image']\">.is_visible", "trace": "self = <src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux.TestMWebPromotionFilterUIUX object at 0x000001A3692373D0>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc...20.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13143&pin_id=2155604'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...giK_BPC5rb-8FdYheWM7DtdreJDKkK08CWilcRx8mYJNFqKWScJ3_Af-wE5bXuQw_cXmL9un8eNgDy9goCU3miZ-EMvjxLuwXjoCLbl3EMn8hIYA', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【H5-活动页】价格过滤器UI/UX验证\")\n    def test_mweb_promotion_filter_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        H5活动页价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 验证活动标题和规则\n        4. 检查商品数量\n        5. 如果商品超过20个，验证过滤器存在\n        6. 测试价格过滤器功能\n        7. 验证商品信息展示\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 直接进入活动页面\n        promotion_url = \"https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13143&pin_id=2155604\"\n        p.goto(promotion_url)\n        p.wait_for_timeout(5000)\n    \n        # 关闭可能的弹窗\n        if p.locator(\"//button[contains(text(), 'Continue')]\").all():\n            p.locator(\"//button[contains(text(), 'Continue')]\").click()\n    \n        print(\"活动页面加载完成\")\n    \n        # 2. 校验活动标题\n        promotion_title = p.locator(\"h3[data-testid='txt-promotion-drawer-title']\")\n        assert promotion_title.is_visible(), \"活动标题不可见\"\n        title_text = promotion_title.text_content()\n        assert title_text, \"活动标题为空\"\n    \n        # 验证标题样式\n        title_classes = promotion_title.get_attribute(\"class\")\n        assert \"enki-body-2xs\" in title_classes, f\"活动标题样式不正确: {title_classes}\"\n    \n        print(f\"活动标题验证完成: {title_text}\")\n    \n        # 3. 校验活动规则\n        promotion_rules = p.locator(\"p[data-testid='txt-promotion-drawer-rules']\")\n        assert promotion_rules.is_visible(), \"活动规则不可见\"\n        rules_text = promotion_rules.text_content()\n        assert rules_text, \"活动规则为空\"\n        assert \"Rules:\" in rules_text, f\"活动规则格式不正确: {rules_text}\"\n    \n        print(f\"活动规则验证完成: {rules_text[:50]}...\")\n    \n        # 4. 校验商品列表容器\n        product_list = p.locator(\"div[data-testid='wid-promotion-drawer-product-list']\")\n        assert product_list.is_visible(), \"商品列表容器不可见\"\n    \n        # 5. 获取商品数量\n        product_cards = p.locator(\"a[data-testid='wid-product-card-container']\").all()\n        product_count = len(product_cards)\n        assert product_count > 0, \"没有找到商品\"\n    \n        print(f\"页面商品数量: {product_count}\")\n    \n        # 6. 验证商品基本信息\n>       self._verify_product_information(p, product_cards[:3])  # 验证前3个商品\n\ntest_110551_mweb_promotion_detail_ui_ux.py:73: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux.TestMWebPromotionFilterUIUX object at 0x000001A3692373D0>\np = <Page url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13143&pin_id=2155604'>\nproduct_cards = [<Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13143&pin_id=2155604'> ...motion/free-gift/landing?ps_id=13143&pin_id=2155604'> selector=\"a[data-testid='wid-product-card-container'] >> nth=2\">]\n\n    def _verify_product_information(self, p: Page, product_cards):\n        \"\"\"验证商品信息\"\"\"\n        print(\"开始验证商品信息...\")\n    \n        for index, card in enumerate(product_cards):\n            print(f\"验证第 {index + 1} 个商品...\")\n    \n            # 验证商品图片\n            product_image = card.locator(\"img[data-testid='wid-product-card-product-image']\")\n>           assert product_image.is_visible(), f\"第{index + 1}个商品图片不可见\"\nE           AssertionError: 第3个商品图片不可见\nE           assert False\nE            +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13143&pin_id=2155604'> selector=\"a[data-testid='wid-product-card-container'] >> nth=2 >> img[data-testid='wid-product-card-product-image']\">>()\nE            +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13143&pin_id=2155604'> selector=\"a[data-testid='wid-product-card-container'] >> nth=2 >> img[data-testid='wid-product-card-product-image']\">> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13143&pin_id=2155604'> selector=\"a[data-testid='wid-product-card-container'] >> nth=2 >> img[data-testid='wid-product-card-product-image']\">.is_visible\n\ntest_110551_mweb_promotion_detail_ui_ux.py:104: AssertionError"}, "description": "\n        H5活动页价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 验证活动标题和规则\n        4. 检查商品数量\n        5. 如果商品超过20个，验证过滤器存在\n        6. 测试价格过滤器功能\n        7. 验证商品信息展示\n        ", "start": 1754884172353, "stop": 1754884181584, "uuid": "ee61bc38-4101-45c9-9b0b-f0456f69861a", "historyId": "f952be12ecba99ee55a0fda8c907b0b5", "testCaseId": "f952be12ecba99ee55a0fda8c907b0b5", "fullName": "src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux.TestMWebPromotionFilterUIUX#test_mweb_promotion_filter_ui_ux", "labels": [{"name": "story", "value": "【H5-活动页】价格过滤器UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5promotion"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_promotion"}, {"name": "suite", "value": "test_110551_mweb_promotion_detail_ui_ux"}, {"name": "subSuite", "value": "TestMWebPromotionFilterUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "20128-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux"}]}