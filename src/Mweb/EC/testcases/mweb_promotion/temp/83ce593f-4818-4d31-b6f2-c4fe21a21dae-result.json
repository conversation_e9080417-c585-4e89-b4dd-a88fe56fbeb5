{"name": "【110552】mweb-promotion page 分享功能流程验证", "status": "failed", "statusDetails": {"message": "AssertionError: 复制链接文本不正确: Copy link\nassert '复制链接' in 'Copy link'", "trace": "self = <src.Mweb.EC.testcases.mweb_promotion.test_110552_mweb_promotion_ui_ux.TestMWebPromotionPageShareUIUX object at 0x00000288DD80F310>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc....exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13440'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...6C920DYXPReMXiNkYZX-nqhxJxL0_wi1n_-MWmggfn_PDNnYQ130HMdydPcLANIOuda7KHLdAALHUteaVcxC4i_VsYCDSoq9z1zckj5SZeHcejE8', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【110552】mweb-promotion page 分享功能流程验证\")\n    def test_110552_mweb_promotion_page_share_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【110552】mweb-promotion page 分享功能流程验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 校验返回按钮和分享按钮\n        4. 测试分享按钮点击功能\n        5. 校验分享弹窗内容\n        6. 校验分享方式选项\n        7. 测试复制链接功能\n        8. 测试弹窗关闭功能\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 直接进入活动页面,需要选择一个线上开启得活动\n        activity_url = \"https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13440\"\n        p.goto(activity_url)\n        p.wait_for_timeout(5000)\n    \n        # 关闭可能的弹窗\n        if p.locator(\"//button[contains(text(), 'Continue')]\").all():\n            p.locator(\"//button[contains(text(), 'Continue')]\").click()\n    \n        # 2. 校验页面基本元素\n        # 校验返回按钮存在\n        back_button = p.locator(\"span[data-testid='wid-page-nav-header-back-button']\")\n        assert back_button.is_visible(), \"活动页面返回按钮不可见\"\n    \n        # 验证返回按钮文本和图标\n        back_text = back_button.text_content()\n        assert \"Back\" in back_text, f\"返回按钮文本不正确: {back_text}\"\n    \n        # 验证返回按钮包含SVG图标\n        back_svg = back_button.locator(\"svg\")\n        assert back_svg.is_visible(), \"返回按钮图标不可见\"\n    \n        print(f\"返回按钮验证完成 - 文本: {back_text}\")\n    \n        # 3. 校验分享按钮存在\n        share_button = p.locator(\"i[data-testid='btn-share']\")\n        assert share_button.is_visible(), \"活动页面分享按钮不可见\"\n    \n        # 验证分享按钮的样式类\n        share_classes = share_button.get_attribute(\"class\")\n        assert \"icon iconfont icona-share-ios1\" in share_classes, f\"分享按钮样式类不正确: {share_classes}\"\n    \n        # 验证分享按钮的alt属性\n        share_alt = share_button.get_attribute(\"alt\")\n        assert share_alt == \"share button image\", f\"分享按钮alt属性不正确: {share_alt}\"\n    \n        print(\"分享按钮验证完成\")\n    \n        # 4. 测试分享按钮点击功能\n        share_button.click()\n        p.wait_for_timeout(2000)\n    \n        # 5. 校验分享弹窗出现\n        share_popup = p.locator(\"div[data-testid='mod-share-popup']\")\n        assert share_popup.is_visible(), \"分享弹窗未出现\"\n    \n        # 验证分享弹窗的样式类\n        popup_classes = share_popup.get_attribute(\"class\")\n        expected_popup_classes = [\"share-popup-content\", \"enki-elevation-5\", \"bg-surface-100-bg\"]\n        for expected_class in expected_popup_classes:\n            assert expected_class in popup_classes, f\"分享弹窗缺少样式类'{expected_class}': {popup_classes}\"\n    \n        print(\"分享弹窗显示验证完成\")\n    \n        # 6. 校验分享弹窗头部\n        popup_header = p.locator(\"header[data-testid='wid-popup-header']\")\n        assert popup_header.is_visible(), \"分享弹窗头部不可见\"\n    \n        # 验证弹窗头部ID\n        header_id = popup_header.get_attribute(\"id\")\n        assert header_id == \"share-popup-header\", f\"分享弹窗头部ID不正确: {header_id}\"\n    \n        # 7. 校验分享弹窗标题\n        popup_title = popup_header.locator(\"h2\")\n        assert popup_title.is_visible(), \"分享弹窗标题不可见\"\n        title_text = popup_title.text_content()\n        assert title_text == \"Share\", f\"分享弹窗标题不正确: {title_text}\"\n    \n        print(f\"分享弹窗标题验证完成 - 标题: {title_text}\")\n    \n        # 8. 校验关闭按钮\n        close_button = p.locator(\"div[data-testid='btn-modal-close']\")\n        assert close_button.is_visible(), \"分享弹窗关闭按钮不可见\"\n    \n        # 验证关闭按钮包含图标\n        close_icon = close_button.locator(\"i\")\n        assert close_icon.is_visible(), \"关闭按钮图标不可见\"\n        close_icon_classes = close_icon.get_attribute(\"class\")\n        assert \"iconclose\" in close_icon_classes, f\"关闭按钮图标样式不正确: {close_icon_classes}\"\n    \n        # 9. 校验分享内容\n        # 校验分享图片\n        share_image = p.locator(\"img[data-testid='wid-share-image']\")\n        assert share_image.is_visible(), \"分享图片不可见\"\n    \n        # 验证图片属性\n        image_alt = share_image.get_attribute(\"alt\")\n        assert image_alt == \"share popup share image\", f\"分享图片alt属性不正确: {image_alt}\"\n    \n        image_src = share_image.get_attribute(\"src\")\n        assert \"weeecdn.net\" in image_src, f\"分享图片src不正确: {image_src}\"\n    \n        # 10. 校验分享标题\n        share_title = p.locator(\"div[data-testid='wid-share-title']\")\n        assert share_title.is_visible(), \"分享标题不可见\"\n        share_title_text = share_title.text_content()\n        assert share_title_text, \"分享标题为空\"\n    \n        # 验证分享标题样式\n        title_classes = share_title.get_attribute(\"class\")\n        assert \"enki-body-base-medium\" in title_classes, f\"分享标题样式不正确: {title_classes}\"\n        assert \"line-clamp-2\" in title_classes, f\"分享标题行数限制样式不正确: {title_classes}\"\n    \n        print(f\"分享内容验证完成 - 标题: {share_title_text}\")\n    \n        \"\"\" # 11. 校验分享描述\n        share_desc = p.locator(\"div[data-testid='wid-share-desc']\")\n        if share_desc.is_visible():\n            # 验证分享描述样式\n            desc_classes = share_desc.get_attribute(\"class\")\n            assert \"enki-body-sm\" in desc_classes, f\"分享描述样式不正确: {desc_classes}\"\n    \n            # 获取描述内容（允许为空）\n            desc_text = share_desc.text_content()\n            print(f\"分享描述内容: {desc_text if desc_text.strip() else '描述为空'}\")\n        else:\n            print(\"分享描述元素不可见，跳过描述验证\")\n    \n        # 验证分享描述样式\n        desc_classes = share_desc.get_attribute(\"class\")\n        assert \"enki-body-sm\" in desc_classes, f\"分享描述样式不正确: {desc_classes}\"\n        \"\"\"\n    \n        # 12. 校验分享方式选项\n        # 校验复制链接选项\n        copy_link_option = p.locator(\"div[data-testid='btn-share-method-copyLink']\")\n        assert copy_link_option.is_visible(), \"复制链接选项不可见\"\n    \n        # 验证复制链接选项的data-method属性\n        copy_method = copy_link_option.get_attribute(\"data-method\")\n        assert copy_method == \"copyLink\", f\"复制链接方法属性不正确: {copy_method}\"\n    \n        # 验证复制链接选项的文本\n        copy_text = copy_link_option.text_content()\n>       assert \"复制链接\" in copy_text, f\"复制链接文本不正确: {copy_text}\"\nE       AssertionError: 复制链接文本不正确: Copy link\nE       assert '复制链接' in 'Copy link'\n\ntest_110552_mweb_promotion_ui_ux.py:164: AssertionError"}, "description": "\n        【110552】mweb-promotion page 分享功能流程验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 校验返回按钮和分享按钮\n        4. 测试分享按钮点击功能\n        5. 校验分享弹窗内容\n        6. 校验分享方式选项\n        7. 测试复制链接功能\n        8. 测试弹窗关闭功能\n        ", "start": 1754032445244, "stop": 1754032456123, "uuid": "7f5ff1fb-97d3-4556-9f03-42d7846bd2d0", "historyId": "354156880756b1abe386e04ca861b4d5", "testCaseId": "354156880756b1abe386e04ca861b4d5", "fullName": "src.Mweb.EC.testcases.mweb_promotion.test_110552_mweb_promotion_ui_ux.TestMWebPromotionPageShareUIUX#test_110552_mweb_promotion_page_share_ui_ux", "labels": [{"name": "story", "value": "【110552】mweb-promotion page 分享功能流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5activity"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_promotion"}, {"name": "suite", "value": "test_110552_mweb_promotion_ui_ux"}, {"name": "subSuite", "value": "TestMWebPromotionPageShareUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "19432-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_promotion.test_110552_mweb_promotion_ui_ux"}]}