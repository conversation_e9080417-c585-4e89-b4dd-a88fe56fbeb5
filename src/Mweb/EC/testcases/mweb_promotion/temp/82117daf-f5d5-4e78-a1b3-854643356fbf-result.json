{"name": "【110552】mweb-promotion page 分享功能流程验证", "status": "failed", "statusDetails": {"message": "AssertionError: 活动页面分享按钮不可见\nassert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=10758%3FjoinEnki%3Dtrue'> selector='internal:testid=[data-testid=\"btn-share\"s]'>>()\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=10758%3FjoinEnki%3Dtrue'> selector='internal:testid=[data-testid=\"btn-share\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=10758%3FjoinEnki%3Dtrue'> selector='internal:testid=[data-testid=\"btn-share\"s]'>.is_visible", "trace": "self = <src.Mweb.EC.testcases.mweb_promotion.test_110552_mweb_promotion_share_ui_ux.TestMWebPromotionPageShareUIUX object at 0x00000181F774A2D0>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc...0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=10758%3FjoinEnki%3Dtrue'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...UGGbDo6L93d2jvSOmV3iScfTVtIDvlOmzckDi0Bw4d4OZU4w1uNklqYfLfhXojHD98d0N1jKM71UnD2g0RwUu7s1MuzkR0je7Vkk1KGEFExB_qIw', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【110552】mweb-promotion page 分享功能流程验证\")\n    def test_110552_mweb_promotion_page_share_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        【110552】mweb-promotion page 分享功能流程验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 校验返回按钮和分享按钮\n        4. 测试分享按钮点击功能\n        5. 校验分享弹窗内容\n        6. 校验分享方式选项\n        7. 测试复制链接功能\n        8. 测试弹窗关闭功能\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 初始化页面对象\n        promotion_page = MWebPDPPage(p, h5_autotest_header, browser_context=c,\n                                   page_url=\"/promotion/free-gift/landing?ps_id=10758\")\n        switch_zipcode(headers=h5_autotest_header, zipcode=\"99991\")\n        p.wait_for_timeout(1000)\n        # 关闭可能的弹窗\n        continue_buttons = p.get_by_text(\"Continue\").all()\n        if continue_buttons:\n            continue_buttons[0].click()\n    \n        # 2. 校验页面基本元素\n        # 校验返回按钮存在 - 使用get_by_test_id\n        back_button = p.get_by_test_id(\"wid-page-nav-header-back-button\")\n        assert back_button.is_visible(), \"活动页面返回按钮不可见\"\n    \n        # 验证返回按钮文本和图标\n        back_text = back_button.text_content()\n        assert \"Back\" in back_text, f\"返回按钮文本不正确: {back_text}\"\n    \n        # 验证返回按钮包含SVG图标\n        back_svg = back_button.locator(\"svg\")\n        assert back_svg.is_visible(), \"返回按钮图标不可见\"\n    \n        print(f\"返回按钮验证完成 - 文本: {back_text}\")\n    \n        # 3. 校验分享按钮存在 - 使用get_by_test_id\n        share_button = p.get_by_test_id(\"btn-share\")\n>       assert share_button.is_visible(), \"活动页面分享按钮不可见\"\nE       AssertionError: 活动页面分享按钮不可见\nE       assert False\nE        +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=10758%3FjoinEnki%3Dtrue'> selector='internal:testid=[data-testid=\"btn-share\"s]'>>()\nE        +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=10758%3FjoinEnki%3Dtrue'> selector='internal:testid=[data-testid=\"btn-share\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=10758%3FjoinEnki%3Dtrue'> selector='internal:testid=[data-testid=\"btn-share\"s]'>.is_visible\n\ntest_110552_mweb_promotion_share_ui_ux.py:58: AssertionError"}, "description": "\n        【110552】mweb-promotion page 分享功能流程验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 校验返回按钮和分享按钮\n        4. 测试分享按钮点击功能\n        5. 校验分享弹窗内容\n        6. 校验分享方式选项\n        7. 测试复制链接功能\n        8. 测试弹窗关闭功能\n        ", "start": 1757748180893, "stop": 1757748192452, "uuid": "0db5e8d2-572e-47ce-8959-7d4bd0f7f449", "historyId": "8451b8c5d0322140f7ff078e11baadd2", "testCaseId": "8451b8c5d0322140f7ff078e11baadd2", "fullName": "src.Mweb.EC.testcases.mweb_promotion.test_110552_mweb_promotion_share_ui_ux.TestMWebPromotionPageShareUIUX#test_110552_mweb_promotion_page_share_ui_ux", "labels": [{"name": "story", "value": "【110552】mweb-promotion page 分享功能流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5activity"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_promotion"}, {"name": "suite", "value": "test_110552_mweb_promotion_share_ui_ux"}, {"name": "subSuite", "value": "TestMWebPromotionPageShareUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "24320-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_promotion.test_110552_mweb_promotion_share_ui_ux"}]}