{"name": "【110552】mweb-promotion page 分享功能流程验证", "status": "passed", "description": "\n        【110552】mweb-promotion page 分享功能流程验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 校验返回按钮和分享按钮\n        4. 测试分享按钮点击功能\n        5. 校验分享弹窗内容\n        6. 校验分享方式选项\n        7. 测试复制链接功能\n        8. 测试弹窗关闭功能\n        ", "start": 1755049889088, "stop": 1755049907694, "uuid": "a5c9e66f-3965-4216-acd4-6686b51d5615", "historyId": "8451b8c5d0322140f7ff078e11baadd2", "testCaseId": "8451b8c5d0322140f7ff078e11baadd2", "fullName": "src.Mweb.EC.testcases.mweb_promotion.test_110552_mweb_promotion_share_ui_ux.TestMWebPromotionPageShareUIUX#test_110552_mweb_promotion_page_share_ui_ux", "labels": [{"name": "story", "value": "【110552】mweb-promotion page 分享功能流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5activity"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_promotion"}, {"name": "suite", "value": "test_110552_mweb_promotion_share_ui_ux"}, {"name": "subSuite", "value": "TestMWebPromotionPageShareUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "9716-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_promotion.test_110552_mweb_promotion_share_ui_ux"}]}