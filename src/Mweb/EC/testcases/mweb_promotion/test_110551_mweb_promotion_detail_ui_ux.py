import allure
import pytest

from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_promotion.mweb_page_promotion import MWebPromotionPage
from src.api.zipcode import switch_zipcode


@allure.story("[110551][mweb]prmotionh活动页展示UI/UX验证")
class TestMWebPromotionFilterUIUX:
    pytestmark = [pytest.mark.h5promotion, pytest.mark.mweb_regression, pytest.mark.transaction]

    @allure.title("[110551][mweb]prmotionh活动页展示UI/UX验证")
    def test_110551_mweb_promotion_filter_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        H5活动页价格过滤器UI/UX验证
        测试步骤：
        1. 访问活动页面
        2. 校验页面基本元素
        3. 验证活动标题和规则
        4. 检查商品数量
        5. 如果商品超过20个，验证过滤器存在
        6. 测试价格过滤器功能
        7. 验证商品信息展示
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 初始化页面对象 - 使用一个稳定的活动ID
        promotion_page = MWebPromotionPage(p, h5_autotest_header, browser_context=c,
                                   page_url="/promotion/free-gift/landing?ps_id=10758")
        switch_zipcode(headers=h5_autotest_header, zipcode="99991")
        p.wait_for_timeout(1000)
        # 关闭可能的弹窗
        continue_buttons = p.get_by_text("Continue").all()
        if continue_buttons:
            continue_buttons[0].click()

        print("活动页面加载完成")

        # 1.5. 检查活动状态
        self._check_promotion_status(p)

        # 2. 校验活动标题   
        promotion_title = p.get_by_test_id("txt-promotion-drawer-title")
        assert promotion_title.is_visible(), "活动标题不可见"
        title_text = promotion_title.text_content()
        assert title_text, "活动标题为空"

        # 验证标题样式
        title_classes = promotion_title.get_attribute("class")
        assert "enki-body-2xs" in title_classes, f"活动标题样式不正确: {title_classes}"

        print(f"活动标题验证完成: {title_text}")

        # 3. 校验活动规则   
        promotion_rules = p.get_by_test_id("txt-promotion-drawer-rules")
        assert promotion_rules.is_visible(), "活动规则不可见"
        rules_text = promotion_rules.text_content()
        assert rules_text, "活动规则为空"
        assert "Rules:" in rules_text, f"活动规则格式不正确: {rules_text}"

        print(f"活动规则验证完成: {rules_text[:50]}...")

        # 4. 校验商品列表容器   ，添加调试信息
        print(f"当前页面URL: {p.url}")

        # 等待页面完全加载
        p.wait_for_load_state("networkidle", timeout=30000)
        p.wait_for_timeout(3000)

        # 检查商品列表容器
        product_list = p.get_by_test_id("wid-promotion-drawer-product-list")

        # 添加调试信息
        if not product_list.is_visible():
            print("商品列表容器不可见，开始调试...")

            # 检查页面是否正确加载
            page_title = p.title()
            print(f"页面标题: {page_title}")

            # 查找所有可能的商品容器
            all_divs_with_testid = p.locator("[data-testid*='product']").all()
            print(f"找到包含'product'的元素数量: {len(all_divs_with_testid)}")

            for i, div in enumerate(all_divs_with_testid[:5]):  # 只显示前5个
                testid = div.get_attribute("data-testid")
                print(f"元素{i+1}: data-testid='{testid}'")

            # 查找所有可能的列表容器
            all_divs_with_list = p.locator("[data-testid*='list']").all()
            print(f"找到包含'list'的元素数量: {len(all_divs_with_list)}")

            for i, div in enumerate(all_divs_with_list[:5]):  # 只显示前5个
                testid = div.get_attribute("data-testid")
                print(f"列表元素{i+1}: data-testid='{testid}'")

            # 检查是否有错误信息或空状态
            error_messages = p.get_by_text("No products found").all()
            if error_messages:
                print("发现'No products found'消息")
                pytest.skip("活动没有商品，跳过测试")

            empty_messages = p.get_by_text("Coming soon").all()
            if empty_messages:
                print("发现'Coming soon'消息")
                pytest.skip("活动即将开始，跳过测试")

        assert product_list.is_visible(), "商品列表容器不可见"

        # 5. 获取商品数量   ，添加容错处理
        product_cards = p.get_by_test_id("wid-product-card-container").all()
        product_count = len(product_cards)

        if product_count == 0:
            print("没有找到商品，尝试查找其他可能的商品容器...")

            # 尝试其他可能的商品选择器
            alternative_selectors = [
                "wid-product-card",
                "product-card-container",
                "product-item",
                "promotion-product"
            ]

            for selector in alternative_selectors:
                alt_products = p.get_by_test_id(selector).all()
                if len(alt_products) > 0:
                    print(f"使用备选选择器'{selector}'找到{len(alt_products)}个商品")
                    product_cards = alt_products
                    product_count = len(alt_products)
                    break

            # 如果仍然没有找到商品，检查活动状态
            if product_count == 0:
                # 检查是否有活动结束或无商品的提示
                no_products_indicators = [
                    "No products available",
                    "Coming soon",
                    "Activity ended",
                    "暂无商品",
                    "即将开始",
                    "活动已结束"
                ]

                for indicator in no_products_indicators:
                    if p.get_by_text(indicator).count() > 0:
                        print(f"发现活动状态提示: {indicator}")
                        pytest.skip(f"活动状态异常: {indicator}")

                # 最后的断言
                assert product_count > 0, f"没有找到商品，页面URL: {p.url}"

        print(f"页面商品数量: {product_count}")

        # 6. 验证商品基本信息
        self._verify_product_information(p, product_cards[:2])  # 验证前2个商品

        # 7. 检查价格过滤器   
        price_filters_container = p.get_by_test_id("wid-promotion-drawer-price-filters")

        if product_count > 20:
            print("商品超过20个，检查价格过滤器...")
            assert price_filters_container.is_visible(), "商品超过20个但价格过滤器不可见"

            # 8. 测试价格过滤器功能
            self._test_price_filter_functionality(p, product_count)
        else:
            print(f"商品数量为{product_count}，未达到显示过滤器的条件")
            if price_filters_container.is_visible():
                print("商品数量少于20个但仍显示过滤器，继续测试过滤功能")
                self._test_price_filter_functionality(p, product_count)

        # 9. 验证活动进度信息（如果存在）
        self._verify_promotion_progress(p)

        print("活动页面价格过滤器验证完成")


    def _check_promotion_status(self, p: Page):
        """检查活动状态，如果活动异常则跳过测试"""
        # 检查常见的活动状态提示
        status_indicators = {
            "No products available": "活动暂无商品",
            "Coming soon": "活动即将开始",
            "Activity ended": "活动已结束",
            "暂无商品": "活动暂无商品",
            "即将开始": "活动即将开始",
            "活动已结束": "活动已结束",
            "Promotion not found": "活动不存在",
            "404": "页面不存在"
        }

        for indicator, message in status_indicators.items():
            if p.get_by_text(indicator).count() > 0:
                print(f"发现活动状态提示: {indicator}")
                pytest.skip(f"活动状态异常: {message}")

        # 检查页面标题是否包含错误信息
        page_title = p.title()
        if "404" in page_title or "Not Found" in page_title:
            pytest.skip(f"页面不存在: {page_title}")

    def _verify_product_information(self, p: Page, product_cards):
        """验证商品信息 - 使用get_by_test_id优化"""
        print("开始验证商品信息...")

        for index, card in enumerate(product_cards):
            print(f"验证第 {index + 1} 个商品...")

            # 验证商品图片    
            product_image = card.get_by_test_id("wid-product-card-product-image")
            assert product_image.count() > 0, f"第{index + 1}个商品图片不存在"
            assert product_image.is_visible(), f"第{index + 1}个商品图片不可见"

            image_alt = product_image.get_attribute("alt")
            image_src = product_image.get_attribute("src")
            assert image_alt, f"第{index + 1}个商品图片alt属性为空"
            assert image_src and "weeecdn" in image_src, f"第{index + 1}个商品图片src不正确: {image_src}"

            # 验证商品标题    
            product_title = card.get_by_test_id("wid-product-card-title")
            assert product_title.count() > 0, f"第{index + 1}个商品标题不存在"
            assert product_title.is_visible(), f"第{index + 1}个商品标题不可见"
            title_text = product_title.text_content()
            assert title_text, f"第{index + 1}个商品标题为空"

            # 验证商品价格    
            product_price = card.get_by_test_id("wid-product-card-price-value")
            assert product_price.count() > 0, f"第{index + 1}个商品价格不存在"
            assert product_price.is_visible(), f"第{index + 1}个商品价格不可见"
            price_text = product_price.text_content()
            assert price_text and price_text.startswith("$"), f"第{index + 1}个商品价格格式不正确: {price_text}"

            # 验证商品原价（如果存在）    
            base_price = card.get_by_test_id("wid-product-card-base-price")
            if base_price.count() > 0 and base_price.is_visible():
                base_price_text = base_price.text_content()
                base_price_classes = base_price.get_attribute("class")
                assert "line-through" in base_price_classes, f"第{index + 1}个商品原价样式不正确"
                print(f"第{index + 1}个商品原价: {base_price_text}")

            # 验证收藏按钮    
            favorite_btn = card.get_by_test_id("btn-favorite")
            assert favorite_btn.count() > 0, f"第{index + 1}个商品收藏按钮不存在"
            assert favorite_btn.is_visible(), f"第{index + 1}个商品收藏按钮不可见"

            # 验证加购按钮    
            atc_btn = card.get_by_test_id("btn-atc-plus")
            assert atc_btn.count() > 0, f"第{index + 1}个商品加购按钮不存在"
            assert atc_btn.is_visible(), f"第{index + 1}个商品加购按钮不可见"

            # 验证销量标签（如果存在）    
            weekly_sold = card.get_by_test_id("wid-product-card-weekly-sold")
            if weekly_sold.count() > 0 and weekly_sold.is_visible():
                sold_text = weekly_sold.text_content()
                print(f"第{index + 1}个商品销量: {sold_text}")

            print(f"第{index + 1}个商品验证完成 - 标题: {title_text[:30]}..., 价格: {price_text}")

    def _test_price_filter_functionality(self, p: Page, original_count):
        """测试价格过滤器功能 - 使用更稳定的选择器"""
        print("开始测试价格过滤器功能...")

        # 获取所有过滤器选项 - 使用更精确的选择器
        filter_container = p.get_by_test_id("wid-promotion-drawer-price-filters")
        filter_items = filter_container.locator("li").all()
        assert len(filter_items) > 0, "没有找到价格过滤器选项"

        print(f"找到 {len(filter_items)} 个价格过滤器选项")

        # 验证过滤器选项文本
        filter_texts = []
        for i, filter_item in enumerate(filter_items):
            filter_text = filter_item.text_content()
            filter_texts.append(filter_text)
            print(f"过滤器选项 {i + 1}: {filter_text}")

        # 验证默认选中状态（第一个选项应该是选中状态）
        first_filter = filter_items[0]
        first_filter_classes = first_filter.get_attribute("class")
        # 检查是否包含选中状态的样式类
        is_selected = ("bg-surface-200-fg-default" in first_filter_classes or
                      "cursor-default" in first_filter_classes or
                      "border-none" in first_filter_classes)
        assert is_selected, f"默认过滤器选项未选中，当前样式: {first_filter_classes}"

        # 测试点击第二个过滤器选项（如果存在）
        if len(filter_items) > 1:
            second_filter = filter_items[1]
            second_filter_text = second_filter.text_content()
            print(f"点击价格过滤器: {second_filter_text}")

            # 记录点击前的样式
            second_filter_classes_before = second_filter.get_attribute("class")

            # 点击过滤器
            second_filter.click()
            p.wait_for_timeout(3000)

            # 验证过滤器状态变化
            second_filter_classes_after = second_filter.get_attribute("class")
            print(f"过滤器样式变化: {second_filter_classes_before != second_filter_classes_after}")

            # 检查商品数量变化   
            products_after_filter = p.get_by_test_id("wid-product-card-container").all()
            count_after_filter = len(products_after_filter)

            print(f"过滤前商品数量: {original_count}, 过滤后商品数量: {count_after_filter}")

            # 验证过滤效果
            if count_after_filter != original_count:
                print("价格过滤器功能正常 - 商品数量发生变化")

                # 验证过滤后的商品价格是否符合过滤条件
                if "Under $5" in second_filter_text:
                    self._verify_filtered_prices(p, products_after_filter[:3], max_price=5.0)
                elif "$5 - $10" in second_filter_text:
                    self._verify_filtered_prices(p, products_after_filter[:3], min_price=5.0, max_price=10.0)
            else:
                print("价格过滤器点击后商品数量未变化")

            # 测试点击回"All"选项
            first_filter.click()
            p.wait_for_timeout(2000)

            products_back_to_all = p.get_by_test_id("wid-product-card-container").all()
            count_back_to_all = len(products_back_to_all)
            print(f"点击'All'后商品数量: {count_back_to_all}")

    def _verify_filtered_prices(self, p: Page, product_cards, min_price=None, max_price=None):
        """验证过滤后的商品价格是否符合条件 - 使用get_by_test_id优化"""
        print("验证过滤后的商品价格...")

        for index, card in enumerate(product_cards):
            # 使用get_by_test_id在card内查找价格元素
            price_element = card.get_by_test_id("wid-product-card-price-value")
            if price_element.count() > 0 and price_element.is_visible():
                price_text = price_element.text_content()
                # 提取价格数值
                try:
                    price_value = float(price_text.replace("$", ""))

                    if min_price is not None and price_value < min_price:
                        print(f"警告: 商品{index + 1}价格${price_value}低于过滤条件${min_price}")

                    if max_price is not None and price_value > max_price:
                        print(f"警告: 商品{index + 1}价格${price_value}高于过滤条件${max_price}")

                    print(f"商品{index + 1}价格验证: ${price_value}")
                except ValueError:
                    print(f"无法解析商品{index + 1}的价格: {price_text}")

    def _verify_promotion_progress(self, p: Page):
        """验证活动进度信息 - 使用get_by_test_id优化"""
        print("检查活动进度信息...")

        # 使用get_by_test_id查找进度信息
        progress_info = p.get_by_test_id("txt-promotion-drawer-processing-info")

        if progress_info.is_visible():
            print("发现活动进度信息")

            # 验证进度信息样式
            progress_classes = progress_info.get_attribute("class")
            assert "bg-surface-100-bg" in progress_classes, f"活动进度信息背景样式不正确: {progress_classes}"
            assert "fixed" in progress_classes, f"活动进度信息定位样式不正确: {progress_classes}"

            # 验证进度信息内容
            progress_text = progress_info.text_content()
            print(f"活动进度信息: {progress_text}")

            # 验证进度信息包含SVG图标
            progress_svg = progress_info.locator("svg")
            if progress_svg.is_visible():
                print("活动进度信息包含图标")

            print("活动进度信息验证完成")
        else:
            print("活动进度信息不存在，跳过验证")