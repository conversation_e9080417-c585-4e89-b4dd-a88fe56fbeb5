import allure
import pytest

from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_mkpl.mweb_page_mkpl_all_store.mweb_page_mkpl_all_store import MWebMkplAllStorePage
from src.common.commfunc import empty_cart
from src.config.weee.log_help import log


@allure.story("【H5-PDP】Global商品加购弹窗UI/UX验证")
class TestMWebPDPAddGlobalSkuPopUIUX:
    pytestmark = [pytest.mark.h5pdp, pytest.mark.mweb_regression, pytest.mark.transaction]

    @allure.title("【H5-PDP】Global商品加购弹窗UI/UX验证")
    def test_mweb_pdp_add_global_sku_pop_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        H5-PDP Global商品加购弹窗UI/UX验证
        测试步骤：
        1. 使用MkplAllStorePage进入All Stores页面
        2. 点击商品卡片进入PDP页面
        3. 验证PDP页面商品信息
        4. 第一次加购商品，验证首次加购弹窗
        5. 再次加购商品，验证Toast弹窗
        6. 验证弹窗内容和样式
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 使用MWebMkplAllStorePage进入All Stores页面
        all_store_page = MWebMkplAllStorePage(p, h5_autotest_header, c)

        print("All Stores页面加载完成")

        # 使用页面对象的方法点击商品进入PDP
        all_store_page._click_product_card()

        print("已通过页面对象方法点击商品，进入PDP页面")

        # 2. 等待PDP页面加载
        p.wait_for_load_state("networkidle", timeout=30000)
        p.wait_for_timeout(3000)

        # 3. 验证PDP页面基本信息
        self._verify_pdp_page_loaded(p)

        # 4. 第一次加购商品，验证首次加购弹窗
        self._test_first_add_to_cart_popup(p)

        # 5. 再次加购商品，验证Toast弹窗
        self._test_second_add_to_cart_toast(p)

        print("Global商品加购弹窗验证完成")

    def _verify_pdp_page_loaded(self, p: Page):
        """验证PDP页面加载完成"""
        print("验证PDP页面加载...")

        # 验证页面URL包含product
        current_url = p.url
        assert "/product/" in current_url, f"页面URL不正确: {current_url}"
        # 清空购物车
        try:
            commfunc().empty_cart()
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        # 关闭首页广告
        MwebPDPPage().close_advertisement_in_homepage()
        # 验证加购按钮存在
        add_cart_btn = p.get_by_test_id("btn-atc-plus")
        assert add_cart_btn.count() > 0, "PDP页面加购按钮不存在"

        # 如果有多个加购按钮，取第一个进行验证
        first_add_cart_btn = add_cart_btn.first
        assert first_add_cart_btn.is_visible(), "PDP页面加购按钮不可见"

        print("PDP页面基本信息验证完成")

    def _test_first_add_to_cart_popup(self, p: Page):
        """测试首次加购弹窗"""
        print("开始测试首次加购功能...")

        # 点击加购按钮
        add_cart_btn = p.get_by_test_id("btn-atc-plus")
        if add_cart_btn.count() > 1:
            # 如果有多个，点击第一个
            add_cart_btn.first.click()
        else:
            add_cart_btn.click()

        # 等待弹窗出现
        p.wait_for_timeout(2000)

        # 验证首次加购弹窗是否出现
        first_popup_appeared = self._check_first_add_cart_popup(p)

        if first_popup_appeared:
            print("✓ 首次加购弹窗出现，验证弹窗内容")
            self._verify_first_add_cart_popup_content(p)
        else:
            print("⚠ 未检测到首次加购弹窗，可能是再次加购或弹窗消失太快")

    def _check_first_add_cart_popup(self, p: Page):
        """检查首次加购弹窗是否出现"""
        try:
            # 检查首次加购弹窗容器
            first_popup = p.locator(".mkpl-first-add-cart-popup-content")
            if first_popup.count() > 0 and first_popup.is_visible():
                print("检测到首次加购弹窗")
                return True
        except:
            pass

        return False

    def _verify_first_add_cart_popup_content(self, p: Page):
        """验证首次加购弹窗内容"""
        print("验证首次加购弹窗内容...")

        # 验证关闭按钮
        close_btn = p.get_by_test_id("btn-close")
        if close_btn.count() > 0:
            assert close_btn.is_visible(), "首次加购弹窗关闭按钮不可见"
            print("✓ 关闭按钮验证完成")

        # 验证弹窗标题
        popup_title = p.get_by_test_id("wid-mkpl-add-cart-store-popup-title")
        if popup_title.count() > 0 and popup_title.is_visible():
            title_text = popup_title.text_content()
            print(f"✓ 弹窗标题: {title_text}")

            # 验证标题组成部分
            title_prefix = p.get_by_test_id("wid-mkpl-add-cart-store-popup-title-prefix")
            title_value = p.get_by_test_id("wid-mkpl-add-cart-store-popup-title-value")
            title_suffix = p.get_by_test_id("wid-mkpl-add-cart-store-popup-title-suffix")

            if title_prefix.count() > 0:
                prefix_text = title_prefix.text_content()
                assert "Added to" in prefix_text, f"标题前缀不正确: {prefix_text}"

            if title_value.count() > 0:
                value_text = title_value.text_content()
                assert value_text, "商家名称为空"
                print(f"✓ 商家名称: {value_text}")

            if title_suffix.count() > 0:
                suffix_text = title_suffix.text_content()
                assert "'s cart" in suffix_text, f"标题后缀不正确: {suffix_text}"

        # 验证进度条锁定图标
        lock_icon = p.get_by_test_id("wid-mkpl-add-cart-store-popup-progress-icon-lock")
        if lock_icon.count() > 0 and lock_icon.is_visible():
            print("✓ 进度条锁定图标验证完成")

        # 验证进度条包装器
        progress_wrapper = p.get_by_test_id("wid-mkpl-add-cart-store-popup-progress-wrapper")
        if progress_wrapper.count() > 0 and progress_wrapper.is_visible():
            print("✓ 进度条包装器验证完成")

            # 验证小计金额
            sub_total = p.get_by_test_id("wid-mkpl-add-cart-store-popup-progress-sub-total")
            if sub_total.count() > 0 and sub_total.is_visible():
                sub_total_text = sub_total.text_content()
                assert sub_total_text.startswith("$"), f"小计金额格式不正确: {sub_total_text}"
                print(f"✓ 小计金额: {sub_total_text}")

        # 验证前往商家按钮
        goto_seller_btn = p.get_by_test_id("btn-goto-seller")
        if goto_seller_btn.count() > 0 and goto_seller_btn.is_visible():
            btn_text = goto_seller_btn.text_content()
            assert "Shop" in btn_text, f"前往商家按钮文本不正确: {btn_text}"
            print(f"✓ 前往商家按钮: {btn_text}")

        # 验证商家信息
        seller_info = p.get_by_test_id("wid-mkpl-add-cart-store-popup-seller-info")
        if seller_info.count() > 0 and seller_info.is_visible():
            # 验证销量信息
            sales_volume = p.get_by_test_id("wid-mkpl-add-cart-store-popup-seller-info-sales-volume")
            if sales_volume.count() > 0 and sales_volume.is_visible():
                sales_text = sales_volume.text_content()
                print(f"✓ 销量信息: {sales_text}")

        print("首次加购弹窗内容验证完成")

    def _test_second_add_to_cart_toast(self, p: Page):
        """测试再次加购Toast弹窗"""
        print("开始测试再次加购功能...")

        # 等待首次弹窗消失或手动关闭
        p.wait_for_timeout(3000)

        # 尝试关闭首次弹窗（如果还存在）
        close_btn = p.get_by_test_id("btn-close")
        if close_btn.count() > 0 and close_btn.is_visible():
            close_btn.click()
            p.wait_for_timeout(1000)

        # 再次点击加购按钮
        add_cart_btn = p.get_by_test_id("btn-atc-plus")
        if add_cart_btn.count() > 1:
            add_cart_btn.first.click()
        else:
            add_cart_btn.click()

        # 等待Toast弹窗出现
        p.wait_for_timeout(2000)

        # 验证Toast弹窗
        toast_appeared = self._check_add_cart_toast(p)

        if toast_appeared:
            print("✓ 再次加购Toast弹窗出现，验证内容")
            self._verify_add_cart_toast_content(p)
        else:
            print("⚠ 未检测到再次加购Toast弹窗，可能弹窗消失太快")

    def _check_add_cart_toast(self, p: Page):
        """检查再次加购Toast弹窗是否出现"""
        try:
            # 检查Toast容器
            toast_container = p.locator("#showToast")
            if toast_container.count() > 0 and toast_container.is_visible():
                print("检测到再次加购Toast弹窗")
                return True
        except:
            pass

        return False

    def _verify_add_cart_toast_content(self, p: Page):
        """验证再次加购Toast弹窗内容"""
        print("验证再次加购Toast弹窗内容...")

        # 验证Toast主要内容
        toast_main = p.get_by_test_id("wid-mkpl-add-cart-store-popup-toast-main")
        if toast_main.count() > 0 and toast_main.is_visible():
            print("✓ Toast主要内容区域验证完成")

            # 验证商品图片
            toast_image = p.get_by_test_id("wid-mkpl-add-cart-store-popup-toast-main-image")
            if toast_image.count() > 0 and toast_image.is_visible():
                image_src = toast_image.get_attribute("src")
                image_alt = toast_image.get_attribute("alt")
                assert image_src and "weeecdn.net" in image_src, f"Toast图片src不正确: {image_src}"
                print(f"✓ Toast商品图片验证完成 - alt: {image_alt}")

            # 验证Toast内容
            toast_content = p.get_by_test_id("wid-mkpl-add-cart-store-popup-toast-main-content")
            if toast_content.count() > 0 and toast_content.is_visible():
                print("✓ Toast内容区域验证完成")

                # 验证标题组成部分
                title_prefix = p.get_by_test_id("wid-mkpl-add-cart-store-popup-toast-main-title-prefix")
                title_value = p.get_by_test_id("wid-mkpl-add-cart-store-popup-toast-main-title-value")
                title_suffix = p.get_by_test_id("wid-mkpl-add-cart-store-popup-toast-main-title-suffix")

                if title_prefix.count() > 0:
                    prefix_text = title_prefix.text_content()
                    assert "Added to" in prefix_text, f"Toast标题前缀不正确: {prefix_text}"

                if title_value.count() > 0:
                    value_text = title_value.text_content()
                    assert value_text, "Toast商家名称为空"
                    print(f"✓ Toast商家名称: {value_text}")

                if title_suffix.count() > 0:
                    suffix_text = title_suffix.text_content()
                    assert "'s cart" in suffix_text, f"Toast标题后缀不正确: {suffix_text}"

        print("再次加购Toast弹窗内容验证完成")