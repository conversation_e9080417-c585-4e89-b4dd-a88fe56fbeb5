{"name": "【H5-PDP】Global FBW配送信息模块UI/UX验证", "status": "passed", "description": "\n        H5 PDP Global FBW配送信息模块UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本加载\n        3. 验证Global FBW配送信息模块存在\n        4. 验证模块标题和Weee logo\n        5. 验证模块副标题内容\n        6. 验证模块样式和布局\n        ", "start": 1755769249810, "stop": 1755769266982, "uuid": "22973aa4-9e0e-40cd-b06a-741a5501e227", "historyId": "49dc7503ce94748b4f0d78ec4e0473fe", "testCaseId": "49dc7503ce94748b4f0d78ec4e0473fe", "fullName": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_100616_mweb_pdp_global_fbw_info_ui.TestMWebPDPGlobalFBWModuleUIUX#test_mweb_pdp_global_fbw_module_ui_ux", "labels": [{"name": "story", "value": "【H5-PDP】Global FBW配送信息模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp"}, {"name": "suite", "value": "test_100616_mweb_pdp_global_fbw_info_ui"}, {"name": "subSuite", "value": "TestMWebPDPGlobalFBWModuleUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "29832-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_100616_mweb_pdp_global_fbw_info_ui"}]}