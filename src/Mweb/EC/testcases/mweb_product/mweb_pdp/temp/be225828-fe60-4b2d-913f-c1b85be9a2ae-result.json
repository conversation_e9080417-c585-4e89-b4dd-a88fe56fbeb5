{"name": "[101480][mweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证", "status": "passed", "description": "\n        [101480][mweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 滚动到店铺推荐模块\n        4. 校验店铺推荐模块元素\n        5. 验证商品信息（名称、价格）\n        6. 测试收藏按钮功能\n        7. 测试加购按钮功能\n        ", "start": 1751012572250, "stop": 1751012599228, "uuid": "dd44d47b-088f-489e-b708-515bca51705c", "historyId": "01762f16d8876d5df35ed6e19a05d9d3", "testCaseId": "01762f16d8876d5df35ed6e19a05d9d3", "fullName": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101480_mweb_pdp_same_vendor_ui_ux.TestMWebPDPSameVendorUIUX#test_mweb_pdp_same_vendor_ui_ux", "labels": [{"name": "story", "value": "[101480][mweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp"}, {"name": "suite", "value": "test_101480_mweb_pdp_same_vendor_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPSameVendorUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "24688-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101480_mweb_pdp_same_vendor_ui_ux"}]}