{"name": "[101480][mweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'is_visible'", "trace": "self = <src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101480_mweb_pdp_same_vendor_ui_ux.TestMWebPDPSameVendorUIUX object at 0x000002C04BB05390>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc....28>>, 'page': <Page url='https://www.sayweee.com/en/product/SK-II-Skinpower-Airy-Milky-Lotion/2038708?joinEnki=true'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...x-WbthDzGXsrvwdZtDDrxz9RW-MigLv3GSj8Ic2UyPri5ZnGr4qN10p-abe1yNe5CzzK_UmWYzsM8JUGoTw30a8vTjWVFBBzYdMdy3nnYCVokIHM', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"[101480][mweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\")\n    def test_mweb_pdp_same_vendor_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        [101480][mweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 滚动到店铺推荐模块\n        4. 校验店铺推荐模块元素\n        5. 验证商品信息（名称、价格）\n        6. 测试收藏按钮功能\n        7. 测试加购按钮功能\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 直接进入指定pdp页面\n        pdp_page = MWebPDPPage(p, h5_autotest_header, browser_context=c,\n                               page_url=\"/product/SK-II-Skinpower-Airy-Milky-Lotion/2038708\")\n    \n        p.wait_for_timeout(5000)\n    \n        # 关闭可能的弹窗\n        if p.locator(\"//button[contains(text(), 'Continue')]\").all():\n            p.locator(\"//button[contains(text(), 'Continue')]\").click()\n    \n        # 2. 滚动到店铺推荐模块\n        scroll_one_page_until(p, mweb_pdp_ele.ele_pdp_same_vendor_card)\n    \n        # 3. 校验店铺推荐模块存在\n        assert pdp_page.FE.ele(mweb_pdp_ele.ele_pdp_same_vendor_card).is_visible(), \"店铺推荐模块不可见\"\n    \n        # 4. 校验店铺推荐标题\n>       assert pdp_page.FE.ele(mweb_pdp_ele.ele_pdp_same_vendor_title).is_visible(), \"店铺推荐标题不可见\"\nE       AttributeError: 'NoneType' object has no attribute 'is_visible'\n\ntest_101480_mweb_pdp_same_vendor_ui_ux.py:47: AttributeError"}, "description": "\n        [101480][mweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 滚动到店铺推荐模块\n        4. 校验店铺推荐模块元素\n        5. 验证商品信息（名称、价格）\n        6. 测试收藏按钮功能\n        7. 测试加购按钮功能\n        ", "start": 1751011490712, "stop": 1751011515778, "uuid": "21dfe965-ef59-4091-aab4-db20cb3ed927", "historyId": "01762f16d8876d5df35ed6e19a05d9d3", "testCaseId": "01762f16d8876d5df35ed6e19a05d9d3", "fullName": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101480_mweb_pdp_same_vendor_ui_ux.TestMWebPDPSameVendorUIUX#test_mweb_pdp_same_vendor_ui_ux", "labels": [{"name": "story", "value": "[101480][mweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp"}, {"name": "suite", "value": "test_101480_mweb_pdp_same_vendor_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPSameVendorUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "38492-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101480_mweb_pdp_same_vendor_ui_ux"}]}