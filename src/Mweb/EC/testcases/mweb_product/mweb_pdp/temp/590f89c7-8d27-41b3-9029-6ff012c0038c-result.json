{"name": "【H5-PDP】Global商品加购弹窗UI/UX验证", "status": "passed", "description": "\n        H5-PDP Global商品加购弹窗UI/UX验证\n        测试步骤：\n        1. 使用MkplAllStorePage进入All Stores页面\n        2. 点击商品卡片进入PDP页面\n        3. 验证PDP页面商品信息\n        4. 第一次加购商品，验证首次加购弹窗\n        5. 再次加购商品，验证Toast弹窗\n        6. 验证弹窗内容和样式\n        ", "start": 1757053989382, "stop": 1757054020374, "uuid": "861a1935-c415-4d81-b6d8-2bc902938ff1", "historyId": "83d3efe082e26edb3b38d97d3a174e61", "testCaseId": "83d3efe082e26edb3b38d97d3a174e61", "fullName": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101862_mweb_pdp_add_global_sku_pop_ui_ux.TestMWebPDPAddGlobalSkuPopUIUX#test_mweb_pdp_add_global_sku_pop_ui_ux", "labels": [{"name": "story", "value": "【H5-PDP】Global商品加购弹窗UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp"}, {"name": "suite", "value": "test_101862_mweb_pdp_add_global_sku_pop_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPAddGlobalSkuPopUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "32944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101862_mweb_pdp_add_global_sku_pop_ui_ux"}]}