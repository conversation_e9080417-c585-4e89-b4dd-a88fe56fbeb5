{"name": "【H5-PDP】Global商品加购弹窗UI/UX验证", "status": "broken", "statusDetails": {"message": "TypeError: argument of type 'NoneType' is not iterable", "trace": "self = <src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101862_mweb_pdp_add_global_sku_pop_ui_ux.TestMWebPDPAddGlobalSkuPopUIUX object at 0x0000018F08614310>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc....28>>, 'page': <Page url='https://www.sayweee.com/en/product/Motsunabe-Rakutenchi-Room-Temperature-Motsunabe/2905417'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...mpstI2h88V8Rmi28rrKXqyPnjoqWa331ylms2DzThaWMZTNykfzx7dJEco2hmyLFw5vy47SebxdLEhSv5gTRR_mTSCH4jTmMWRKFw8aGVzQo8rmg', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【H5-PDP】Global商品加购弹窗UI/UX验证\")\n    def test_mweb_pdp_add_global_sku_pop_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        H5-PDP Global商品加购弹窗UI/UX验证\n        测试步骤：\n        1. 使用MkplAllStorePage进入All Stores页面\n        2. 点击商品卡片进入PDP页面\n        3. 验证PDP页面商品信息\n        4. 第一次加购商品，验证首次加购弹窗\n        5. 再次加购商品，验证Toast弹窗\n        6. 验证弹窗内容和样式\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 使用MWebMkplAllStorePage进入All Stores页面\n        all_store_page = MWebMkplAllStorePage(p, h5_autotest_header, c)\n    \n        print(\"All Stores页面加载完成\")\n    \n        # 使用页面对象的方法点击商品进入PDP\n        all_store_page._click_product_card()\n    \n        print(\"已通过页面对象方法点击商品，进入PDP页面\")\n    \n        # 2. 等待PDP页面加载\n        p.wait_for_load_state(\"networkidle\", timeout=30000)\n        p.wait_for_timeout(3000)\n    \n        # 2.5. 关闭广告弹窗\n    \n>       pdp_page = MWebPDPPage(p, h5_autotest_header, c)\n\ntest_101862_mweb_pdp_add_global_sku_pop_ui_ux.py:53: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <src.Mweb.EC.mweb_pages.mweb_page_pdp.mweb_page_pdp.MWebPDPPage object at 0x0000018F08602310>\npage = <Page url='https://www.sayweee.com/en/product/Motsunabe-Rakutenchi-Room-Temperature-Motsunabe/2905417'>\nheader = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...mpstI2h88V8Rmi28rrKXqyPnjoqWa331ylms2DzThaWMZTNykfzx7dJEco2hmyLFw5vy47SebxdLEhSv5gTRR_mTSCH4jTmMWRKFw8aGVzQo8rmg', ...}\nbrowser_context = <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>\npage_url = None\n\n    def __init__(self, page: Page, header, browser_context, page_url: str = None):\n        super().__init__(page, header)\n        self.bc = browser_context\n        # 直接进入PDP页面 - 智能URL拼接，避免重复参数和编码问题\n>       if \"joinEnki=true\" in page_url:\nE       TypeError: argument of type 'NoneType' is not iterable\n\n..\\..\\..\\mweb_pages\\mweb_page_pdp\\mweb_page_pdp.py:16: TypeError"}, "description": "\n        H5-PDP Global商品加购弹窗UI/UX验证\n        测试步骤：\n        1. 使用MkplAllStorePage进入All Stores页面\n        2. 点击商品卡片进入PDP页面\n        3. 验证PDP页面商品信息\n        4. 第一次加购商品，验证首次加购弹窗\n        5. 再次加购商品，验证Toast弹窗\n        6. 验证弹窗内容和样式\n        ", "start": 1755853661084, "stop": 1755853681382, "uuid": "54244228-f2c5-4681-92fa-aac05c78de17", "historyId": "83d3efe082e26edb3b38d97d3a174e61", "testCaseId": "83d3efe082e26edb3b38d97d3a174e61", "fullName": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101862_mweb_pdp_add_global_sku_pop_ui_ux.TestMWebPDPAddGlobalSkuPopUIUX#test_mweb_pdp_add_global_sku_pop_ui_ux", "labels": [{"name": "story", "value": "【H5-PDP】Global商品加购弹窗UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp"}, {"name": "suite", "value": "test_101862_mweb_pdp_add_global_sku_pop_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPAddGlobalSkuPopUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "15376-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101862_mweb_pdp_add_global_sku_pop_ui_ux"}]}