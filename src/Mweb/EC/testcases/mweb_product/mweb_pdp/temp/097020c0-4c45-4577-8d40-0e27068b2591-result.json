{"name": "【H5-PDP】Global商品加购弹窗UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: PDP页面加购按钮不存在\nassert 0 > 0\n +  where 0 = <bound method Locator.count of <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Motsunabe-Rakutenchi-Room-Temperature-Motsunabe/2905417'> selector='internal:testid=[data-testid=\"btn-atc-plus\"s]'>>()\n +    where <bound method Locator.count of <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Motsunabe-Rakutenchi-Room-Temperature-Motsunabe/2905417'> selector='internal:testid=[data-testid=\"btn-atc-plus\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Motsunabe-Rakutenchi-Room-Temperature-Motsunabe/2905417'> selector='internal:testid=[data-testid=\"btn-atc-plus\"s]'>.count", "trace": "self = <src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101862_mweb_pdp_add_global_sku_pop_ui_ux.TestMWebPDPAddGlobalSkuPopUIUX object at 0x000001BEAD5E32D0>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc....28>>, 'page': <Page url='https://www.sayweee.com/en/product/Motsunabe-Rakutenchi-Room-Temperature-Motsunabe/2905417'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...IcjaXJ0cF_KA0uIj8rQCQb10kyk5Qej4yGXEmIZnFKZqC7FjlBnGhc9J_m4sLSKfA2XQy0g5HoWXucoo_LlUR4LhCQDId33DXTFqfeqbdK7yMwqc', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【H5-PDP】Global商品加购弹窗UI/UX验证\")\n    def test_mweb_pdp_add_global_sku_pop_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        H5-PDP Global商品加购弹窗UI/UX验证\n        测试步骤：\n        1. 使用MkplAllStorePage进入All Stores页面\n        2. 点击商品卡片进入PDP页面\n        3. 验证PDP页面商品信息\n        4. 第一次加购商品，验证首次加购弹窗\n        5. 再次加购商品，验证Toast弹窗\n        6. 验证弹窗内容和样式\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 使用MWebMkplAllStorePage进入All Stores页面\n        all_store_page = MWebMkplAllStorePage(p, h5_autotest_header, c)\n    \n        print(\"All Stores页面加载完成\")\n    \n        # 使用页面对象的方法点击商品进入PDP\n        all_store_page._click_product_card()\n    \n        print(\"已通过页面对象方法点击商品，进入PDP页面\")\n    \n        # 2. 等待PDP页面加载\n        p.wait_for_load_state(\"networkidle\", timeout=30000)\n        p.wait_for_timeout(3000)\n    \n        # 3. 验证PDP页面基本信息\n>       self._verify_pdp_page_loaded(p)\n\ntest_101862_mweb_pdp_add_global_sku_pop_ui_ux.py:42: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101862_mweb_pdp_add_global_sku_pop_ui_ux.TestMWebPDPAddGlobalSkuPopUIUX object at 0x000001BEAD5E32D0>\np = <Page url='https://www.sayweee.com/en/product/Motsunabe-Rakutenchi-Room-Temperature-Motsunabe/2905417'>\n\n    def _verify_pdp_page_loaded(self, p: Page):\n        \"\"\"验证PDP页面加载完成\"\"\"\n        print(\"验证PDP页面加载...\")\n    \n        # 验证页面URL包含product\n        current_url = p.url\n        assert \"/product/\" in current_url, f\"页面URL不正确: {current_url}\"\n    \n        # 验证加购按钮存在\n        add_cart_btn = p.get_by_test_id(\"btn-atc-plus\")\n>       assert add_cart_btn.count() > 0, \"PDP页面加购按钮不存在\"\nE       AssertionError: PDP页面加购按钮不存在\nE       assert 0 > 0\nE        +  where 0 = <bound method Locator.count of <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Motsunabe-Rakutenchi-Room-Temperature-Motsunabe/2905417'> selector='internal:testid=[data-testid=\"btn-atc-plus\"s]'>>()\nE        +    where <bound method Locator.count of <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Motsunabe-Rakutenchi-Room-Temperature-Motsunabe/2905417'> selector='internal:testid=[data-testid=\"btn-atc-plus\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/product/Motsunabe-Rakutenchi-Room-Temperature-Motsunabe/2905417'> selector='internal:testid=[data-testid=\"btn-atc-plus\"s]'>.count\n\ntest_101862_mweb_pdp_add_global_sku_pop_ui_ux.py:62: AssertionError"}, "description": "\n        H5-PDP Global商品加购弹窗UI/UX验证\n        测试步骤：\n        1. 使用MkplAllStorePage进入All Stores页面\n        2. 点击商品卡片进入PDP页面\n        3. 验证PDP页面商品信息\n        4. 第一次加购商品，验证首次加购弹窗\n        5. 再次加购商品，验证Toast弹窗\n        6. 验证弹窗内容和样式\n        ", "start": 1755851323107, "stop": 1755851344647, "uuid": "55dcb166-bfc7-4505-8701-3ea0bdd21704", "historyId": "83d3efe082e26edb3b38d97d3a174e61", "testCaseId": "83d3efe082e26edb3b38d97d3a174e61", "fullName": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101862_mweb_pdp_add_global_sku_pop_ui_ux.TestMWebPDPAddGlobalSkuPopUIUX#test_mweb_pdp_add_global_sku_pop_ui_ux", "labels": [{"name": "story", "value": "【H5-PDP】Global商品加购弹窗UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp"}, {"name": "suite", "value": "test_101862_mweb_pdp_add_global_sku_pop_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPAddGlobalSkuPopUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "33832-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101862_mweb_pdp_add_global_sku_pop_ui_ux"}]}