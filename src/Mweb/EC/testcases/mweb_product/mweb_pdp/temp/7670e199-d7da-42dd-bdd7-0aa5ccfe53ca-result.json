{"name": "【H5-PDP】Global商品加购弹窗UI/UX验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'MWebMkplAllStorePage' object has no attribute '_click_product_card'", "trace": "self = <src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101862_mweb_pdp_add_global_sku_pop_ui_ux.TestMWebPDPAddGlobalSkuPopUIUX object at 0x00000276267F8ED0>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc...version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/mkpl/global?mode=sub_page&hide_activity_pop=1'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...jGeFd8HZlCCy3angEOocQ1ZKMeUzezGMv9tRB6MCTuZ8Z_2_xMOf53id1OLUcxOjsIQ0RrIe3FDD3WoalWo9Zujin_vSI3aaWYoKEJQ5SfgV4yZA', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【H5-PDP】Global商品加购弹窗UI/UX验证\")\n    def test_mweb_pdp_add_global_sku_pop_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        H5-PDP Global商品加购弹窗UI/UX验证\n        测试步骤：\n        1. 使用MkplAllStorePage进入All Stores页面\n        2. 点击商品卡片进入PDP页面\n        3. 验证PDP页面商品信息\n        4. 第一次加购商品，验证首次加购弹窗\n        5. 再次加购商品，验证Toast弹窗\n        6. 验证弹窗内容和样式\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 进入All Stores页面并点击商品\n        all_store_page = MWebMkplAllStorePage(p, h5_autotest_header, c)\n    \n        # 使用页面对象的方法点击商品进入PDP\n>       all_store_page._click_product_card()\nE       AttributeError: 'MWebMkplAllStorePage' object has no attribute '_click_product_card'\n\ntest_101862_mweb_pdp_add_global_sku_pop_ui_ux.py:32: AttributeError"}, "description": "\n        H5-PDP Global商品加购弹窗UI/UX验证\n        测试步骤：\n        1. 使用MkplAllStorePage进入All Stores页面\n        2. 点击商品卡片进入PDP页面\n        3. 验证PDP页面商品信息\n        4. 第一次加购商品，验证首次加购弹窗\n        5. 再次加购商品，验证Toast弹窗\n        6. 验证弹窗内容和样式\n        ", "start": 1755850225715, "stop": 1755850235908, "uuid": "02a76b62-45c6-4c61-a88e-7170117824cb", "historyId": "83d3efe082e26edb3b38d97d3a174e61", "testCaseId": "83d3efe082e26edb3b38d97d3a174e61", "fullName": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101862_mweb_pdp_add_global_sku_pop_ui_ux.TestMWebPDPAddGlobalSkuPopUIUX#test_mweb_pdp_add_global_sku_pop_ui_ux", "labels": [{"name": "story", "value": "【H5-PDP】Global商品加购弹窗UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp"}, {"name": "suite", "value": "test_101862_mweb_pdp_add_global_sku_pop_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPAddGlobalSkuPopUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "27440-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101862_mweb_pdp_add_global_sku_pop_ui_ux"}]}