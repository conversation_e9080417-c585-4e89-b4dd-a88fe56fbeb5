{"name": "[101480][mweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'ElementHandle' object has no attribute 'swipe_by_ratio'", "trace": "self = <src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101480_mweb_pdp_same_vendor_ui_ux.TestMWebPDPSameVendorUIUX object at 0x000001A5CA3F20D0>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc....28>>, 'page': <Page url='https://www.sayweee.com/en/product/SK-II-Skinpower-Airy-Milky-Lotion/2038708?joinEnki=true'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...tQ7_t6ZGjqZefm5jv2jiPsJkx1NhJGFuvrEEm6ZEDXDXJWqFfrIfcrsHj85d5eFYOpioPut1IyYfreDUne7MbdNKpsE_ibEpmbUzK63JmNqw6OlY', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"[101480][mweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\")\n    def test_mweb_pdp_same_vendor_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        [101480][mweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 滚动到店铺推荐模块\n        4. 校验店铺推荐模块元素\n        5. 验证商品信息（名称、价格）\n        6. 测试收藏按钮功能\n        7. 测试加购按钮功能\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 直接进入指定pdp页面\n        pdp_page = MWebPDPPage(p, h5_autotest_header, browser_context=c,\n                               page_url=\"/product/SK-II-Skinpower-Airy-Milky-Lotion/2038708\")\n    \n        p.wait_for_timeout(5000)\n    \n        # 关闭可能的弹窗\n        if p.locator(\"//button[contains(text(), 'Continue')]\").all():\n            p.locator(\"//button[contains(text(), 'Continue')]\").click()\n    \n        # 2. 滚动到店铺推荐模块\n        scroll_one_page_until(p, mweb_pdp_ele.ele_pdp_same_vendor_card)\n    \n        # 3. 校验店铺推荐模块存在\n        assert pdp_page.FE.ele(mweb_pdp_ele.ele_pdp_same_vendor_card).is_visible(), \"店铺推荐模块不可见\"\n    \n        # 4. 校验店铺推荐标题\n        assert pdp_page.FE.ele(mweb_pdp_ele.ele_pdp_same_vendor_title).is_visible(), \"店铺推荐标题不可见\"\n    \n        # 5. 校验查看全部按钮\n        assert pdp_page.FE.ele(mweb_pdp_ele.ele_pdp_same_vendor_view_all).is_visible(), \"查看全部按钮不可见\"\n    \n        # 6. 获取店铺推荐商品列表\n        same_vendor_products = pdp_page.FE.eles(mweb_pdp_ele.ele_pdp_same_vendor_product_card)\n        assert len(same_vendor_products) > 0, \"店铺推荐商品列表为空\"\n    \n        # 7. 验证每个商品的基本信息\n        for index, product in enumerate(same_vendor_products[:3]):  # 只验证前3个商品\n            # 7.1 验证商品名称\n            product_name_ele = product.query_selector(\"div[data-testid='wid-product-card-title'] div\")\n            assert product_name_ele, f\"第{index + 1}个商品名称元素不存在\"\n            product_name = product_name_ele.text_content()\n            assert product_name, f\"第{index + 1}个商品名称为空\"\n    \n            # 7.2 验证商品价格\n            product_price_ele = product.query_selector(\"div[data-testid='wid-product-card-price'] div div\")\n            assert product_price_ele, f\"第{index + 1}个商品价格元素不存在\"\n            product_price = product_price_ele.text_content()\n            assert product_price and product_price.startswith(\n                \"$\"), f\"第{index + 1}个商品价格格式不正确: {product_price}\"\n    \n            # 7.3 验证商品图片\n            product_img = product.query_selector(\"img[data-role='product-image']\")\n            assert product_img, f\"第{index + 1}个商品图片不存在\"\n    \n            # 7.4 验证配送信息\n            ships_from = product.query_selector(\"div[data-testid='wid-product-card-ships-from']\")\n            assert ships_from, f\"第{index + 1}个商品配送信息不存在\"\n    \n            print(f\"商品{index + 1} - 名称: {product_name}, 价格: {product_price}\")\n    \n        # 8. 测试收藏按钮功能\n        first_product = same_vendor_products[0]\n        favorite_btn = first_product.query_selector(\"div[data-testid='btn-favorite']\")\n        assert favorite_btn, \"收藏按钮不存在\"\n        assert favorite_btn.is_visible(), \"收藏按钮不可见\"\n    \n        # 9. 测试加购按钮功能\n        atc_btn = first_product.query_selector(\"div[data-testid='btn-atc-plus']\")\n        assert atc_btn, \"加购按钮不存在\"\n    \n        # 点击加购按钮\n        atc_btn.click()\n        p.wait_for_timeout(2000)\n    \n        # 验证加购成功（可以通过购物车数量变化或其他方式验证）\n        print(\"店铺推荐商品加购测试完成\")\n    \n        # 10. 测试轮播功能\n        swiper_container = pdp_page.FE.ele(mweb_pdp_ele.ele_pdp_same_vendor_swiper)\n        assert swiper_container.is_visible(), \"轮播容器不可见\"\n    \n        # 验证轮播滑动（通过触摸滑动）\n        if len(same_vendor_products) > 1:\n            # 模拟向左滑动\n>           swiper_container.swipe_by_ratio(0.8, 0.5, 0.2, 0.5)\nE           AttributeError: 'ElementHandle' object has no attribute 'swipe_by_ratio'\n\ntest_101480_mweb_pdp_same_vendor_ui_ux.py:105: AttributeError"}, "description": "\n        [101480][mweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 滚动到店铺推荐模块\n        4. 校验店铺推荐模块元素\n        5. 验证商品信息（名称、价格）\n        6. 测试收藏按钮功能\n        7. 测试加购按钮功能\n        ", "start": 1751012178974, "stop": 1751012205135, "uuid": "be488850-24e3-45f9-b85e-285aa8a433b5", "historyId": "01762f16d8876d5df35ed6e19a05d9d3", "testCaseId": "01762f16d8876d5df35ed6e19a05d9d3", "fullName": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101480_mweb_pdp_same_vendor_ui_ux.TestMWebPDPSameVendorUIUX#test_mweb_pdp_same_vendor_ui_ux", "labels": [{"name": "story", "value": "[101480][mweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5pdp"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp"}, {"name": "suite", "value": "test_101480_mweb_pdp_same_vendor_ui_ux"}, {"name": "subSuite", "value": "TestMWebPDPSameVendorUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "15884-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_product.mweb_pdp.test_101480_mweb_pdp_same_vendor_ui_ux"}]}