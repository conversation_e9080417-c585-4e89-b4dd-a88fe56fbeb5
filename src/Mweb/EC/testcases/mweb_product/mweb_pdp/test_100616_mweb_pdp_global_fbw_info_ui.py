import allure
import pytest

from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_pdp.mweb_page_pdp import MWebPDPPage


@allure.story("[100616][mweb]Global FBW配送信息模块UI/UX验证")
class TestMWebPDPGlobalFBWModuleUIUX:
    pytestmark = [pytest.mark.h5pdp, pytest.mark.mweb_regression, pytest.mark.transaction]

    @allure.title("[100616][mweb]]Global FBW配送信息模块UI/UX验证")
    def test_100616_mweb_pdp_global_fbw_module_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        H5 PDP Global FBW配送信息模块UI/UX验证
        测试步骤：
        1. 访问指定商品PDP页面
        2. 校验页面基本加载
        3. 验证Global FBW配送信息模块存在
        4. 验证模块标题和Weee logo
        5. 验证模块副标题内容
        6. 验证模块样式和布局
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 进入指定PDP页面
        pdp_url = "/product/2X-Cooking-Iron-Spatula-Spoon-Long-Beech-Handle/2120534"
        pdp_page = MWebPDPPage(p, h5_autotest_header, browser_context=c, page_url=pdp_url)

        print("PDP页面加载完成")

        # 2. 等待页面完全加载
        p.wait_for_load_state("networkidle", timeout=30000)
        p.wait_for_timeout(3000)

        # 3. 验证Global FBW配送信息模块存在 - 使用get_by_test_id
        fbw_module = p.get_by_test_id("mod-pdp-fulfilled-by-weee")
        assert fbw_module.count() > 0, "Global FBW配送信息模块不存在"
        assert fbw_module.is_visible(), "Global FBW配送信息模块不可见"

        print("✓ Global FBW配送信息模块存在且可见")

        # 4. 验证模块标题和Weee logo
        self._verify_fbw_title_and_logo(p, fbw_module)

        # 5. 验证模块副标题
        self._verify_fbw_subtitle(p, fbw_module)

        # 6. 验证模块样式和布局
        self._verify_fbw_module_style(p, fbw_module)

        print("Global FBW配送信息模块验证完成")

    def _verify_fbw_title_and_logo(self, p: Page, fbw_module):
        """验证FBW模块标题和Weee logo"""
        print("验证FBW模块标题和logo...")

        # 验证标题元素
        fbw_title = p.get_by_test_id("mod-fulfilled-by-weee-title")
        assert fbw_title.count() > 0, "FBW标题元素不存在"
        assert fbw_title.is_visible(), "FBW标题不可见"

        # 验证标题文本内容
        title_text = fbw_title.text_content()
        assert title_text, "FBW标题文本为空"
        assert "Fulfilled by" in title_text, f"FBW标题内容不正确: {title_text}"

        print(f"✓ FBW标题验证完成: {title_text}")

        # 验证Weee logo
        weee_logo = fbw_title.locator("img")
        assert weee_logo.count() > 0, "Weee logo不存在"
        assert weee_logo.is_visible(), "Weee logo不可见"

        # 验证logo属性
        logo_src = weee_logo.get_attribute("src")
        logo_width = weee_logo.get_attribute("width")
        logo_height = weee_logo.get_attribute("height")

        assert logo_src, "Weee logo src属性为空"
        assert "logo.png" in logo_src, f"Weee logo路径不正确: {logo_src}"
        assert logo_width == "50", f"Weee logo宽度不正确: {logo_width}"
        assert logo_height == "15", f"Weee logo高度不正确: {logo_height}"

        print(f"✓ Weee logo验证完成 - src: {logo_src}, 尺寸: {logo_width}x{logo_height}")

    def _verify_fbw_subtitle(self, p: Page, fbw_module):
        """验证FBW模块副标题"""
        print("验证FBW模块副标题...")

        # 验证副标题元素
        fbw_subtitle = p.get_by_test_id("mod-fulfilled-by-weee-sub-title")
        assert fbw_subtitle.count() > 0, "FBW副标题元素不存在"
        assert fbw_subtitle.is_visible(), "FBW副标题不可见"

        # 验证副标题文本内容
        subtitle_text = fbw_subtitle.text_content()
        assert subtitle_text, "FBW副标题文本为空"

        # 验证副标题包含关键信息
        expected_keywords = ["Sold by", "Delivered with your groceries"]
        for keyword in expected_keywords:
            assert keyword in subtitle_text, f"FBW副标题缺少关键词'{keyword}': {subtitle_text}"

        print(f"✓ FBW副标题验证完成: {subtitle_text}")

        # 验证副标题样式
        subtitle_classes = fbw_subtitle.get_attribute("class")
        assert "enki-body-2xs" in subtitle_classes, f"FBW副标题样式不正确: {subtitle_classes}"
        assert "mt-0.5" in subtitle_classes, f"FBW副标题间距样式不正确: {subtitle_classes}"

        print("✓ FBW副标题样式验证完成")

    def _verify_fbw_module_style(self, p: Page, fbw_module):
        """验证FBW模块样式和布局"""
        print("验证FBW模块样式和布局...")

        # 验证模块容器样式
        module_classes = fbw_module.get_attribute("class")
        assert "px-5" in module_classes, f"FBW模块容器样式不正确: {module_classes}"

        # 验证内部布局容器
        layout_container = fbw_module.locator("div.flex.items-center.justify-between")
        assert layout_container.count() > 0, "FBW模块布局容器不存在"
        assert layout_container.is_visible(), "FBW模块布局容器不可见"

        # 验证布局容器样式
        layout_classes = layout_container.get_attribute("class")
        expected_layout_classes = [
            "flex", "items-center", "justify-between",
            "py-2.5", "border-t", "border-solid", "border-surface-200-bg"
        ]

        for expected_class in expected_layout_classes:
            assert expected_class in layout_classes, f"FBW模块布局缺少样式类'{expected_class}': {layout_classes}"

        print("✓ FBW模块布局样式验证完成")

        # 验证文本容器样式
        text_container = layout_container.locator("div.enki-body-sm-strong")
        if text_container.count() > 0:
            text_classes = text_container.get_attribute("class")
            expected_text_classes = ["enki-body-sm-strong", "text-surface-100-fg-minor", "flex-1"]

            for expected_class in expected_text_classes:
                assert expected_class in text_classes, f"FBW文本容器缺少样式类'{expected_class}': {text_classes}"

            print("✓ FBW文本容器样式验证完成")

        print("✓ FBW模块整体样式验证完成")