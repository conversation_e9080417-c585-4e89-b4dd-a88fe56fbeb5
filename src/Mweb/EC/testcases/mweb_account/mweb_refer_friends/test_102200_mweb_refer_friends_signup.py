import allure
import pytest
from playwright.sync_api import Page, TimeoutError
from src.Mweb.EC.mweb_pages.mweb_account_page.mweb_page_refer_friends.mweb_page_refer_friends_signup import MWebReferFriendsSignupPage
from src.Mweb.EC.mweb_ele.mweb_account.mweb_refer_friends.mweb_refer_friends_signup_ele import *
from src.config.weee.log_help import log
from src.config.base_config import TEST_URL

@allure.story("推荐朋友注册功能")
class TestMWebReferFriendsSignup:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.transaction]

    @allure.title("推荐朋友注册功能验证")
    def test_102200_mweb_refer_friends_signup(self, not_login_phone_page: dict, h5_autotest_header):
        """
        测试推荐朋友注册功能
        步骤：
        1. 初始化邀请好友落地页
        2. 更新邮编为95008
        3. 点击领取优惠券按钮
        4. 进入邀请好友输入邮箱页面，输入动态邮箱
        5. 点击Next按钮完成注册
        6. 初始化coupon portal页面，验证优惠券标题
        """
        p: Page = not_login_phone_page.get("page")
        c = not_login_phone_page.get("context")
        landing_url = "account/referral/landing?t=1&referral_id=7216895&lang=zh&utm_source=copyLink"

        try:
            # 1. 初始化邀请好友落地页
            refer_friends_page = MWebReferFriendsSignupPage(p, h5_autotest_header, browser_context=c, page_url=landing_url)
            log.info("成功初始化邀请好友落地页")
            p.wait_for_timeout(2000)

            # 2. 更新邮编为95008 - 使用XPath选择器
            p.wait_for_selector(ele_zipcode_input, state="visible", timeout=10000)
            refer_friends_page.fill_zipcode_95008()
            log.info("成功更新邮编为95008")
            p.wait_for_timeout(2000)

            # 3. 点击领取优惠券按钮 - 使用XPath选择器
            p.wait_for_selector(ele_redeem_offer_button, state="visible", timeout=10000)
            refer_friends_page.click_redeem_offer_button()
            log.info("成功点击领取优惠券按钮")
            p.wait_for_timeout(2000)
            # 4. 进入邀请好友输入邮箱页面，输入动态邮箱
            # 这里其实有一些重复的日志和注释，比如“进入邀请好友输入邮箱页面，输入动态邮箱”这句话和log.info日志都出现了两次。
            # 实际上，我们只需要等待邮箱输入框出现，然后调用页面对象的方法即可，相关的日志和细节已经在input_dynamic_email方法内部做了详细处理。
            # 所以可以简化为如下：

            p.wait_for_selector(ele_email_input, state="visible", timeout=10000)
            dynamic_email = refer_friends_page.input_dynamic_email()
            p.wait_for_timeout(2000)

            # 5. 点击Next按钮完成注册
            p.wait_for_selector(ele_next_button, state="visible", timeout=10000)
            refer_friends_page.click_next_button()
            log.info("成功点击Next按钮，注册完成")
            p.wait_for_timeout(3000)  # 等待注册完成，页面跳转

            # 6. 初始化coupon portal页面，验证优惠券标题
            coupon_portal_url = "account/my-coupons"
            refer_friends_page.page.goto(f"{TEST_URL}/{coupon_portal_url}")
            refer_friends_page.page.wait_for_load_state("load")
            log.info(f"成功进入coupon portal页面: {coupon_portal_url}")
            p.wait_for_timeout(2000)

            # 验证优惠券标题文案
            p.wait_for_selector(ele_coupon_title, state="visible", timeout=10000)
            coupon_title = refer_friends_page.get_coupon_title()
            log.info(f"获取到优惠券标题: {coupon_title}")

            # 断言优惠券标题是否符合预期
            expected_title = "$10 off $35 \n New User Coupon"
            assert coupon_title.strip() == expected_title.strip(), f"优惠券标题不匹配，期望: '{expected_title}'，实际: '{coupon_title}'"
            log.info("优惠券标题验证成功，符合预期")

            log.info("推荐朋友注册功能测试完成")

        except Exception as e:
            log.error(f"测试过程中发生异常: {str(e)}")
            try:
                log.info(f"当前页面URL: {p.url}")
                log.info(f"页面标题: {p.title()}")
                import time
                screenshot_path = f"error_screenshot_{int(time.time())}.png"
                p.screenshot(path=screenshot_path)
                log.info(f"错误截图已保存: {screenshot_path}")
            except Exception as debug_e:
                log.error(f"保存调试信息时出错: {str(debug_e)}")
            raise
