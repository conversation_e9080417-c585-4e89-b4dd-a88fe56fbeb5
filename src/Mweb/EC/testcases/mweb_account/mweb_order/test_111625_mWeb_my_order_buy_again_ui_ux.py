import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_order_page.mweb_order_page import MWebOrderPage
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log


@allure.story("【111625】 订单列表-再来一单功能验证")
class TestMWebMyOrderBuyAgainUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【111625】 订单列表-再来一单功能验证")
    def test_111625_mWeb_my_order_buy_again_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【111625】 订单列表-再来一单功能验证
        测试步骤：
        1、进入订单列表页面，切换到已取消tab下
        2、点击全部订单，找到订单列表下再来一单按钮，进入商品选择页面
        3、商品选择页面，默认勾选全选按钮，勾选掉某个商品，全选勾掉，点击全选商品全部选中，这里如果选中几个商品，加入购物车按钮上就会显示几个
        4、点击加入购物车按钮，自动回到订单列表页面，并弹出toast，点击查看，进入购物车页面
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        
        # 1. 进入订单列表页面
        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url="/order/list")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")
        
        # 切换到已取消tab
        canceled_tab = p.get_by_test_id("order-tab-canceled")
        assert canceled_tab.is_visible(), "未找到已取消tab"
        canceled_tab.click()
        p.wait_for_timeout(2000)
        log.info("成功切换到已取消tab")
        
        # 2. 点击全部订单，找到订单列表下再来一单按钮
        # 先切换回全部订单tab
        all_orders_tab = p.get_by_test_id("order-tab-all")
        assert all_orders_tab.is_visible(), "未找到全部订单tab"
        all_orders_tab.click()
        p.wait_for_timeout(2000)
        log.info("成功切换到全部订单tab")
        
        # 查找订单列表中的再来一单按钮
        buy_again_buttons = p.get_by_test_id("btn-buy-again").all()
        assert len(buy_again_buttons) > 0, "订单列表中未找到再来一单按钮"
        
        # 点击第一个再来一单按钮
        buy_again_buttons[0].click()
        p.wait_for_timeout(3000)
        log.info("点击再来一单按钮")
        
        # 验证进入商品选择页面
        select_product_page = p.get_by_test_id("buy-again-product-select")
        assert select_product_page.is_visible(), "未成功进入商品选择页面"
        log.info("成功进入商品选择页面")
        
        # 3. 商品选择页面操作
        # 验证默认勾选全选按钮
        select_all_checkbox = p.get_by_test_id("select-all-checkbox")
        assert select_all_checkbox.is_checked(), "全选按钮默认未被勾选"
        log.info("验证全选按钮默认被勾选")
        
        # 获取所有商品的复选框
        product_checkboxes = p.get_by_test_id("product-checkbox").all()
        assert len(product_checkboxes) > 0, "商品选择页面没有商品"
        log.info(f"商品选择页面有{len(product_checkboxes)}个商品")
        
        # 取消勾选第一个商品
        if len(product_checkboxes) > 0:
            product_checkboxes[0].uncheck()
            p.wait_for_timeout(1000)
            log.info("取消勾选第一个商品")
            
            # 验证全选按钮被取消勾选
            assert not select_all_checkbox.is_checked(), "取消勾选商品后，全选按钮仍被勾选"
            log.info("验证取消勾选商品后，全选按钮也被取消勾选")
        
        # 点击全选按钮，选中所有商品
        select_all_checkbox.check()
        p.wait_for_timeout(1000)
        log.info("点击全选按钮，选中所有商品")
        
        # 验证所有商品都被选中
        for i, checkbox in enumerate(product_checkboxes):
            assert checkbox.is_checked(), f"第{i+1}个商品未被选中"
        log.info("验证所有商品都被选中")
        
        # 获取加入购物车按钮上显示的商品数量
        add_to_cart_btn = p.get_by_test_id("btn-add-to-cart")
        assert add_to_cart_btn.is_visible(), "未找到加入购物车按钮"
        
        # 获取按钮上显示的商品数量
        btn_text = add_to_cart_btn.text_content()
        log.info(f"加入购物车按钮文本: {btn_text}")
        
        # 验证按钮上显示的商品数量与选中的商品数量一致
        assert str(len(product_checkboxes)) in btn_text, "加入购物车按钮上显示的商品数量与选中的商品数量不一致"
        log.info("验证加入购物车按钮上显示的商品数量正确")
        
        # 4. 点击加入购物车按钮
        add_to_cart_btn.click()
        p.wait_for_timeout(3000)
        log.info("点击加入购物车按钮")
        
        # 验证自动回到订单列表页面
        order_list_page = p.get_by_test_id("order-list-page")
        assert order_list_page.is_visible(), "未自动回到订单列表页面"
        log.info("成功回到订单列表页面")
        
        # 验证弹出toast
        toast = p.get_by_test_id("toast-message")
        assert toast.is_visible(), "未弹出toast提示"
        log.info(f"成功弹出toast: {toast.text_content()}")
        
        # 点击查看，进入购物车页面
        view_cart_btn = p.get_by_test_id("btn-view-cart")
        if view_cart_btn.is_visible():
            view_cart_btn.click()
            p.wait_for_timeout(3000)
            log.info("点击查看按钮")
            
            # 验证进入购物车页面
            cart_page = p.get_by_test_id("cart-page")
            assert cart_page.is_visible(), "未成功进入购物车页面"
            log.info("成功进入购物车页面")
        else:
            log.warning("未找到查看按钮，可能toast已消失")
