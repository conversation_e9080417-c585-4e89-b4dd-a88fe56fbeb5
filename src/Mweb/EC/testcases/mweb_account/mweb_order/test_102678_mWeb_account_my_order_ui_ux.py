
import allure
import pytest

from playwright.sync_api import Page
from src.Mweb.EC.mweb_ele.mweb_account import mweb_account_ele
from src.Mweb.EC.mweb_pages.mweb_order_page.mweb_order_page import MWebOrderPage
from src.config.weee.log_help import log


@allure.story("【102678】 Mobile-Account 页面-我的订单模块UI/UX验证")
class TestMWebMyCanceledOrderUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("【102678】 Mobile-Account 页面-我的订单模块UI/UX验证")
    def test_102678_mWeb_account_my_order_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【102678】 Mobile-Account 页面-我的订单模块UI/UX验证
        测试步骤：
        1、me页面的我的订单模块下面展示:待付款、待发货、已发货、待晒单、退换/售后
        2、点击我的订单>,进入订单列表:默认选中“全部订单”tab
        3、点击待付款:进入我的订单页面，待付款tab下
        4、点击待发货:进入我的订单页面，待发货tab下
        5、点击已发货：进入我的订单页面，已发货tab下
        6、点击待晒单：进入我的订单页面，待晒单tab下
        7、点击退换/售后：进入我的订单页面，退换/售后tab下

        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 进入me页面，验证我的订单模块展示
        log.info("步骤1：进入me页面，验证我的订单模块展示")
        account_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url="/account")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")
        # 3. 滚动到我的订单模块
        account_page.scroll_to_pos(mweb_account_ele.my_orders_ele)
        log.info("滚动到我的订单模块")
        p.wait_for_timeout(2000)
        # 断言 > 存在
        assert p.get_by_test_id("wid-order-arrow-right").is_visible()
        # 断言 我的订单里 icon、title 都存在
        assert p.get_by_test_id("wid-order-pending-image").is_visible()
        assert p.get_by_test_id("wid-order-pending-title").is_visible()
        # 断言如果存在数字球，大于99 显示99+
        if (p.get_by_test_id("wid-order-pending-value").is_visible()
                and int(p.get_by_test_id("wid-order-pending-value").text_content()) > 99):
            assert p.get_by_test_id("wid-order-pending-value").text_content() == "99+"

        assert p.get_by_test_id("wid-order-unshipped-image").is_visible()
        assert p.get_by_test_id("wid-order-unshipped-title  ").is_visible()
        # 断言如果存在数字球，大于99 显示99+
        if (p.get_by_test_id("wid-order-unshipped-value").is_visible()
                and int(p.get_by_test_id("wid-order-unshipped-value").text_content()) > 99):
            assert p.get_by_test_id("wid-order-unshipped-value").text_content() == "99+"

        assert p.get_by_test_id("wid-order-shipped-image").is_visible()
        assert p.get_by_test_id("wid-order-shipped-title").is_visible()
        # 断言如果存在数字球，大于99 显示99+
        if (p.get_by_test_id("wid-order-shipped-value").is_visible()
                and int(p.get_by_test_id("wid-order-shipped-value").text_content()) > 99):
            assert p.get_by_test_id("wid-order-shipped-value").text_content() == "99+"


        assert p.get_by_test_id("wid-order-to_review-image").is_visible()
        assert p.get_by_test_id("wid-order-to_review-title").is_visible()
        # 断言如果存在数字球，大于99 显示99+
        if (p.get_by_test_id("wid-order-to_review-value").is_visible()
                and int(p.get_by_test_id("wid-order-to_review-value").text_content()) > 99):
            assert p.get_by_test_id("wid-order-to_review-value").text_content() == "99+"


        assert p.get_by_test_id("wid-order-returns-image").is_visible()
        assert p.get_by_test_id("wid-order-returns-title").is_visible()
        # 断言如果存在数字球，大于99 显示99+
        if (p.get_by_test_id("wid-order-returns-value").is_visible()
                and int(p.get_by_test_id("wid-order-returns-value").text_content()) > 99):
            assert p.get_by_test_id("wid-order-returns-value").text_content() == "99+"

        # 断言如果存在数字球，大于99 显示99+
        if (p.get_by_test_id("wid-order-to_review-value").is_visible()
                and int(p.get_by_test_id("wid-order-to_review-value").text_content()) > 99):
            assert p.get_by_test_id("wid-order-to_review-value").text_content() == "99+"

        # 点击我的订单> 入口
        my_orders = p.get_by_test_id(mweb_account_ele.my_orders_link_ele)
        assert my_orders.is_visible(), "未找到我的订单> 入口"
        # 切换到全部订单tab
        my_orders.click()
        p.wait_for_timeout(2000)
        # 验证已切换到全部订单tab
        print(p.url)
        assert "/order/list" in p.url, "未切换到全部订单tab"

        # 待付款tab
        pending_tab = p.get_by_test_id(mweb_account_ele.my_order_pending_tab_ele)
        assert pending_tab.is_visible(), "未找到待付款tab"
        # 切换到待付款tab
        pending_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到待付款tab
        print(p.url)
        assert "/order/list?filter_status=1" in p.url, "未切换到待付款tab"
        p.go_back()
        p.wait_for_timeout(2000)
        # 3. 滚动到我的订单模块
        account_page.scroll_to_pos(mweb_account_ele.my_orders_ele)
        log.info("滚动到我的订单模块")
        p.wait_for_timeout(2000)
        # 切换到待发货tab
        unshipped_tab = p.get_by_test_id(mweb_account_ele.my_order_unshipped_tab_ele)
        assert unshipped_tab.is_visible(), "未找到待发货tab"
        # 切换到待发货tab
        unshipped_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到待发货tab
        print(p.url)
        assert "/order/list?filter_status=2" in p.url, "未切换到待发货tab"
        p.go_back()
        p.wait_for_timeout(2000)
        # 3. 滚动到我的订单模块
        account_page.scroll_to_pos(mweb_account_ele.my_orders_ele)
        log.info("滚动到我的订单模块")
        p.wait_for_timeout(2000)
        # 切换到已发货tab
        shipped_tab = p.get_by_test_id(mweb_account_ele.my_order_shipped_tab_ele)
        assert shipped_tab.is_visible(), "未找到已发货tab"
        # 切换到已发货tab
        shipped_tab.click()
        p.wait_for_timeout(2000)
        assert "/order/list?filter_status=3" in p.url, "未切换到已发货tab"
        p.go_back()
        p.wait_for_timeout(2000)
        # 3. 滚动到我的订单模块
        account_page.scroll_to_pos(mweb_account_ele.my_orders_ele)
        log.info("滚动到我的订单模块")
        p.wait_for_timeout(2000)
        # 切换到待晒单tab
        reviewed_tab = p.get_by_test_id(mweb_account_ele.my_order_review_tab_ele)
        assert reviewed_tab.is_visible(), "未找到待晒单tab"
        # 切换到待晒单tab
        reviewed_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到待晒单tab
        assert "/order/list?filter_status=6" in p.url, "未切换到待晒单tab"
        p.go_back()
        p.wait_for_timeout(2000)
        # 3. 滚动到我的订单模块
        account_page.scroll_to_pos(mweb_account_ele.my_orders_ele)
        log.info("滚动到我的订单模块")
        p.wait_for_timeout(2000)

        # 3. 滚动到我的订单模块
        account_page.scroll_to_pos(mweb_account_ele.my_orders_ele)
        log.info("滚动到我的订单模块")
        p.wait_for_timeout(2000)
        # 切换到Returns tab
        canceled_tab = p.get_by_test_id(mweb_account_ele.my_order_returns_tab_ele)
        assert canceled_tab.is_visible(), "未找到Returns tab"
        # 切换到Returns tab
        canceled_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到Returns tab
        assert "/order/case/list?ws=me_page" in p.url, "未切换到Returns tab"


