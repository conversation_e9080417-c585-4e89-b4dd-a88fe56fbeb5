
import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_order_page.mweb_order_page import MWebOrderPage
from src.config.weee.log_help import log


@allure.story("【108220】 订单列表已发货 tab订单流程验证")
class TestMWebMyShippedOrderUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("【108220】 订单列表已发货 tab订单流程验证")
    def test_108220_mWeb_my_shipped_order_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【108220】 订单列表已发货 tab订单流程验证
        测试步骤：
        1、进入订单列表页面，点击切换到已发货tab下
        2、如果存在订单，订单上会存在各种按钮
        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面
        4、点击Refund details 按钮，进入售后退款详情页面，再返回已送达页面
        5、点击Track items 按钮，弹出物流pop，点击conform 按钮，回到已送达页面
        6、点击reorder 按钮 ，拉起再来一单选择商品页面，再返回已送达页面
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 进入订单列表页面
        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url="/order/list")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")
        
        # 切换到已发货tab
        shipped_tab = p.get_by_test_id("order-tab-shipped")
        assert shipped_tab.is_visible(), "未找到已发货tab"
        shipped_tab.click()
        p.wait_for_timeout(2000)
        log.info("成功切换到已发货tab")
        
        # 验证已切换到已发货tab
        assert shipped_tab.get_attribute("aria-selected") == "true", "未成功切换到已发货tab"
        
        # 2. 检查是否存在订单
        order_items = p.get_by_test_id("order-item").all()
        if len(order_items) == 0:
            log.warning("已发货tab下没有订单，无法继续测试")
            pytest.skip("已发货tab下没有订单，跳过测试")
        
        log.info(f"已发货tab下有{len(order_items)}个订单")
        
        # 选择第一个订单进行操作
        first_order = order_items[0]
        
        # 3. 点击 Return/Refund 按钮
        return_refund_btn = first_order.get_by_test_id("btn-return-refund")
        if return_refund_btn.is_visible():
            log.info("找到 Return/Refund 按钮")
            return_refund_btn.click()
            p.wait_for_timeout(3000)
            
            # 验证进入售后退款页面
            refund_page = p.get_by_test_id("refund-page")
            assert refund_page.is_visible(), "未成功进入售后退款页面"
            log.info("成功进入售后退款页面")
            
            # 返回已送达页面
            back_btn = p.get_by_test_id("btn-back")
            assert back_btn.is_visible(), "未找到返回按钮"
            back_btn.click()
            p.wait_for_timeout(2000)
            log.info("返回已送达页面")
            
            # 验证已返回订单列表页面
            assert p.get_by_test_id("order-list-page").is_visible(), "未成功返回订单列表页面"
        else:
            log.info("当前订单没有 Return/Refund 按钮")
        
        # 4. 点击 Refund details 按钮
        refund_details_btn = first_order.get_by_test_id("btn-refund-details")
        if refund_details_btn.is_visible():
            log.info("找到 Refund details 按钮")
            refund_details_btn.click()
            p.wait_for_timeout(3000)
            
            # 验证进入售后退款详情页面
            refund_details_page = p.get_by_test_id("refund-details-page")
            assert refund_details_page.is_visible(), "未成功进入售后退款详情页面"
            log.info("成功进入售后退款详情页面")
            
            # 返回已送达页面
            back_btn = p.get_by_test_id("btn-back")
            assert back_btn.is_visible(), "未找到返回按钮"
            back_btn.click()
            p.wait_for_timeout(2000)
            log.info("返回已送达页面")
            
            # 验证已返回订单列表页面
            assert p.get_by_test_id("order-list-page").is_visible(), "未成功返回订单列表页面"
        else:
            log.info("当前订单没有 Refund details 按钮")
        
        # 5. 点击 Track items 按钮
        track_items_btn = first_order.get_by_test_id("btn-track-items")
        if track_items_btn.is_visible():
            log.info("找到 Track items 按钮")
            track_items_btn.click()
            p.wait_for_timeout(3000)
            
            # 验证弹出物流pop
            tracking_popup = p.get_by_test_id("tracking-popup")
            assert tracking_popup.is_visible(), "未成功弹出物流pop"
            log.info("成功弹出物流pop")
            
            # 点击 Confirm 按钮
            confirm_btn = p.get_by_test_id("btn-confirm")
            assert confirm_btn.is_visible(), "未找到 Confirm 按钮"
            confirm_btn.click()
            p.wait_for_timeout(2000)
            log.info("点击 Confirm 按钮")
            
            # 验证已关闭物流pop
            assert not tracking_popup.is_visible(), "物流pop未成功关闭"
            log.info("物流pop已关闭")
        else:
            log.info("当前订单没有 Track items 按钮")
        
        # 6. 点击 Reorder 按钮
        reorder_btn = first_order.get_by_test_id("btn-reorder")
        if reorder_btn.is_visible():
            log.info("找到 Reorder 按钮")
            reorder_btn.click()
            p.wait_for_timeout(3000)
            
            # 验证拉起再来一单选择商品页面
            buy_again_page = p.get_by_test_id("buy-again-product-select")
            assert buy_again_page.is_visible(), "未成功拉起再来一单选择商品页面"
            log.info("成功拉起再来一单选择商品页面")
            
            # 返回已送达页面
            back_btn = p.get_by_test_id("btn-back")
            assert back_btn.is_visible(), "未找到返回按钮"
            back_btn.click()
            p.wait_for_timeout(2000)
            log.info("返回已送达页面")
            
            # 验证已返回订单列表页面
            assert p.get_by_test_id("order-list-page").is_visible(), "未成功返回订单列表页面"
        else:
            log.info("当前订单没有 Reorder 按钮")
        
        log.info("订单列表已发货 tab订单流程验证完成")
