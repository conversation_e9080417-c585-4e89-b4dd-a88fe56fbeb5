import allure
import pytest
from playwright.sync_api import Page, TimeoutError
from src.Mweb.EC.mweb_pages.mweb_account_page.mweb_page_points.mweb_page_points_portal import MWebPointsPortalPage
from src.Mweb.EC.mweb_ele.mweb_rewards.mweb_rewards_ele import ele_rewards_upgrade_btn
from src.Mweb.EC.mweb_ele.mweb_account.mweb_points.mweb_points_portal_ele import ele_points_portal_bronze_upgrade_btn
from src.config.weee.log_help import log

@allure.story("青铜用户购买无法购买普通积分，只能购买升级积分")
class TestMWebBronzeBuyPoints:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.transaction]

    @allure.title("青铜用户点击Load points to upgrade按钮后弹窗校验")
    def test_103420_mweb_bronze_buy_points(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        测试青铜用户在积分portal页面点击“Load points to upgrade”按钮后，弹窗内元素校验
        步骤：
        1. 初始化portal points页面
        2. 点击points portal页面的load points按钮
        3. 跳转到/account/rewards/level?，弹出升级弹窗
        4. 断言弹窗内元素ele_rewards_upgrade_btn存在
        """
        # 1. 获取Playwright页面对象和上下文
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        points_portal_url = "account/perks?ws=me_page"

        try:
            # 2. 初始化portal points页面
            points_portal_page = MWebPointsPortalPage(p, h5_autotest_header, browser_context=c, page_url=points_portal_url)
            log.info("成功初始化积分portal页面")
            p.wait_for_timeout(2000)  # 等待页面稳定

            # 3. 点击load points按钮
            points_portal_page.click_upgrade_points()
            log.info("成功点击Load points to upgrade按钮")
            p.wait_for_timeout(2000)  # 等待页面跳转和弹窗出现

            # 4. 断言弹窗内元素ele_rewards_upgrade_btn存在
            try:
                # 等待弹窗内的升级按钮出现
                p.wait_for_selector(ele_rewards_upgrade_btn, state="visible", timeout=10000)
                log.info("弹窗内升级按钮存在，校验通过")
                assert True, "弹窗内升级按钮存在"
            except TimeoutError:
                log.error("弹窗内未找到升级按钮")
                assert False, "弹窗内未找到升级按钮"

            log.info("青铜用户购买无法购买普通积分，只能购买升级积分测试完成")

        except Exception as e:
            log.error(f"测试过程中发生异常: {str(e)}")
            try:
                log.info(f"当前页面URL: {p.url}")
                log.info(f"页面标题: {p.title()}")
                import time
                screenshot_path = f"error_screenshot_{int(time.time())}.png"
                p.screenshot(path=screenshot_path)
                log.info(f"错误截图已保存: {screenshot_path}")
            except Exception as debug_e:
                log.error(f"保存调试信息时出错: {str(debug_e)}")
            raise