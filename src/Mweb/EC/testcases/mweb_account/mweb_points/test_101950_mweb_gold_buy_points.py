"""
<AUTHOR>  Assistant
@Version        :  V1.0.0
------------------------------------
@File           :  test_101950_mweb_gold_buy_points.py
@Description    :  MWeb Gold积分购买测试用例
@CreateTime     :  2025/08/12
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/08/12
"""
import allure
import pytest
from playwright.sync_api import Page, TimeoutError
from src.Mweb.EC.mweb_pages.mweb_account_page.mweb_page_points.mweb_page_gold_poitns_buy import MWebGoldPointsBuyPage
from src.config.weee.log_help import log

@allure.story("积分购买流程")
class TestMWebGoldBuyPoints:
    pytestmark = [pytest.mark.mweb_gold_points, pytest.mark.transaction]

    @allure.title("积分购买流程验证")
    def test_101950_mweb_gold_buy_points_flow(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        测试MWeb Gold积分购买的完整流程
        步骤：
        1. 初始化页面：/account/perks?ws=me_page
        2. 点击 Add points 进入了points 商品列表页面
        3. 点击 Checkout, 进入了checkout 页面
        4. 选择 Payment Method
        5. 选择 paypal 支付方式
        6. 点击 Confirm payment method 自己回到了checkout 页面
        7. 点击 Place order
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        points_url = "/account/perks?ws=me_page"

        try:
            # 1. 初始化积分portal页面
            points_page = MWebGoldPointsBuyPage(p, h5_autotest_header, browser_context=c, page_url=points_url)
            log.info("成功初始化积分portal页面")
            p.wait_for_timeout(1000)

            # 2. 点击 Add points 进入了points 商品列表页面
            p.wait_for_selector("//a[text()='Add points']", state="visible", timeout=10000)
            points_page.click_add_points()
            log.info("成功点击Add points按钮，进入积分商品列表页面")
            p.wait_for_timeout(2000)

            # 3. 点击 Checkout, 进入了checkout 页面
            p.wait_for_selector("//button[.//span[text()='Checkout']]", state="visible", timeout=10000)
            points_page.click_points_list_checkout()
            log.info("成功点击Checkout按钮，进入结算页面")
            p.wait_for_timeout(2000)

            # 4. 选择 Payment Method
            p.wait_for_selector("[data-testid='wid-payment-box']", state="visible", timeout=10000)
            points_page.select_payment_method()
            log.info("成功点击支付方式模块")
            p.wait_for_timeout(1000)

            # 5. 选择 paypal 支付方式
            p.wait_for_selector("//*[@data-category='P']", state="visible", timeout=10000)
            points_page.select_paypal_payment()
            log.info("成功选择PayPal支付方式")
            p.wait_for_timeout(1000)

            # 6. 点击 Confirm payment method 自己回到了checkout 页面
            p.wait_for_selector("[data-testid='btn-pay-method-confirm']", state="visible", timeout=10000)
            points_page.confirm_payment_method()
            log.info("成功点击确认支付方式按钮，回到checkout页面")
            p.wait_for_timeout(1000)

            # 7. 点击 Place order
            p.wait_for_selector("//button[@data-testid='btn-checkout']", state="visible", timeout=10000)
            points_page.click_points_checkout_place_order()
            log.info("成功点击Place Order按钮")
            p.wait_for_timeout(3000)

            # 断言：验证是否跳转到了PayPal页面
            # 等待页面跳转完成
            p.wait_for_load_state("load")
            
            # 检查当前URL是否包含PayPal相关域名
            current_url = p.url
            log.info(f"当前页面URL: {current_url}")
            
            # 验证是否跳转到PayPal页面（PayPal域名通常包含paypal.com）
            if "paypal.com" in current_url.lower():
                log.info("✅ 断言成功：已成功跳转到PayPal页面")
                assert True, "成功跳转到PayPal页面"
            else:
                log.warning(f"当前页面URL: {current_url}")
                log.warning("⚠️ 断言失败：未跳转到PayPal页面")
                assert False, f"未跳转到PayPal页面，当前URL: {current_url}"

            log.info("MWeb Gold积分购买测试流程全部完成！")

        except Exception as e:
            log.error(f"测试过程中发生异常: {str(e)}")
            try:
                log.info(f"当前页面URL: {p.url}")
                log.info(f"页面标题: {p.title()}")
                import time
                screenshot_path = f"error_screenshot_{int(time.time())}.png"
                p.screenshot(path=screenshot_path)
                log.info(f"错误截图已保存: {screenshot_path}")
            except Exception as debug_e:
                log.error(f"保存调试信息时出错: {str(debug_e)}")
            raise

