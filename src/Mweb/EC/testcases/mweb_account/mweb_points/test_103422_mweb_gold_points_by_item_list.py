"""
<AUTHOR>  Assistant
@Version        :  V1.0.0
------------------------------------
@File           :  test_103422_mweb_gold_points_by_item_list.py
@Description    :  MWeb Gold积分商品列表验证测试用例
@CreateTime     :  2025/08/12
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/08/12
"""
import allure
import pytest
from playwright.sync_api import Page, TimeoutError
from src.Mweb.EC.mweb_pages.mweb_account_page.mweb_page_points.mweb_page_points_portal import MWebPointsPortalPage
from src.config.weee.log_help import log

@allure.story("积分商品列表验证")
class TestMWebGoldPointsItemList:
    pytestmark = [pytest.mark.mweb_gold_points, pytest.mark.item_list]

    @allure.title("积分商品列表页面验证")
    def test_103422_mweb_gold_points_by_item_list(self, xiaochong_phone_page: dict, xiaochong_h5_autotest_header):
        """
        测试MWeb Gold积分商品列表页面的商品展示
        步骤：
        1. 初始化页面：/account/perks?ws=me_page
        2. 点击 Add points 进入了points 商品列表页面
        3. 断言验证页面是否存在三个商品积分商品：200、500、1000三个金额
        """
        p: Page = xiaochong_phone_page.get("page")
        c = xiaochong_phone_page.get("context")
        points_url = "/account/perks?ws=me_page"

        try:
            # 1. 初始化积分portal页面
            points_page = MWebPointsPortalPage(p, xiaochong_h5_autotest_header, browser_context=c, page_url=points_url)
            log.info("成功初始化积分portal页面")
            p.wait_for_timeout(1000)

            # 2. 点击 Add points 进入了points 商品列表页面
            p.wait_for_selector("//a[text()='Add points']", state="visible", timeout=10000)
            points_page.click_add_points()
            log.info("成功点击Add points按钮，进入积分商品列表页面")
            p.wait_for_timeout(2000)

            # 3. 断言验证页面是否存在三个商品积分商品：200、500、1000三个金额
            log.info("开始验证积分商品列表页面的三个商品金额")
            
            # 验证 $200 积分商品是否存在
            try:
                p.wait_for_selector("//span[text()='200']", state="visible", timeout=10000)
                log.info("✅ 验证成功：$200 积分商品存在")
            except TimeoutError:
                log.error("❌ 验证失败：$200 积分商品不存在")
                assert False, "$200 积分商品不存在"

            # 验证 $500 积分商品是否存在
            try:
                p.wait_for_selector("//span[text()='500']", state="visible", timeout=10000)
                log.info("✅ 验证成功：$500 积分商品存在")
            except TimeoutError:
                log.error("❌ 验证失败：$500 积分商品不存在")
                assert False, "$500 积分商品不存在"

            # 验证 $1000 积分商品是否存在
            try:
                p.wait_for_selector("//span[text()='1,000']", state="visible", timeout=10000)
                log.info("✅ 验证成功：$1000 积分商品存在")
            except TimeoutError:
                log.error("❌ 验证失败：$1000 积分商品不存在")
                assert False, "$1000 积分商品不存在"

            # 额外验证：确保页面标题或URL表明我们在正确的页面上
            current_url = p.url
            log.info(f"当前页面URL: {current_url}")
            
            # 验证页面标题或内容包含积分相关关键词
            page_title = p.title()
            log.info(f"页面标题: {page_title}")
            
            # 如果页面标题包含积分相关关键词，则记录成功
            if any(keyword in page_title.lower() for keyword in ['points', '积分', 'perks']):
                log.info("✅ 页面标题验证成功：包含积分相关关键词")
            else:
                log.info("ℹ️ 页面标题未包含积分相关关键词，但这不是错误")

            log.info("🎉 MWeb Gold积分商品列表验证测试全部完成！")
            log.info("✅ 所有三个积分商品金额（$200、$500、$1000）都已成功验证")

        except Exception as e:
            log.error(f"测试过程中发生异常: {str(e)}")
            try:
                log.info(f"当前页面URL: {p.url}")
                log.info(f"页面标题: {p.title()}")
                import time
                screenshot_path = f"error_screenshot_{int(time.time())}.png"
                p.screenshot(path=screenshot_path)
                log.info(f"错误截图已保存: {screenshot_path}")
            except Exception as debug_e:
                log.error(f"保存调试信息时出错: {str(debug_e)}")
            raise
