"""
H5-Marketplace All Store Tabs 点击验证测试

优化说明：
- 主要测试方法 test_100831_click_all_store_tabs 已经包含了所有tab的点击和验证
- 其他单独的tab测试方法已被注释，以避免重复执行
- 如果需要单独测试某个tab，可以取消注释对应的方法
"""

import allure
import pytest
from playwright.sync_api import Page
from urllib.parse import urlparse, parse_qs
from src.Mweb.EC.mweb_pages.mweb_page_mkpl.mweb_page_mkpl_all_store.mweb_page_mkpl_all_store_tabs_click import MWebMkplAllStorePage
from src.config.weee.log_help import log


@allure.story("H5-Marketplace All Store Tabs 验证")
class TestAllStoreTabsClick:
    pytestmark = [pytest.mark.mweb_regression]

    @allure.title("【101495】 H5-Marketplace All Store Tabs 顺序点击验证（含URL断言）")
    @pytest.mark.h5home
    def test_101495_click_all_store_tabs(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【101495】 H5-Marketplace All Store Tabs 顺序点击验证（含URL断言）
        测试步骤：
        1. 直接访问 https://www.sayweee.com/zh/mkpl/global?mode=sub_page&hide_activity_pop=1 页面
        2. 等待页面加载完成后等待5秒
        3. 关闭首页广告弹窗
        4. 按 "日本" -> "韩国" -> "美国" -> "其他" -> "推荐" 的顺序依次点击校验，每个tab点击间隔5秒
        5. 验证每个tab都能正常点击和加载，最后回到推荐tab结束
        6. 校验每个tab切换后URL中的key参数：
           - 日本: key=japan
           - 韩国: key=korea
           - 美国: key=usa
           - 其他: key=others
           - 推荐: 不验证URL（跳过）
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]

        # 创建全球购所有商店页面操作类实例
        all_store_page = MWebMkplAllStorePage(_page, h5_autotest_header, _context)

        # 按顺序点击所有tab并验证URL
        with allure.step("按 日本->韩国->美国->其他->推荐 的顺序依次点击校验所有tab，并验证URL中的key参数"):
            all_store_page.click_all_tabs_in_sequence()

        log.info("测试完成，成功按顺序点击了所有tab并验证了URL参数")

    # 以下为单独的tab测试方法，已被注释以避免重复执行
    # 如果需要单独测试某个tab，可以取消注释对应的方法

    # @allure.title("【100831-1】 H5-Marketplace All Store 推荐Tab 单独点击验证")
    # @pytest.mark.h5home
    def _test_101495_click_recommend_tab(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【101495-1】 H5-Marketplace All Store 推荐Tab 单独点击验证
        测试步骤：
        1. 直接访问 https://www.sayweee.com/zh/mkpl/global?mode=sub_page&hide_activity_pop=1 页面
        2. 等待页面加载完成后等待5秒
        3. 关闭首页广告弹窗
        4. 点击推荐tab
        5. 验证tab能正常点击和加载
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]

        # 创建全球购所有商店页面操作类实例
        all_store_page = MWebMkplAllStorePage(_page, h5_autotest_header, _context)

        # 点击推荐tab
        with allure.step("点击推荐tab"):
            all_store_page.click_recommend_tab()

        log.info("测试完成，成功点击推荐tab")

    # @allure.title("【101495-2】 H5-Marketplace All Store 日本Tab 单独点击验证")
    # @pytest.mark.h5home
    def _test_101495_click_japan_tab(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【101495-2】 H5-Marketplace All Store 日本Tab 单独点击验证
        测试步骤：
        1. 直接访问 https://www.sayweee.com/zh/mkpl/global?mode=sub_page&hide_activity_pop=1 页面
        2. 等待页面加载完成后等待5秒
        3. 关闭首页广告弹窗
        4. 点击日本tab
        5. 验证tab能正常点击和加载
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]

        # 创建全球购所有商店页面操作类实例
        all_store_page = MWebMkplAllStorePage(_page, h5_autotest_header, _context)

        # 点击日本tab
        with allure.step("点击日本tab"):
            all_store_page.click_japan_tab()

        # 验证URL中的key参数
        with allure.step("验证日本tab的URL参数"):
            self._verify_url_key_parameter(_page, "日本", "japan")

        log.info("测试完成，成功点击日本tab并验证URL参数")

    # @allure.title("【100831-3】 H5-Marketplace All Store 韩国Tab 单独点击验证")
    # @pytest.mark.h5home
    def _test_101495_click_korea_tab(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【101495-3】 H5-Marketplace All Store 韩国Tab 单独点击验证
        测试步骤：
        1. 直接访问 https://www.sayweee.com/zh/mkpl/global?mode=sub_page&hide_activity_pop=1 页面
        2. 等待页面加载完成后等待5秒
        3. 关闭首页广告弹窗
        4. 点击韩国tab
        5. 验证tab能正常点击和加载
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]

        # 创建全球购所有商店页面操作类实例
        all_store_page = MWebMkplAllStorePage(_page, h5_autotest_header, _context)

        # 点击韩国tab
        with allure.step("点击韩国tab"):
            all_store_page.click_korea_tab()

        # 验证URL中的key参数
        with allure.step("验证韩国tab的URL参数"):
            self._verify_url_key_parameter(_page, "韩国", "korea")

        log.info("测试完成，成功点击韩国tab并验证URL参数")

    # @allure.title("【100831-4】 H5-Marketplace All Store 美国Tab 单独点击验证")
    # @pytest.mark.h5home
    def _test_101495_click_usa_tab(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【101495-4】 H5-Marketplace All Store 美国Tab 单独点击验证
        测试步骤：
        1. 直接访问 https://www.sayweee.com/zh/mkpl/global?mode=sub_page&hide_activity_pop=1 页面
        2. 等待页面加载完成后等待5秒
        3. 关闭首页广告弹窗
        4. 点击美国tab
        5. 验证tab能正常点击和加载
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]

        # 创建全球购所有商店页面操作类实例
        all_store_page = MWebMkplAllStorePage(_page, h5_autotest_header, _context)

        # 点击美国tab
        with allure.step("点击美国tab"):
            all_store_page.click_usa_tab()

        # 验证URL中的key参数
        with allure.step("验证美国tab的URL参数"):
            self._verify_url_key_parameter(_page, "美国", "usa")

        log.info("测试完成，成功点击美国tab并验证URL参数")

    # @allure.title("【100831-5】 H5-Marketplace All Store 其他Tab 单独点击验证")
    # @pytest.mark.h5home
    def _test_101495_click_other_tab(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【101495-5】 H5-Marketplace All Store 其他Tab 单独点击验证
        测试步骤：
        1. 直接访问 https://www.sayweee.com/zh/mkpl/global?mode=sub_page&hide_activity_pop=1 页面
        2. 等待页面加载完成后等待5秒
        3. 关闭首页广告弹窗
        4. 点击其他tab
        5. 验证tab能正常点击和加载
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]

        # 创建全球购所有商店页面操作类实例
        all_store_page = MWebMkplAllStorePage(_page, h5_autotest_header, _context)

        # 点击其他tab
        with allure.step("点击其他tab"):
            all_store_page.click_other_tab()

        # 验证URL中的key参数
        with allure.step("验证其他tab的URL参数"):
            self._verify_url_key_parameter(_page, "其他", "others")

        log.info("测试完成，成功点击其他tab并验证URL参数")

    def _verify_url_key_parameter(self, page: Page, tab_name: str, expected_key: str):
        """
        验证URL中的key参数是否正确

        Args:
            page: Playwright页面对象
            tab_name: tab名称，用于日志记录
            expected_key: 期望的key参数值
        """
        # 等待URL更新
        page.wait_for_timeout(1000)

        # 获取当前页面URL
        current_url = page.url
        log.info(f"当前页面URL: {current_url}")

        # 解析URL参数
        parsed_url = urlparse(current_url)
        query_params = parse_qs(parsed_url.query)

        # 获取key参数
        actual_key = query_params.get('key', [None])[0]

        # 断言key参数是否正确
        assert actual_key == expected_key, f"{tab_name}tab的URL key参数验证失败: 期望 '{expected_key}', 实际 '{actual_key}'"

        log.info(f"{tab_name}tab的URL key参数验证成功: key={actual_key}")
