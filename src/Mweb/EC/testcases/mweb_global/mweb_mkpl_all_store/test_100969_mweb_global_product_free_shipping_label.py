import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_mkpl.mweb_page_mkpl_home.mweb_page_mkpl_search import MWebMkplSearchPage
from src.Mweb.EC.mweb_ele.mweb_mkpl_home.mweb_global_waterfall import global_product_free_shipping_label
from src.config.weee.log_help import log


@allure.story("H5 Global+商品免运费标签功能")
class TestMWebGlobalProductFreeShippingLabel:
    pytestmark = [pytest.mark.mweb_regression]

    @allure.title("[100969] H5 Global+商品免运费标签测试")
    @pytest.mark.h5home
    def test_100969_global_product_free_shipping_label(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【100969】H5商品免运费标签测试
        测试步骤：
        1. 访问主页
        2. 搜索商品
        3. 检查免运费标签内容不为空
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]
        
        # 构造搜索页面操作实例
        search_page = MWebMkplSearchPage(_page, h5_autotest_header)

        # 执行搜索流程测试
        with allure.step("执行搜索流程测试"):
            # 访问主页
            search_page.navigate_to_homepage()
            
            # 搜索商品
            assert search_page.search_mkpl_product(), "搜索商品失败"
            
            # 检查URL包含search字符串
            current_url = _page.url
            assert "search" in current_url, f"页面跳转失败，URL中不包含search: {current_url}"
            
            # 等待6秒检查搜索结果
            _page.wait_for_timeout(6000)
            
            # 检查免运费标签
            free_shipping_label = _page.get_by_test_id(global_product_free_shipping_label)
            if free_shipping_label.count() > 0:
                # 检查标签内容不为空
                label_text = free_shipping_label.first.text_content()
                assert label_text.strip(), "免运费标签内容为空"
                log.info(f"免运费标签内容: {label_text}")
            else:
                log.info("免运费标签不存在")
            
            log.info("测试完成：已检查免运费标签")