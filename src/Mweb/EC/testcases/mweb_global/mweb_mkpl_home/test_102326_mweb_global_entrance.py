import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_mkpl.mweb_page_mkpl_home.mweb_page_global_entrance import MWebGlobalEntrancePage
from src.config.weee.log_help import log


@allure.story("H5 Global+瀑布流入口功能")
class TestMWebGlobalEntrance:
    pytestmark = [pytest.mark.mweb_regression]

    @allure.title("[102326] H5 Global+瀑布流入口测试")
    @pytest.mark.h5home
    def test_102326_mweb_global_entrance(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        [102326] H5 Global+瀑布流入口测试
        测试步骤：
        1. 访问主页https://www.sayweee.com/zh
        2. 等待5秒，检查元素close_popup_on_home是否存在，如果存在则点击该元素，如果不存在则跳过
        3. 检查页面当前可视范围内，ele_h5_home_menu_side_bar元素是否存在，如果存在，则点击该元素
        4. 等待3秒后，检查当前页面可视范围内ele_h5_home_menu_global_entrance元素是否存在，如果存在则点击该元素
        5. 断言点击ele_h5_home_menu_global_entrance后，跳转的页面url包含'mkpl/waterfall'
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]
        
        # 构造Global+入口页面操作实例
        entrance_page = MWebGlobalEntrancePage(_page, h5_autotest_header)

        # 执行Global+瀑布流入口测试
        with allure.step("执行Global+瀑布流入口测试"):
            # 执行导航和点击Global+入口流程
            assert entrance_page.navigate_and_click_global_entrance(), "Global+入口点击失败"
            
            # 等待3秒让页面跳转完成
            _page.wait_for_timeout(3000)
            
            # 检查URL跳转
            current_url = _page.url
            
            # 断言URL中包含mkpl/waterfall
            assert "mkpl/waterfall" in current_url, f"页面跳转失败，URL中不包含mkpl/waterfall: {current_url}"
            log.info(f"Global+瀑布流入口跳转验证成功: {current_url}")
            
            log.info("Global+瀑布流入口测试完成")