import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_mkpl.mweb_page_mkpl_home.mweb_page_mkpl_search import MWebMkplSearchPage
from src.Mweb.EC.mweb_ele.mweb_mkpl_home.mweb_global_waterfall import close_popup_on_home
from src.config.weee.log_help import log


@allure.story("H5 商家快速搜索功能")
class TestMWebSellerQuickSearch:
    pytestmark = [pytest.mark.mweb_regression]

    @allure.title("[102326] H5商家快速搜索测试")
    @pytest.mark.h5home
    def test_102326_mweb_seller_quick_search(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        [102326] H5商家快速搜索测试
        测试步骤：
        1. 访问主页https://www.sayweee.com/zh
        2. 输入商家名称: Woo Japan
        3. 回车确认发起搜索
        4. 等待3秒后，断言url检查页面是否跳转到: https://www.sayweee.com/en/mkpl/vendor/6887
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]
        
        # 构造搜索页面操作实例
        search_page = MWebMkplSearchPage(_page, h5_autotest_header)

        # 执行商家搜索测试
        with allure.step("执行商家搜索测试"):
            # 1. 访问主页
            _page.goto("https://www.sayweee.com/zh")
            log.info("访问主页完成")
            
            # 等待7秒
            _page.wait_for_timeout(7000)
            
            # 检查并点击关闭弹窗按钮
            close_btn = _page.get_by_test_id(close_popup_on_home)
            if close_btn.count() > 0:
                close_btn.click()
                log.info("点击关闭弹窗按钮成功")
            else:
                log.info("关闭弹窗按钮不存在")
            
            # 2. 搜索商家
            assert search_page.search_seller("Woo Japan"), "搜索商家失败"
            
            # 3. 检查URL跳转（已在search_seller中等待）
            current_url = _page.url
            
            # 断言URL中包含mkpl/vendor/6887
            assert "mkpl/vendor/6887" in current_url, f"页面跳转失败，URL中不包含mkpl/vendor/6887: {current_url}"
            log.info(f"商家搜索跳转验证成功: {current_url}")
            
            log.info("商家快速搜索测试完成")

