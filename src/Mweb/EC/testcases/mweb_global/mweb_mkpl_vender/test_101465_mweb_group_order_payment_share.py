import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_mkpl_vender.mweb_page_vendor_group_order_payment_share import MWebVendorGroupOrderPaymentSharePage
from src.Mweb.EC.mweb_ele.mweb_mkpl_home.mweb_global_waterfall import ele_arrow_right_icon
from src.Mweb.EC.mweb_ele.mweb_mkpl_vender.mweb_mkpl_vender_ele import (
    btn_share,
    order_payment_share_pop_up,
    order_payment_share_title,
    order_payment_share_desc,
    order_payment_share_copy_link,order_payment_share_zh_lang,
    order_payment_share_zht_lang,
    order_payment_share_en_lang,
    order_payment_share_ko_lang,
    order_payment_share_ja_lang,
    order_payment_share_vi_lang
)
from src.config.weee.log_help import log


@allure.story("H5 拼单支付分享功能")
class TestMWebGroupOrderPaymentShare:
    pytestmark = [pytest.mark.mweb_regression]

    @allure.title("[101465] H5 拼单支付分享测试")
    @pytest.mark.h5home
    def test_101465_mweb_group_order_payment_share(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        [101465] H5 拼单支付分享测试
        测试步骤：
        1. 查看订单详情页面horder/detail/74009002
        2. 滚动页面-直到页面ele_arrow_right_icon元素可见
        3. 点击ele_arrow_right_icon元素
        4. 等待6秒,检查页面btn_share元素是否存在
        5. 如果存在，则点击该元素
        6. 点击后检查order_payment_share_pop_up是否存在，如果存在则断言order_payment_share_title，order_payment_share_desc不为空
        7. 等待3秒后，点击order_payment_share_copy_link元素
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]
        
        # 构造拼单支付分享页面操作实例
        payment_share_page = MWebVendorGroupOrderPaymentSharePage(_page, h5_autotest_header)

        # 执行拼单支付分享测试
        with allure.step("执行拼单支付分享测试"):
            # 使用页面操作类执行完整流程
            domain = h5_autotest_header.get('domain', 'https://www.sayweee.com')
            _page.goto(f"{domain}/zh/order/detail/74009002")
            log.info("访问订单详情页面完成")
            
            # 2. 滚动页面直到箭头图标可见
            arrow_icon = _page.locator(ele_arrow_right_icon)
            if arrow_icon.count() > 0:
                arrow_icon.first.scroll_into_view_if_needed()
                log.info("滚动到箭头图标位置")
                
                # 3. 点击箭头图标
                arrow_icon.first.click()
                log.info("点击箭头图标成功")
            else:
                pytest.skip("箭头图标不存在，跳过测试")
            
            # 4. 等待6秒，检查分享按钮
            _page.wait_for_timeout(6000)
            
            share_btn = _page.get_by_test_id(btn_share)
            if share_btn.count() > 0:
                # 5. 点击分享按钮
                share_btn.click()
                log.info("点击分享按钮成功")
                
                # 6. 等待5秒，检查分享弹窗
                _page.wait_for_timeout(5000)
                
                share_popup = _page.get_by_test_id(order_payment_share_pop_up)
                if share_popup.count() > 0:
                    log.info("分享弹窗存在")
                    
                    # 检查标题和描述元素是否存在
                    title_element = _page.get_by_test_id(order_payment_share_title)
                    desc_element = _page.get_by_test_id(order_payment_share_desc)
                    
                    log.info(f"标题元素数量: {title_element.count()}")
                    log.info(f"描述元素数量: {desc_element.count()}")
                    
                    # 输出弹窗内的所有元素信息用于调试
                    popup_html = share_popup.inner_html()
                    log.info(f"弹窗HTML内容: {popup_html[:500]}...")  # 只显示前500个字符
                    
                    # 尝试使用更宽泛的选择器查找元素
                    all_elements_in_popup = share_popup.locator("*[data-testid]")
                    log.info(f"弹窗中所有带testid的元素数量: {all_elements_in_popup.count()}")
                    
                    for i in range(min(5, all_elements_in_popup.count())):
                        element = all_elements_in_popup.nth(i)
                        testid = element.get_attribute("data-testid")
                        log.info(f"元素{i}: data-testid='{testid}'")
                    
                    # 断言标题元素存在
                    assert title_element.count() > 0, f"分享标题元素不存在，预期 testid: {order_payment_share_title}"
                    
                    # 断言描述元素存在
                    assert desc_element.count() > 0, f"分享描述元素不存在，预期 testid: {order_payment_share_desc}"

                    zh_lang = _page.get_by_test_id(order_payment_share_zh_lang)
                    zht_lang = _page.get_by_test_id(order_payment_share_zht_lang)
                    en_lang = _page.get_by_test_id(order_payment_share_en_lang)
                    ko_lang = _page.get_by_test_id(order_payment_share_ko_lang)
                    ja_lang = _page.get_by_test_id(order_payment_share_ja_lang)
                    vi_lang = _page.get_by_test_id(order_payment_share_vi_lang)

                    assert zh_lang.count() > 0, f"元素不存在，预期 testid: {zh_lang}"
                    assert zht_lang.count() > 0, f"元素不存在，预期 testid: {zht_lang}"
                    assert en_lang.count() > 0, f"元素不存在，预期 testid: {en_lang}"
                    assert ko_lang.count() > 0, f"元素不存在，预期 testid: {ko_lang}"
                    assert ja_lang.count() > 0, f"元素不存在，预期 testid: {ja_lang}"
                    assert vi_lang.count() > 0, f"元素不存在，预期 testid: {vi_lang}"

                    # 获取元素内容并断言不为空
                    title_text = title_element.text_content()
                    desc_text = desc_element.text_content()
                    
                    assert title_text.strip(), "分享标题内容为空"
                    assert desc_text.strip(), "分享描述内容为空"
                    log.info(f"分享标题: {title_text}")
                    log.info(f"分享描述: {desc_text}")

                else:
                    pytest.skip("分享弹窗不存在，跳过测试")
                
                # 7. 等待3秒后点击复制链接
                _page.wait_for_timeout(3000)
                
                copy_link_btn = _page.get_by_test_id(order_payment_share_copy_link)
                assert copy_link_btn.count() > 0, "复制链接按钮不存在"
                
                copy_link_btn.click()
                log.info("点击复制链接成功")
                
            else:
                pytest.skip("分享按钮不存在，跳过测试")
            
            log.info("拼单支付分享测试完成")