import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_mkpl_vender.mweb_page_vendor_group_order import MWebVendorGroupOrderPage
from src.Mweb.EC.mweb_ele.mweb_mkpl_vender.mweb_mkpl_vender_ele import (
    ele_group_order_pop_up_title,
    ele_group_order_pop_up_subtitle,
    ele_group_order_pop_up_desc,
    ele_group_order_share_image,
    ele_group_order_share_title,
    ele_group_order_share_desc,
    ele_float_cart_container,
    ele_group_order_float_cart_eta,
    ele_group_order_float_cart_user_info,
    ele_group_order_float_cart_tips,
    ele_seller_title,
    ele_group_order_modal_close_title,
    ele_group_order_modal_close_desc,
    ele_group_order_homepage_banner_icon,
    ele_group_order_homepage_banner_title,
    ele_group_order_homepage_banner_subtitle,
    ele_group_order_homepage_banner_label
)
from src.config.weee.log_help import log


@allure.story("[101470]H5商家好友拼单功能")
class TestMWebHostCreateGroupOrder:
    pytestmark = [pytest.mark.mweb_regression]

    @allure.title("【101470】移动端商家好友拼单测试")
    @pytest.mark.present
    def test_101470_host_create_group_order(self, phone_page: dict, h5_autotest_header):
        """
        移动端商家好友拼单测试
        1. 访问商家页面
        2. 检查好友拼单按钮是否存在
        3. 点击好友拼单按钮并验证弹窗内容
        4. 关闭弹窗后再次点击拼单按钮
        5. 点击邀请好友按钮
        6. 验证分享弹窗内容并复制链接
        7. 点击删除按钮并确认取消
        8. 验证页面跳转
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        
        # 构造商家拼单页面操作实例
        vendor_page = MWebVendorGroupOrderPage(p, h5_autotest_header, browser_context=c)

        # 执行好友拼单流程测试
        with allure.step("执行好友拼单流程测试"):
            # 访问页面并点击拼单按钮
            vendor_page.navigate_to_vendor_page()

            # 增加等待时间让页面完全加载
            p.wait_for_timeout(8000)
            
            # 添加调试信息
            from src.Mweb.EC.mweb_ele.mweb_mkpl_vender.mweb_mkpl_vender_ele import ele_create_group_order
            group_order_btn = p.get_by_test_id(ele_create_group_order)
            log.info(f"好友拼单按钮元素数量: {group_order_btn.count()}")
            
            if not vendor_page.click_group_order_button():
                # 再等待5秒后重试
                p.wait_for_timeout(5000)
                if not vendor_page.click_group_order_button():
                    pytest.skip("好友拼单按钮不存在，跳过测试")
                    return
            
            # 等待3秒后检查弹窗
            p.wait_for_timeout(6000)
            assert vendor_page.check_popup_exists(), "拼单弹窗未出现"
            
            # 验证弹窗内容不为空
            title_element = p.get_by_test_id(ele_group_order_pop_up_title)
            subtitle_element = p.get_by_test_id(ele_group_order_pop_up_subtitle)
            desc_element = p.get_by_test_id(ele_group_order_pop_up_desc)
            
            title_text = title_element.text_content() if title_element.count() > 0 else ""
            subtitle_text = subtitle_element.text_content() if subtitle_element.count() > 0 else ""
            desc_text = desc_element.text_content() if desc_element.count() > 0 else ""
            
            assert title_text.strip(), "弹窗标题内容为空"
            assert subtitle_text.strip(), "弹窗副标题内容为空"
            assert desc_text.strip(), "弹窗描述内容为空"
            
            log.info(f"弹窗标题: {title_text}")
            log.info(f"弹窗副标题: {subtitle_text}")
            log.info(f"弹窗描述: {desc_text}")
            
            # 等待3秒后关闭弹窗
            p.wait_for_timeout(3000)
            assert vendor_page.close_popup(), "关闭弹窗失败"
            
            # 再次点击拼单按钮并确认
            vendor_page.click_group_order_button()
            p.wait_for_timeout(3000)
            vendor_page.click_confirm_button()
            
            # 等待5秒后检查分享弹窗
            p.wait_for_timeout(5000)
            if vendor_page.check_share_popup_exists():
                # 验证分享弹窗内容不为空
                share_image = p.get_by_test_id(ele_group_order_share_image)
                share_title = p.get_by_test_id(ele_group_order_share_title)
                share_desc = p.get_by_test_id(ele_group_order_share_desc)
                
                image_exists = share_image.count() > 0
                title_text = share_title.text_content() if share_title.count() > 0 else ""
                desc_text = share_desc.text_content() if share_desc.count() > 0 else ""
                
                assert image_exists, "分享弹窗图片不存在"
                assert title_text.strip(), "分享弹窗标题内容为空"
                assert desc_text.strip(), "分享弹窗描述内容为空"
                
                log.info(f"分享弹窗标题: {title_text}")
                log.info(f"分享弹窗描述: {desc_text}")
                
                # 点击复制链接
                p.wait_for_timeout(3000)
                vendor_page.click_copy_link_button()

                # 点击全部商品Tab
                p.wait_for_timeout(3000)
                vendor_page.check_and_click_seller_tab_all()
                
                # 等待5秒后检查商品卡片并点击加购按钮
                p.wait_for_timeout(5000)
                if vendor_page.check_product_card_exists():
                    vendor_page.check_and_click_atc_button()
                    log.info("商品卡片存在，已点击加购按钮")
                    
                    # 等待5秒让悬浮购物车出现
                    p.wait_for_timeout(8000)
                    
                    # 检查悬浮购物车内容
                    float_cart_container = p.get_by_test_id(ele_float_cart_container)
                    log.info(f"悬浮购物车容器元素数量: {float_cart_container.count()}")
                    #if float_cart_container.count() > 0:
                    vendor_page.execute_float_cart_flow()
                    """"p.wait_for_timeout(5000)
                        eta_element = p.get_by_test_id(ele_group_order_float_cart_eta)
                        user_info_element = p.get_by_test_id(ele_group_order_float_cart_user_info)
                        tips_element = p.get_by_test_id(ele_group_order_float_cart_tips)
                        seller_title_element = p.get_by_test_id(ele_seller_title)

                        eta_text = eta_element.text_content() if eta_element.count() > 0 else ""
                        user_info_text = user_info_element.text_content() if user_info_element.count() > 0 else ""
                        tips_text = tips_element.text_content() if tips_element.count() > 0 else ""
                        seller_title_text = seller_title_element.text_content() if seller_title_element.count() > 0 else ""

                        assert eta_text.strip(), "悬浮购物车ETA内容为空"
                        assert user_info_text.strip(), "悬浮购物车用户信息内容为空"
                        assert tips_text.strip(), "悬浮购物车提示内容为空"
                        assert seller_title_text.strip(), "商家标题内容为空"

                        log.info(f"悬浮购物车ETA: {eta_text}")
                        log.info(f"悬浮购物车用户信息: {user_info_text}")
                        log.info(f"悬浮购物车提示: {tips_text}")
                        log.info(f"商家标题: {seller_title_text}")
                    else:
                        log.info("悬浮购物车容器不存在，执行悬浮购物车流程")"""

                else:
                    log.info("商品卡片不存在，跳过加购操作")

                # 先执行handle_modal_close_only流程
                p.wait_for_timeout(5000)
                vendor_page.handle_modal_close_only()
                p.wait_for_timeout(4000)

                banner_icon = p.get_by_test_id(ele_group_order_homepage_banner_icon)
                banner_title = p.get_by_test_id(ele_group_order_homepage_banner_title)
                banner_subtitle = p.get_by_test_id(ele_group_order_homepage_banner_subtitle)
                banner_label = p.get_by_test_id(ele_group_order_homepage_banner_label)

                assert banner_icon.count() > 0, "banner图标元素不存在"
                assert banner_title.count() > 0, "banner标题元素不存在"
                assert banner_subtitle.count() > 0, "banner副标题元素不存在"
                assert banner_label.count() > 0, "banner标签元素不存在"

                banner_title_text = banner_title.text_content() if banner_title.count() > 0 else ""
                banner_subtitle_text = banner_subtitle.text_content() if banner_subtitle.count() > 0 else ""
                banner_label_text = banner_label.text_content() if banner_label.count() > 0 else ""

                assert banner_title_text.strip(), "banner标题内容为空"
                assert banner_subtitle_text.strip(), "banner副标题内容为空"
                assert banner_label_text.strip(), "banner标签内容为空"

                log.info(f"banner标题: {banner_title_text}")
                log.info(f"banner副标题: {banner_subtitle_text}")
                log.info(f"banner标签: {banner_label_text}")

                p.wait_for_timeout(7000)

                # 然后执行handle_homepage_banner_flow
                vendor_page.handle_homepage_banner_flow()
                p.wait_for_timeout(3000)
                vendor_page.click_close_group_order_cart_popup()
                p.wait_for_timeout(3000)
                    
                # 最后执行execute_group_order_flow
                vendor_page.execute_group_order_flow()
            
            log.info("好友拼单流程测试成功")

        log.info("商家好友拼单测试完成")
