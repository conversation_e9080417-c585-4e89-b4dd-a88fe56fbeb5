import allure
import pytest
from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_page_mkpl_home.mweb_page_global_waterfall import MWebPageGlobalWaterfall
from src.config.weee.log_help import log


@allure.story("H5-Marketplace Banner Array 验证")
class TestWaterfallBannerArray:
    pytestmark = [pytest.mark.mweb_regression]

    @allure.title("【100829】 H5-Marketplace Banner Array 点击验证")
    @pytest.mark.h5home
    def test_100829_click_mkpl_banner_array_3(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【100829】 H5-Marketplace Banner Array 点击验证
        测试步骤：
        1. 直接访问 https://tb1.sayweee.net/zh/mkpl/waterfall 页面
        2. 等待页面加载完成后等待5秒
        3. 检查是否出现全球购介绍弹窗，如果有则关闭
        4. 在页面中找到 Marketplace Banner Array 3 元素
        5. 点击该元素
        6. 验证跳转到包含 "mkpl/global" 的页面
        7. 验证URL包含必要的参数 mode=sub_page 和 hide_activity_pop=1
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]

        # 创建全球购瀑布流页面操作类实例
        waterfall_page = MWebPageGlobalWaterfall(_page, h5_autotest_header, _context)

        # 处理全球购介绍弹窗
        waterfall_page.handle_global_intro_popup()

        # 点击 Banner Array 3 元素并验证跳转结果
        current_url = waterfall_page.click_banner_array_3()

        log.info(f"测试完成，成功跳转到目标页面: {current_url}")


