import allure
import pytest
from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_page_mkpl.mweb_page_mkpl_home.mweb_page_global_waterfall import MWebPageGlobalWaterfall
from src.config.weee.log_help import log


@allure.story("H5-Marketplace Coupon List 验证")
class TestWaterfallCouponList:
    pytestmark = [pytest.mark.mweb_regression]

    @allure.title("【100830】 H5-Marketplace Coupon List 点击验证")
    @pytest.mark.h5home
    def test_100830_click_coupon_list(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【100830】 H5-Marketplace Coupon List 点击验证
        测试步骤：
        1. 直接访问 mkpl/waterfall 页面
        2. 等待页面加载完成后等待5秒
        3. 检查是否出现全球购介绍弹窗，如果有则关闭
        4. 在页面中找到class="w-11 h-11 rounded-700 mr-2.5 overflow-hidden flex-none"对应的元素
        5. 点击该元素
        6. 验证跳转到包含 "mkpl/coupon/landing" 的页面
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]

        # 创建全球购瀑布流页面操作类实例
        waterfall_page = MWebPageGlobalWaterfall(_page, h5_autotest_header, _context)

        # 处理全球购介绍弹窗
        waterfall_page.handle_global_intro_popup()

        # 点击优惠券图标元素并验证跳转结果
        current_url = waterfall_page.click_coupon_list_icon()

        log.info(f"测试完成，成功跳转到目标页面: {current_url}")
