"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_114923_mweb_order_detail_change_address.py
@Description    :  H5订单详情页修改地址功能测试
@CreateTime     :  2025/1/20 14:00
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/1/20 14:00
"""
import allure
import pytest
from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_pages.mweb_page_address.mweb_page_address import MWebAddressPage
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_category.mweb_page_category import MWebCategorypage
from src.api.zipcode import switch_zipcode
from src.common.commfunc import empty_cart
from src.config.weee.log_help import log
from src.config.base_config import TEST_URL
from src.Mweb.EC.mweb_pages.mweb_page_checkout.mweb_page_checkout import MWebPageCheckout


class TestMwebOrderDetailChangeAddress:
    pytestmark = [pytest.mark.mweb_todo, pytest.mark.zhuli]

    @allure.title("H5订单详情页修改地址功能测试")
    def test_114923_mweb_order_detail_change_address(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        H5订单详情页修改地址功能测试
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        switch_zipcode(h5_autotest_header, '94538')
        p.wait_for_timeout(2000)
        
        # 清除购物车
        empty_cart(h5_autotest_header)

        # 从分类页加购生鲜商品
        with allure.step("从分类页加购生鲜商品"):
            category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="/category/sale")
            p.wait_for_timeout(3000)
            #
            # # 环境适配逻辑
            # is_tb1_env = "tb1" in TEST_URL
            #
            # if is_tb1_env:
            #     # tb1环境直接加购商品，不使用筛选
            #     log.info("tb1环境，直接加购商品")
            #     add_btns = p.get_by_test_id("btn-atc-plus").all()
            #     added_count = 0
            #     for btn in add_btns[:3]:
            #         if btn.is_visible():
            #             btn.click()
            #             p.wait_for_timeout(1500)
            #             added_count += 1
            #             if added_count >= 3:
            #                 break
            # else:
            #     # 线上环境使用筛选功能
            local_filter_id = mweb_category_ele.ele_local_delivery
            added_count = category_page.add_products_from_home_by_filter(
                filter_name="Local Delivery",
                filter_id=local_filter_id,
                count=3,
            )
            
            p.wait_for_timeout(5000)
            log.info(f"成功加购{added_count}个商品")
            assert added_count > 0, "未能成功加购商品"

        # 点击结算按钮进入结算页
        with allure.step("点击结算按钮进入结算页"):
            MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
            p.wait_for_timeout(2000)
            
            checkout_button = p.get_by_test_id("btn-checkout")
            assert checkout_button.is_visible(), "结算按钮不可见"
            checkout_button.click()
            log.info("成功点击结算按钮")
            
            p.wait_for_timeout(3000)
            upsell_button = p.get_by_test_id("btn-continue")
            if upsell_button.is_visible(timeout=3000):
                log.info("检测到upsell弹窗，点击继续结算按钮")
                upsell_button.click()
                p.wait_for_timeout(3000)
            else:
                log.info("未检测到upsell弹窗，直接进行下一步")
        
        p.wait_for_timeout(2000)

        # 使用结算页面类提交订单
        checkout_page = MWebPageCheckout(p, h5_autotest_header, c)
        fresh_order_id = checkout_page.submit_order_with_points_on_checkout_page()
        
        if not fresh_order_id:
            pytest.skip("创建生鲜订单失败")

        address_page = MWebAddressPage(p, h5_autotest_header, c, page_url=f'/account/address/order/{fresh_order_id}')

        # 验证生鲜订单显示修改地址入口
        assert address_page.verify_fresh_order_change_address_entry(fresh_order_id)

        # 验证跨区域修改地址提示错误
        # assert address_page.verify_cross_region_address_change_error(fresh_order_id)

        log.info("H5订单详情页修改地址功能验证完成")
    
