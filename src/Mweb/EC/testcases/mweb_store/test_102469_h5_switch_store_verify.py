import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_store.mweb_page_store import MWebStorePageStore
from src.config.weee.log_help import log

@allure.story("H5-商店功能验证")
class TestH5StoreFeatures:
    # pytestmark = [pytest.mark.mweb_regression]
    pytestmark = [pytest.mark.h5store, pytest.mark.regression, pytest.mark.transaction, pytest.mark.xiaoxue]

    @allure.title("H5-日本商店选择验证")
    @pytest.mark.store

    def test_h5_japanese_store_selection(self, phone_page: dict, h5_autotest_header):
        _page: Page = phone_page["page"]
        _context = phone_page["context"]

        try:
            log.info("开始H5-日本商店选择验证测试")

            store = MWebStorePageStore(_page, h5_autotest_header, _context)
            store.complete_store_selection_process()

            log.info("H5-日本商店选择验证测试完成")

        except Exception as e:
            log.error(f"测试过程中发生错误: {str(e)}")
            allure.attach(
                _page.screenshot(),
                name="error_screenshot",
                attachment_type=allure.attachment_type.PNG
            )
            raise


