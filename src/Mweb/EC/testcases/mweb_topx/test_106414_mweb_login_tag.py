import re
import pytest
import allure
from playwright.sync_api import Page, expect, Error

from src.Mweb.EC.mweb_ele.mweb_topx.mweb_topx_ele import ele_h5_topx_homepage_tag
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log

# 验证topx标签入口展示正常，点击标签可以正常跳转

class TestMwebTopxTag:
    def test_106414_mweb_home_login_tag(self, phone_page: dict, h5_autotest_header):
        """
        【106414】 点击主要一级页面对应topx 标签可以正常跳转至topx 详情页面
        """
        # 首页topx tag 点击进入到landing page,主要分布在首页 ，分类， pdp ，搜索结果页 ，替换对应的url 即可
        p: Page = phone_page.get('page')

        with allure.step("打开首页并处理弹窗"):
            p.goto(TEST_URL)
            log.info(f"Navigated to {TEST_URL}")
            try:
                p.get_by_role("button", name="Continue").click(timeout=3000)
                log.info("Clicked the 'Continue' pop-up.")
            except Error:
                log.info("'Continue' pop-up did not appear, proceeding.")

        with allure.step("滚动查找并点击TopX入口标签"):
            topx_tag = p.locator(ele_h5_topx_homepage_tag).first
            tag_found = False
            # 滚动页面，直到找到元素或达到最大滚动次数
            for _ in range(20):
                if topx_tag.is_visible():
                    log.info("TopX homepage tag is visible. Clicking it.")
                    topx_tag.click()
                    tag_found = True
                    break
                log.info("TopX tag not visible, scrolling down...")
                PageH5CommonOperations(p, h5_autotest_header).scroll_one_page(2)

            if not tag_found:
                pytest.fail("在首页滚动20次后仍未找到TopX入口标签。")

        with allure.step("验证页面已成功跳转到TopX详情页"):
            # TopX页面的URL通常包含 '/promotion/top-x/'
            expect(p, "点击TopX标签后应跳转到TopX详情页").to_have_url(re.compile(r".*/promotion/top-x/.*"), timeout=10000)
            log.info(f"✅ PASSED: Successfully navigated to a TopX page: {p.url}")




