import pytest
import allure
from playwright.sync_api import Page, expect, Error
from src.Mweb.EC.mweb_ele.mweb_topx.mweb_topx_ele import  ele_h5_topx_chart_top_rated_tag, \
    ele_h5_topx_chart_prod_metrics, ele_h5_topx_chart_list_see_all, ele_h5_topx_chart_icon
from src.Mweb.EC.mweb_pages.mweb_page_topx.mweb_page_topx import MWebTOPXPage


# 验证topx 卖点文案展示：

class TestTopxMetricsUi:
    def test_111749_mweb_topx_metrics_ui_ux(self, phone_page: dict, h5_autotest_header):
        """
        【111749】 TopX Chart页面交互验证
        1. 导航到TopX Chart页面
        2. 找到并点击包含'好评榜' (Top Rated) 标签的分类
        3. 验证点击后，该分类被正确选中
        4. 验证好评榜标签下第一个titile可以正常展示，点击see all 按钮可以正常跳转至landing page
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 进入到指定topx chart 页面
        topx_page = MWebTOPXPage(p, h5_autotest_header, browser_context=c,
                                     page_url="/promotion/top-x/chart")
        # 获取topx chart 页面的分类icon
        category_tabs = p.locator(ele_h5_topx_chart_icon).all()
        for i in category_tabs:
            i.click()
            if p.locator(ele_h5_topx_chart_top_rated_tag).is_visible():
                p.locator(ele_h5_topx_chart_top_rated_tag).click()
                p.wait_for_timeout(1000)
                p.locator(ele_h5_topx_chart_list_see_all[1]).click()
                p.wait_for_timeout(1000)
                break

        # 验证topx 详情页面对应评论展示正常
        topx_mweb_list_page_metrics = p.locator(ele_h5_topx_chart_prod_metrics).all()
        for i in topx_mweb_list_page_metrics:
            p.locator(i).is_visible()
        p.wait_for_timeout(1000)
