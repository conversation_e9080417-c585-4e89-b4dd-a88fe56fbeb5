import pytest
import allure
from playwright.sync_api import Page, expect, Error
from src.Mweb.EC.mweb_ele.mweb_topx.mweb_topx_ele import ele_h5_topx_chart_top_trending_tag, ele_h5_topx_chart_icon
from src.Mweb.EC.mweb_pages.mweb_page_topx.mweb_page_topx import MWebTOPXPage
from src.config.weee.log_help import log

class TestMwebTopxTopTrendingTag:
      # top chart 飙升榜标签展示
      def test_115401_mweb_topx_chart_top_trending_tag(self,phone_page: dict,h5_autotest_header):
          """
           【115401】 topx飙升榜标签展示
          """

          p: Page = phone_page.get("page")
          c = phone_page.get("context")
          try:
              p.get_by_role("button", name="Continue").click(timeout=3000)
              log.info("Clicked the 'Continue' pop-up.")
          except Error:
              log.info("'Continue' pop-up did not appear, proceeding.")

          #打开topx chart页面
          topx_page = MWebTOPXPage(p, h5_autotest_header, browser_context=c,
                                  page_url="/promotion/top-x/chart")

          # 获取topx chart 页面的分类icon
          category_list_wrapper = p.locator(ele_h5_topx_chart_icon)
          expect(category_list_wrapper).to_be_visible(timeout=10000)

          # 获取topx chart 页面的所有分类icon
          category_tabs = category_list_wrapper.locator('div[role="tab"]')
          tabs_count = category_tabs.count()
          log.info(f"Found {tabs_count} category tabs to test.")

          # 点击分类icon 获取topx 分类下的top trending标签
          for i in range(tabs_count):
              tab = category_tabs.nth(i)
              tab_text = tab.inner_text()

              with allure.step(f"验证分类 '{tab_text}' 下的飙升榜标签"):
                  log.info(f"Clicking tab '{tab_text}' and verifying content.")
                  tab.click()
                  # Wait for network activity to cease, ensuring the new content is loaded
                  p.wait_for_load_state("networkidle")

                  # After clicking, verify the "Top Trending" tag is visible for the current category
                  top_trending_tag = p.locator(ele_h5_topx_chart_top_trending_tag).first
                  expect(top_trending_tag).to_be_visible(timeout=5000)
                  log.info(f"Successfully verified 'Top Trending' tag for '{tab_text}'.")
