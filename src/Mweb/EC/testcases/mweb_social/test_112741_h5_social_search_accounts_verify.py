import allure
import pytest
from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home import MWebPageHome as PH5
from src.Mweb.EC.mweb_pages.mweb_social_page.mweb_page_social import MWebPageSocial
from src.config.weee.log_help import log


@allure.story("H5-社区搜索Accounts栏验证")
class TestH5HomePageCategoryVerify:
    pytestmark = [pytest.mark.mweb_regression]

    @allure.title("H5-社区搜索Accounts栏验证")
    @pytest.mark.social
    def test_112741_h5_social_search_accounts_verify(self, phone_page: dict, h5_autotest_header,
                                                     h5_open_and_close_trace):
        """
        H5-社区搜索Accounts栏验证
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]
        social = MWebPageSocial(_page, h5_autotest_header, _context)
        # 首页category存在
        social.click_search("autotest")
        social.account_verify("autotest")
