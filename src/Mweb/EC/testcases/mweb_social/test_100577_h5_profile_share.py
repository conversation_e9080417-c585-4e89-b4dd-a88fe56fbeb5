# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2025/8/4
@Software       :  PyCharm
------------------------------------
"""
import allure
import pytest
from urllib.parse import urlparse
from src.Mweb.EC.mweb_pages.mweb_social_page.mweb_page_profile import MWebProfilePage
from playwright.sync_api import Page
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log

@allure.story("mweb-profile分享验证")
class TestMwebProfileShare:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.mweb_wangyue]

    @allure.title("mweb-profile分享验证")
    @pytest.mark.time_banner
    def test_100577_mweb_profile_share(self,phone_page: dict,h5_autotest_header,h5_open_and_close_trace):
        """
        【100577】 social-分享-profile分享流程验证
        测试步骤：
        1. 打开account页面 /account
        2. 点击头像进入profile页
        3. 点击分享按钮,拉起分享pop
        4. 点击复制链接按钮
        5. 复制链接后pop自动关闭
        """
        p : Page =phone_page.get("page")
        c = phone_page.get("context")
        # 创建页面对象
        profile_page = MWebProfilePage(p, h5_autotest_header, browser_context=c)

        with allure.step("步骤1.打开account页"):
            p.goto(TEST_URL + "/account")
            p.wait_for_timeout(5000)

        with allure.step("步骤2.点击头像进入profile页"):
            try:
                # 等待并点击头像
                p.get_by_test_id("wid-account-my-profile-avatar").click()
                p.wait_for_timeout(5000)
                p.wait_for_url("/social/user*", timeout=5000)

                # 等待页面跳转
                p.wait_for_url("https://www.sayweee.com/en/social/user*", timeout=5000)
                # 验证URL是否正确
                current_url = p.url
                if not current_url.startswith("https://www.sayweee.com/en/social/user"):
                    log.error(f"未成功进入profile页, 当前路径: {current_url}")
                    pytest.fail(f"未成功进入profile页, 当前路径: {current_url}")
                else:
                    log.info("成功进入profile页")

            except Exception as e:
                log.error(f"进入profile页失败: 当前路径: {current_url}")
                pytest.fail(f"进入profile页失败:当前路径: {current_url}")

        with allure.step("步骤3：点击分享按钮弹出popup"):
            try:
                profile_page.m_click_share()
                p.wait_for_timeout(10000)
                log.info("已点击分享按钮")

            except Exception as e:
                log.error(f"分享按钮操作失败: {str(e)}")
                pytest.fail(f"分享按钮操作失败: {str(e)}")

        with allure.step("步骤4：点击复制链接按钮"):
            try:
                profile_page.m_click_copylink()
                log.info("已点击复制链接按钮")
                p.wait_for_timeout(2000)  # 等待操作完成

            except Exception as e:
                log.error(f"点击复制链接按钮失败: {str(e)}")
                pytest.fail(f"点击复制链接按钮失败: {str(e)}")

        with allure.step("步骤5：验证分享是否成功"):
            try:
                if profile_page.page.get_by_test_id("wid-share-popup").is_visible():
                    log.error("分享弹窗未关闭 - 分享失败")
                    pytest.fail("分享弹窗未关闭 - 分享失败")
                else:
                    log.info("分享弹窗已关闭 - 分享成功")

            except Exception as e:
                log.error(f"验证分享结果失败: {str(e)}")
                pytest.fail(f"验证分享结果失败: {str(e)}")