# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2025/8/12
@Software       :  PyCharm
------------------------------------
"""
import allure
import pytest


from src.Mweb.EC.mweb_pages.mweb_social_page.mweb_page_profile import MWebProfilePage
from playwright.sync_api import Page
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
@allure.story("mweb-验证T1/T2/T3级别profile页面星星支持点击")
class TestMwebTierTagClick:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.mweb_wangyue]

    @allure.title("mweb-验证T1/T2/T3级别profile页面星星支持点击")
    @pytest.mark.profile
    def test_100870_mweb_tier_tag_verify(self,wangyue_phone_page: dict,wangyue_h5_autotest_header):
        """
        【100577】 social-验证T1/T2/T3级别profile页面星星支持点击
        测试步骤：
        1. 打开account页面 /account
        2. 点击头像进入profile页
        3. 点击tier tag跳转到tier详情页
        """
        p : Page =wangyue_phone_page.get("page")
        c = wangyue_phone_page.get("context")
        # 创建页面对象
        profile_page = MWebProfilePage(p, wangyue_h5_autotest_header, browser_context=c)

        with allure.step("步骤1.打开account页"):
            p.goto(TEST_URL + "/account")
            p.wait_for_timeout(5000)

        with allure.step("步骤2.点击头像进入profile页"):
            try:
                # 等待并点击头像
                p.get_by_test_id("wid-account-my-profile-avatar").click()
                p.wait_for_timeout(5000)

                # 等待页面跳转
                p.wait_for_url("https://www.sayweee.com/en/social/user*", timeout=5000)

                # 验证URL是否正确
                current_url = p.url
                if not current_url.startswith("https://www.sayweee.com/en/social/user"):
                    log.error(f"未成功进入profile页, 当前URL: {current_url}")
                    pytest.fail(f"未成功进入profile页, 当前URL: {current_url}")
                else:
                    log.info("成功进入profile页")

            except Exception as e:
                log.error(f"进入profile页失败")
                pytest.fail(f"进入profile页失败")

        with allure.step("步骤3：点击tier tag跳转到tier详情页"):
            try:
                profile_page.m_verify_tier_badge_visibility()
                log.info("已点击tier tag")
                p.wait_for_timeout(10000)
                current_url = p.url
                if not current_url.startswith("https://www.sayweee.com/en/social/user/tier"):
                    log.error(f"未成功进入用户等级页面, 当前URL: {current_url}")
                    pytest.fail(f"未成功进入用户等级页面, 当前URL: {current_url}")
                else:
                    log.info("成功进入用户等级页面")

            except Exception as e:
                log.error(f"tier tag点击失败")
                pytest.fail(f"tier tag点击失败")
