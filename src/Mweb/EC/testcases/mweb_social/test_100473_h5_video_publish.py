# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2025/8/11
@Software       :  PyCharm
------------------------------------
"""
import allure
import pytest

from src.Mweb.EC.mweb_pages.mweb_social_page.mweb_page_profile import MWebProfilePage
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from playwright.sync_api import Page, TimeoutError
@allure.story("mweb-发布视频验证")
class TestMwebPublishVideo:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.mweb_wangyue]

    @allure.title("mweb-发布视频验证")
    @pytest.mark.profile
    def test_100476_mweb_video_publish(self,phone_page: dict,h5_autotest_header,h5_open_and_close_trace):
        """
        【100577】 social-视频发布流程验证
        测试步骤：
        1. 打开account页面 /account
        2. 点击头像进入profile页
        3. 点击发布按钮,拉起pop
        4. 点击app下载按钮到下载页
        """
        p : Page =phone_page.get("page")
        c = phone_page.get("context")
        # 创建页面对象
        profile_page = MWebProfilePage(p, h5_autotest_header, browser_context=c)

        with allure.step("步骤1.打开account页"):
            p.goto(TEST_URL + "/account")
            p.wait_for_timeout(5000)

        with allure.step("步骤2.点击头像进入profile页"):
            try:
                # 等待并点击头像
                p.get_by_test_id("wid-account-my-profile-avatar").click()
                p.wait_for_timeout(5000)

                # 等待页面跳转
                p.wait_for_url("https://www.sayweee.com/en/social/user*", timeout=5000)

                # 验证URL是否正确
                current_url = p.url
                if not current_url.startswith("https://www.sayweee.com/en/social/user"):
                    log.error(f"未成功进入profile页, 当前URL: {current_url}")
                    pytest.fail(f"未成功进入profile页, 当前URL: {current_url}")
                else:
                    log.info("成功进入profile页")

            except Exception as e:
                log.error(f"进入profile页失败: {str(e)}")
                pytest.fail(f"进入profile页失败: {str(e)}")

        with allure.step("步骤3：点击发布按钮,拉起pop"):
            try:
                profile_page.m_publish_click()
                p.wait_for_timeout(5000)
                log.info("已点击publish按钮")

            except Exception as e:
                log.error(f"发布按钮操作失败: {str(e)}")
                pytest.fail(f"发布按钮操作失败: {str(e)}")

        with allure.step("步骤4：点击app下载按钮到下载页"):
            try:
                # 记录当前页面
                original_page = p
                # 使用事件监听等待新页面
                with c.expect_page() as new_page_info:
                    profile_page.m_video_upload_notice()
                    log.info("已点击app下载按钮")
                # 获取新页面
                new_page = new_page_info.value
                # 等待新页面加载完成
                new_page.wait_for_load_state("networkidle", timeout=15000)
                # 验证URL
                new_page_url = new_page.url
                log.info(f"新标签页URL: {new_page_url}")

                if new_page_url.startswith("https://www.sayweee.com/mgp/download-app"):
                    log.info("成功打开下载页")
                else:
                    log.error(f"新标签页URL不符合预期: {new_page_url}")

            except TimeoutError:
                log.error("等待新标签页打开超时")
                pytest.fail("点击下载按钮后未在指定时间内打开新标签页")


            except Exception as e:
                log.error(f"app下载按钮失败: {str(e)}")
                pytest.fail(f"app下载按钮失败: {str(e)}")
