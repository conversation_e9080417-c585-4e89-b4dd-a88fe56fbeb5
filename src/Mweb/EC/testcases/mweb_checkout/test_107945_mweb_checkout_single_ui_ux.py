"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_107945_mweb_checkout_single_ui_ux.py
@Description    :  
@CreateTime     :  2025/3/25 13:59
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/3/25 13:59
"""
from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_category.mweb_page_category import MWebCategorypage
import allure
import pytest
from playwright.sync_api import Page, expect

from src.Mweb.EC.mweb_pages.mweb_page_checkout.mweb_page_checkout import MWebPageCheckout
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart


@allure.epic("H5结算流程测试")
@allure.feature("信用卡支付流程")
class TestCheckoutSingleUiUx:

    @allure.title("H5结算生鲜订单")
    @pytest.mark.smoke
    def test_107945_checkout_single_ui_ux(self, phone_page: dict, h5_autotest_header):
        """
        测试步骤：
        1. 清空购物车
        2. 从分类页加购商品
        3. 进入购物车
        4. 点击结算按钮
        5. 选择配送地址
        6. 选择配送时间
        7. 选择信用卡支付
        8. 提交订单
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 清空购物车
        empty_cart(h5_autotest_header)

        # 2. 使用封装方法加购Local Delivery商品

        with allure.step("使用封装方法加购Local Delivery商品"):
            # 创建分类页面对象
            category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="/category/sale")
            p.wait_for_timeout(2000)

            # 检查筛选按钮是否存在
            filter_button = p.get_by_test_id("btn-sub-category-filter")
            log.info(f"筛选按钮是否可见: {filter_button.is_visible(timeout=1000)}")

            # 检查Local Delivery元素选择器
            # log.info(f"Local Delivery元素选择器: {mweb_category_ele.ele_local_delivery}")

            # 尝试直接使用XPath定位Local Delivery选项
            local_filter_id = p.locator(mweb_category_ele.ele_local_delivery_xpath)

            # 调用封装方法加购Local Delivery商品
            added_count = category_page.add_products_from_home_by_filter(
                filter_name="Local Delivery",
                filter_id=local_filter_id,
                count=2,  # 加购2个商品
            )
            p.wait_for_timeout(5000)  # 增加等待时间
            log.info(f"成功加购{added_count}个商品")
            assert added_count > 0, "未能成功加购商品"

            # 3. 进入购物车页面
        with allure.step("进入购物车页面"):
            # 创建购物车页面对象
            cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
            p.wait_for_timeout(2000)

            # 关闭可能出现的广告弹窗
            if p.locator("//img[contains(@aria-label, 'close button')]").all():
                p.locator("//img[contains(@aria-label, 'close button')]").click()
                log.info("关闭广告弹窗")

        # with allure.step("从分类页加购商品"):
        #     try:
        #         # 进入分类页
        #         category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c)
        #         # 添加商品到购物车
        #         category_page.add_to_local_product_cart_from_category()
        #         p.wait_for_timeout(2000)
        #         log.info("商品加购成功")
        #     except Exception as e:
        #         log.error(f"加购商品失败: {str(e)}")
        #         raise

        with allure.step("点击结算按钮"):
            try:
                checkout_button = p.get_by_test_id("btn-checkout")
                expect(checkout_button).to_be_visible()
                checkout_button.click()
                p.wait_for_timeout(2000)
                log.info("成功点击结算按钮")
            except Exception as e:
                log.error(f"点击结算按钮失败: {str(e)}")
                raise

        # 5. 验证结算页面各功能
        with allure.step("验证结算页面各功能"):
            try:
                # 创建结算页面对象
                checkout_page = MWebPageCheckout(p, h5_autotest_header, browser_context=c)
                p.wait_for_timeout(2000)

                # 验证结算页面功能
                result = checkout_page.verify_checkout_page_features()

                # 记录验证结果
                for feature, details in result["details"].items():
                    if isinstance(details, dict) and "success" in details:
                        assert details["success"], f"功能 {feature} 验证失败: {details.get('reason', '未知原因')}"

                log.info("结算页面功能验证完成")
            except Exception as e:
                log.error(f"验证结算页面功能失败: {str(e)}")
                raise