# PDP 店铺推荐模块自动化测试实施计划

## 项目概述

本文档详细描述了为商品详情页（PDP）店铺推荐模块实施自动化测试的完整计划，包括元素定义、页面方法封装和测试用例编写。

## 1. 元素定义 (dweb_pdp_ele.py)

### 1.1 需要添加的元素定义

```python
# 店铺推荐模块
ele_pdp_same_vendor_card = "//div[@data-testid='wid-pdp-same-vendor-card']"
# 店铺推荐模块标题
ele_pdp_same_vendor_title = ele_pdp_same_vendor_card + "//h3[text()='店铺推荐']"
# 店铺推荐商品卡片容器
ele_pdp_same_vendor_product_card = ele_pdp_same_vendor_card + "//a[@data-testid='wid-product-card-container']"
# 店铺推荐商品名称
ele_pdp_same_vendor_product_name = ele_pdp_same_vendor_product_card + "//div[@data-testid='wid-product-card-title']"
# 店铺推荐商品价格
ele_pdp_same_vendor_product_price = ele_pdp_same_vendor_product_card + "//div[@data-testid='wid-product-card-price']"
# 店铺推荐收藏按钮
ele_pdp_same_vendor_favorite_btn = ele_pdp_same_vendor_card + "//button[@data-testid='btn-favorite']"
# 店铺推荐加购按钮
ele_pdp_same_vendor_atc_btn = ele_pdp_same_vendor_card + "//div[@data-testid='btn-atc-plus']"
# 店铺推荐轮播下一页按钮
ele_pdp_same_vendor_next_btn = ele_pdp_same_vendor_card + "//button[contains(@class,'absolute')]//span[text()='Next slide']/.."
```

### 1.2 元素定义说明

- **模块化设计**：所有子元素都基于主容器元素构建
- **XPath选择器**：使用XPath确保元素定位的准确性
- **层级关系**：遵循HTML结构的层级关系
- **可维护性**：便于后续维护和扩展

## 2. 页面方法封装 (dweb_page_pdp.py)

### 2.1 主要方法

#### 2.1.1 goto_pdp_and_check_same_vendor()
```python
def goto_pdp_and_check_same_vendor(self, pdp_url):
    """
    此方法包含以下功能：
    1. 进入pdp页面
    2. 校验页面基本元素
    3. 校验店铺推荐模块
    4. 验证店铺推荐商品信息（商品名、价格）
    5. 加购店铺推荐商品
    """
```

#### 2.1.2 _check_same_vendor_module()
```python
def _check_same_vendor_module(self):
    """
    校验店铺推荐模块元素
    - 校验模块可见性
    - 校验标题存在
    - 校验商品卡片
    - 验证商品基本信息
    """
```

#### 2.1.3 _add_same_vendor_products_to_cart()
```python
def _add_same_vendor_products_to_cart(self):
    """
    加购店铺推荐商品
    - 获取加购按钮
    - 处理hover交互
    - 执行加购操作
    """
```

### 2.2 方法设计原则

- **单一职责**：每个方法只负责一个特定功能
- **可复用性**：方法可以在不同测试场景中复用
- **错误处理**：包含适当的异常处理和断言
- **日志记录**：记录关键操作和验证结果

## 3. 测试用例设计 (test_pdp_same_vendor_ui_ux.py)

### 3.1 测试用例结构

```python
@allure.story("【PDP】店铺推荐模块UI/UX验证")
class TestDWebPDPSameVendorUIUX:
    pytestmark = [pytest.mark.pcpdp, pytest.mark.dweb_regression, pytest.mark.transaction]
```

### 3.2 测试步骤

1. **页面访问**：访问指定商品PDP页面
2. **基础校验**：校验页面基本元素加载
3. **模块定位**：滚动到店铺推荐模块
4. **元素校验**：验证模块内各元素存在性
5. **信息验证**：验证商品名称、价格格式
6. **交互测试**：测试收藏和加购功能
7. **轮播测试**：测试商品轮播功能

### 3.3 验证点

#### 3.3.1 UI验证
- 店铺推荐模块可见性
- 模块标题正确显示
- 商品卡片布局正常
- 商品图片加载完成

#### 3.3.2 数据验证
- 商品名称非空
- 价格格式正确（以$开头）
- 商品信息完整性

#### 3.3.3 交互验证
- 收藏按钮hover显示
- 加购按钮点击响应
- 轮播功能正常工作

## 4. 测试数据

### 4.1 测试URL
- 主要测试页面：`/product/SK-II-Skinpower-Airy-Milky-Lotion/2038708`
- 备用测试页面：可根据需要添加其他商品页面

### 4.2 预期结果
- 店铺推荐模块正常显示
- 至少显示1个推荐商品
- 商品信息格式正确
- 交互功能正常

## 5. 实施计划

### 5.1 开发阶段
1. **元素定义**：在 `dweb_pdp_ele.py` 中添加元素定义
2. **方法封装**：在 `dweb_page_pdp.py` 中添加页面方法
3. **测试编写**：创建测试用例文件
4. **本地测试**：在本地环境验证功能

### 5.2 测试阶段
1. **单元测试**：验证各个方法功能
2. **集成测试**：验证整体流程
3. **回归测试**：确保不影响现有功能
4. **性能测试**：验证页面加载和交互性能

### 5.3 部署阶段
1. **代码审查**：团队代码审查
2. **测试环境部署**：部署到测试环境
3. **生产环境部署**：部署到生产环境
4. **监控验证**：监控测试执行情况

## 6. 风险评估

### 6.1 技术风险
- **元素定位**：页面结构变化可能导致元素定位失败
- **异步加载**：商品数据异步加载可能影响测试稳定性
- **浏览器兼容**：不同浏览器可能存在兼容性问题

### 6.2 缓解措施
- **多重定位策略**：使用多种定位方式提高稳定性
- **等待机制**：合理使用显式等待和隐式等待
- **错误重试**：实现自动重试机制
- **环境隔离**：使用独立的测试环境

## 7. 成功标准

### 7.1 功能标准
- 所有元素定义准确有效
- 页面方法功能完整
- 测试用例覆盖全面
- 测试执行稳定可靠

### 7.2 质量标准
- 代码符合团队规范
- 测试通过率 > 95%
- 执行时间合理
- 维护成本可控

## 8. 后续维护

### 8.1 定期维护
- 定期检查元素定位有效性
- 更新测试数据和预期结果
- 优化测试执行效率
- 扩展测试覆盖范围

### 8.2 文档更新
- 及时更新技术文档
- 记录问题和解决方案
- 分享最佳实践
- 培训团队成员

---

**文档版本**：v1.0  
**创建日期**：2025-06-27  
**最后更新**：2025-06-27  
**负责人**：开发团队
