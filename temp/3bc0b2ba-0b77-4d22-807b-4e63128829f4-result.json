{"name": "【H5-活动页】价格过滤器UI/UX验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'is_visible'", "trace": "self = <src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux.TestMWebPromotionFilterUIUX object at 0x0000027E938EFF50>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Loc..., 'page': <Page url='https://www.sayweee.com/en/promotion/free-gift/landing?ps_id=13143&pin_id=2155604&joinEnki=true'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...ZnmPvuF57AcDmrikTI6ybD6t0QjUJFzG-AzqNHwS4fjj_88b8zT9fXslOolTLQB41iR6P0t_TaInyumkO5ACYyWHkW2MUlypdbuzNr453AR2yW7o', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"【H5-活动页】价格过滤器UI/UX验证\")\n    def test_mweb_promotion_filter_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        H5活动页价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 验证活动标题和规则\n        4. 检查商品数量\n        5. 如果商品超过20个，验证过滤器存在\n        6. 测试价格过滤器功能\n        7. 验证商品信息展示\n        \"\"\"\n        p: Page = phone_page.get(\"page\")\n        c = phone_page.get(\"context\")\n    \n        # 1. 直接进入活动页面\n        promotion_page = MWebPromotionPage(p, h5_autotest_header, browser_context=c,\n                                         page_url=\"/promotion/free-gift/landing?ps_id=13143&pin_id=2155604\")\n    \n        p.wait_for_timeout(5000)\n    \n        # 关闭可能的弹窗\n        if p.locator(\"//button[contains(text(), 'Continue')]\").all():\n            p.locator(\"//button[contains(text(), 'Continue')]\").click()\n    \n        print(\"活动页面加载完成\")\n    \n        # 2. 校验活动标题\n        promotion_title =promotion_page.FE.ele(mweb_promotion_page_ele.ele_promotion_drawer_title)\n>       assert promotion_title.is_visible(), \"活动标题不可见\"\nE       AttributeError: 'NoneType' object has no attribute 'is_visible'\n\nsrc\\Mweb\\EC\\testcases\\mweb_promotion\\test_110551_mweb_promotion_detail_ui_ux.py:44: AttributeError"}, "description": "\n        H5活动页价格过滤器UI/UX验证\n        测试步骤：\n        1. 访问活动页面\n        2. 校验页面基本元素\n        3. 验证活动标题和规则\n        4. 检查商品数量\n        5. 如果商品超过20个，验证过滤器存在\n        6. 测试价格过滤器功能\n        7. 验证商品信息展示\n        ", "start": 1754901136072, "stop": 1754901155539, "uuid": "29bb623b-b655-491b-a493-acf981e5245c", "historyId": "f952be12ecba99ee55a0fda8c907b0b5", "testCaseId": "f952be12ecba99ee55a0fda8c907b0b5", "fullName": "src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux.TestMWebPromotionFilterUIUX#test_mweb_promotion_filter_ui_ux", "labels": [{"name": "story", "value": "【H5-活动页】价格过滤器UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "h5promotion"}, {"name": "tag", "value": "mweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_promotion"}, {"name": "suite", "value": "test_110551_mweb_promotion_detail_ui_ux"}, {"name": "subSuite", "value": "TestMWebPromotionFilterUIUX"}, {"name": "host", "value": "SHLAP10349"}, {"name": "thread", "value": "18344-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_promotion.test_110551_mweb_promotion_detail_ui_ux"}]}